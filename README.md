Product Requirements Document (PRD)
Cashback E-commerce Platform
1. Product Overview
1.1 Purpose
A modern shopping price and savings platform that connects consumers with leading brands while offering cashback rewards on purchases. The platform aims to enhance the shopping experience by providing transparent cashback offers and streamlined claim processes that are offered by the brands who are refunding the end users who purchase the products from their authorised resellers..

1.2 Target Audience
Price-conscious consumers
Regular online shoppers
Brand-loyal customers
Deal seekers
Tech-savvy users aged 18-45

## 2. Technical Architecture

### Search Suggestions URL Patterns

The search suggestions component now supports type-aware navigation with the following URL patterns:

#### Brand Suggestions
- **Pattern**: `/search?q=&brand=<brand-slug>`
- **Example**: `/search?q=&brand=samsung`
- **Behavior**: Filters search results to show only products from the selected brand

#### Category Suggestions  
- **Pattern**: `/search?q=&category=<category-slug>`
- **Example**: `/search?q=&category=electronics`
- **Behavior**: Filters search results to show only products from the selected category

#### Product Suggestions
- **Pattern**: `/products/<product-slug>`
- **Example**: `/products/samsung-galaxy-s23`
- **Behavior**: Navigates directly to the product detail page (PDP)

#### Analytics Tracking
All suggestion clicks are tracked with the following data:
- `suggestionType`: 'brand' | 'category' | 'product'
- `suggestionId`: Database ID of the suggestion
- `destinationUrl`: Final navigation URL
- `query`: Original search query

### URL State Management with usePagination

The application employs a centralized approach to URL state management, with the `usePagination` hook serving as the single source of truth for constructing and modifying paginated URLs. This pattern ensures consistency, maintainability, and proper handling of transient parameters.

#### Key Principles

1. **Single Responsibility**: The `usePagination` hook is the only utility allowed to construct paginated URLs throughout the application.
2. **Centralized Parameter Handling**: All URL parameter logic, including transient parameters like `scroll`, is managed within this hook.
3. **Type Safety**: Strong TypeScript types ensure all URL parameters are properly typed and validated.

#### Implementation Details

```typescript
// Example implementation in usePagination.ts
interface PaginationParams {
  page: number;
  pageSize: number;
  // Other filter parameters
}

interface TransientParams {
  scroll?: boolean;
  // Other transient parameters
}

function usePagination(initialParams: PaginationParams) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Update URL with new parameters
  const updateUrl = (params: Partial<PaginationParams>, transient?: TransientParams) => {
    const newParams = new URLSearchParams(searchParams.toString());
    
    // Handle pagination parameters
    if (params.page !== undefined) {
      params.page === 1 
        ? newParams.delete('page')
        : newParams.set('page', params.page.toString());
    }
    
    // Handle transient parameters
    if (transient?.scroll === false) {
      newParams.set('scroll', 'false');
    } else {
      newParams.delete('scroll');
    }
    
    // Update URL without scrolling
    router.push(`?${newParams.toString()}`, { scroll: false });
  };
  
  // Public API
  return {
    currentPage: parseInt(searchParams.get('page') || '1', 10),
    goToPage: (page: number) => updateUrl({ page }),
    updateFilters: (filters: Partial<PaginationParams>) => updateUrl({ ...filters, page: 1 }),
  };
}
```

#### Usage Guidelines

1. **Always use the hook's methods** for navigation:
   ```typescript
   const { goToPage, updateFilters } = usePagination();
   
   // Good - Uses centralized URL construction
   <button onClick={() => goToPage(2)}>Next Page</button>
   
   // Bad - Avoid direct router.push() for pagination
   <button onClick={() => router.push('?page=2')}>Next Page (avoid this)</button>
   ```

2. **Handling Scroll Behavior**:
   - The hook automatically manages the `scroll` parameter
   - Use `updateUrl({}, { scroll: false })` when you need to prevent scroll reset
   - The `ProductsContent` component handles scroll restoration based on this parameter

3. **Adding New Parameters**:
   - Add new parameters to the `PaginationParams` interface
   - Update the `updateUrl` function to handle the new parameter
   - Document the new parameter in this README

#### Benefits

- **Consistency**: All URL construction follows the same pattern
- **Maintainability**: Changes to URL structure are made in one place
- **Reliability**: Reduces bugs from inconsistent parameter handling
- **SEO Friendly**: Ensures clean, canonical URLs for search engines

## 3. Core Features

### 3.1 Homepage
Purpose: Showcase featured offers and provide easy access to key platform features

Components:

Hero section with search functionality
Featured cashback offers carousel
Popular brands showcase
How it works section
Trust indicators
Features grid
Key Metrics:

Search utilization rate
Carousel engagement
Brand click-through rates
Feature section visibility
2.2 Product Discovery
Purpose: Enable users to find relevant cashback offers efficiently

Features:

Advanced search functionality
Category browsing
Brand filtering
Price range filters
Sorting options
Featured deals section
Success Criteria:

Search success rate
Category navigation depth
Filter usage metrics
Time to find products
2.3 Brand Integration
Purpose: Showcase partner brands and their offers

Features:

Brand directory
Individual brand pages
Brand-specific promotions
Logo showcase
Brand description and details
Metrics:

Brand page visits
Brand-specific conversion rates
Logo click-through rates
2.4 Cashback System
Purpose: Provide transparent and easy-to-understand cashback rewards

Features:

Clear cashback amount display
Limited-time offer indicators
Claim submission process
Status tracking
Payment processing
Success Metrics:

Claim submission rate
Processing time
User satisfaction
Payment success rate
3. Technical Requirements
3.1 Frontend Architecture
React with TypeScript
Vite for build system
Tailwind CSS for styling
shadcn/ui component library
Responsive design principles
SEO optimization
3.2 Backend Services (Supabase)
User authentication
Database management
Real-time updates
File storage
API endpoints
3.3 Database Schema
Core Tables:

Products
Brands
Promotions
Claims
Users
Categories
3.4 Performance Requirements
Page load time < 3 seconds
Time to interactive < 4 seconds
First contentful paint < 2 seconds
Core Web Vitals compliance
4. User Experience
4.1 Navigation
Clear hierarchical structure
Breadcrumb navigation
Mobile-friendly menu
Quick access to key features
4.2 Search Experience
Real-time suggestions
Relevant results
Filter options
Sort capabilities
4.3 Product Pages
Clear product information
High-quality images
Cashback details
Terms and conditions
Claim process
5. Security Requirements
5.1 User Data
Secure authentication
Data encryption
Privacy compliance
GDPR adherence
5.2 Transaction Security
Secure payment processing
Claim verification
Fraud prevention
Audit trails
6. Internationalization
6.1 Language Support
Multi-language interface
Regional content
Currency conversion
Date/time localization
7. Analytics & Reporting
7.1 User Analytics
Behavior tracking
Conversion metrics
Search analytics
User journey mapping
7.2 Business Metrics
Cashback claims
Brand performance
Product popularity
User engagement
8. Future Considerations
8.1 Scalability
Infrastructure scaling
User base growth
Brand partnerships
Feature expansion
8.2 Integration Opportunities
Payment providers
Marketing platforms
Social media
Email systems
Would you like me to:

Elaborate on any specific section?
Add more detailed metrics?
Expand on technical requirements?
Include more future considerations?
Ask Lovable...
Attach
Select
Chat only
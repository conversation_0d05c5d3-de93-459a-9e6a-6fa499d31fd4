// [DISABLED BY GITHUB COPILOT JULY 2025]
// This Babel config is commented out to allow Next.js to use SWC for compilation.
// Reason: Next.js 13+ and next/font require SWC, and a custom Babel config disables SWC, causing build errors with import attributes and next/font.
// If you need custom transforms, use next.config.js `compiler` options instead.
/*
module.exports = {
  presets: [
    ['@babel/preset-env', { targets: { node: 'current' }, modules: 'auto' }],
    '@babel/preset-typescript',
  ],
};
*/

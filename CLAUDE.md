# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Development
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run clean` - Remove .next directory
- `npm run clean:build` - Clean and build
- `npm run preview` - Build and start production server

### Testing
- `npm run test` - Run all Jest tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run test:camelcase` - Run camelCase validation tests
- `npm run test:api` - Run API tests
- `npm run test:metadata` - Run metadata tests

### Linting and Quality
- `npm run lint` - Run ESLint
- `npx eslint --fix` - Auto-fix ESLint issues

### SEO and Performance
- `npm run seo:test` - Run SEO tests
- `npm run audit:seo` - Run Lighthouse SEO audit
- `npm run audit:performance` - Run Lighthouse performance audit
- `npm run performance:check` - Check Web Vitals

## Project Architecture

### Technology Stack
- **Framework**: Next.js 15.3.5 with App Router (Security upgraded July 2025)
- **Database**: Supabase (PostgreSQL)
- **UI Framework**: React 19.1.0 with TypeScript (Stable release upgrade July 2025)
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React Query for server state
- **Authentication**: Supabase Auth
- **Testing**: Jest with React Testing Library
- **Deployment**: AWS Amplify with Cloudflare security

### Key Architectural Patterns

#### 1. Data Layer Architecture
The application uses a centralized data layer pattern located in `src/lib/data/`:
- **Server-side data functions**: All database operations use server-side functions with proper caching
- **Type safety**: Comprehensive TypeScript interfaces in `src/lib/data/types.ts`
- **Caching strategy**: Redis-like caching with `createCachedFunction` utility
- **Supabase client**: Server-side read-only client for security (`src/lib/supabase/server.ts`)

#### 2. URL State Management with usePagination
The `usePagination` hook (`src/hooks/usePagination.ts`) is the **single source of truth** for all paginated URLs:
- **Centralized parameter handling**: All URL parameters including transient ones like `scroll`
- **Page-specific hooks**: `useProductsPagination`, `useRetailersPagination`, `useBrandsPagination`
- **Clean URLs**: Page 1 doesn't show in URL, maintains SEO-friendly structure
- **Browser navigation**: Full support for back/forward navigation

#### 3. Search Architecture
Comprehensive search system with multiple components:
- **Search API**: `/api/search/route.ts` with rate limiting and validation
- **Search data layer**: `src/lib/data/search.ts` with caching and transformation
- **Search components**: `SearchBar`, `SearchSuggestions` in `src/components/search/`
- **Search suggestions**: Auto-complete with brand and product suggestions

#### 4. Security Implementation
Multi-layered security approach:
- **HTTP Security Headers**: CSP, HSTS, XSS protection in `next.config.js`
- **Rate Limiting**: API route protection with `src/lib/rateLimiter.ts`
- **Input Validation**: Zod schemas in `src/lib/validation/schemas.ts`
- **DOMPurify**: XSS prevention with isomorphic-dompurify
- **Cloudflare Turnstile**: Bot protection and CAPTCHA

#### 5. Performance Optimization
- **Image optimization**: Next.js Image with multiple remote patterns
- **Caching layers**: Multiple cache durations (SHORT, MEDIUM, EXTENDED)
- **Web Vitals monitoring**: Real-time performance tracking
- **Code splitting**: Webpack optimization for vendor chunks
- **SEO optimization**: Structured data, sitemaps, metadata utils

### Directory Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes with rate limiting
│   ├── brands/            # Brand listing and detail pages
│   ├── products/          # Product listing and detail pages
│   ├── search/            # Search functionality
│   └── layout.tsx         # Root layout with providers
├── components/            # Reusable UI components
│   ├── layout/           # Header, footer, SEO components
│   ├── pages/            # Page-specific client components
│   ├── search/           # Search-related components
│   ├── ui/               # shadcn/ui components
│   └── debug/            # Development debugging tools
├── lib/                   # Core utilities and services
│   ├── data/             # Data layer (products, brands, search)
│   ├── supabase/         # Database clients
│   ├── validation/       # Zod schemas
│   └── security/         # Security utilities
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
└── utils/                # Utility functions
```

### Database Schema
Core entities with full-text search capabilities:
- **Products**: With brand, category, promotion relationships
- **Brands**: With search vector and featured status
- **Retailers**: With claim periods and offer management
- **Promotions**: With time-based validation and cashback rules
- **Product_Retailer_Offers**: Price comparison and stock status

### Key Development Patterns

#### 1. Component Architecture
- **Client components**: Use 'use client' directive, located in `src/components/pages/`
- **Server components**: Default for page.tsx files, handle data fetching
- **UI components**: shadcn/ui based, in `src/components/ui/`
- **Layout components**: Header, footer, SEO in `src/components/layout/`

#### 2. Data Fetching Pattern
```typescript
// Always use server-side data functions
import { getProducts } from '@/lib/data/products'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'

// In server components
const supabase = createServerSupabaseReadOnlyClient()
const products = await getProducts(supabase, filters, page, limit)
```

#### 3. URL Management Pattern
```typescript
// Always use pagination hooks for URL state
const { currentPage, goToPage, updateFilters } = useProductsPagination()

// For navigation
goToPage(2) // Handles URL construction and scroll behavior
updateFilters({ brand: 'samsung' }) // Resets to page 1
```

#### 4. Error Handling
- **API routes**: Use try-catch with structured error responses
- **Components**: Error boundaries and fallback UI
- **Validation**: Zod schema validation with detailed error messages
- **Monitoring**: Debug logging in development mode

### Environment Variables
Key environment variables (see `docs/ENVIRONMENT_VARIABLES.md`):
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Public Supabase key
- `SUPABASE_SERVICE_ROLE_KEY` - Server-side Supabase key
- `NEXT_PUBLIC_TURNSTILE_SITE_KEY` - Cloudflare Turnstile
- `TURNSTILE_SECRET_KEY` - Turnstile server validation

### Testing Strategy
- **Unit tests**: Jest with React Testing Library
- **Integration tests**: API route testing with Supabase
- **End-to-end tests**: Playwright for critical user flows
- **Security tests**: XSS, rate limiting, input validation tests
- **Performance tests**: Web Vitals and Lighthouse audits

### Deployment and Infrastructure
- **Hosting**: Our production build is deployed via AWS Amplify Console
- **Database**: Supabase with migrations in `supabase/migrations/`
- **CDN**: Cloudflare for security headers and bot protection
- **Images**: Next.js Image optimization with remote patterns
- **Monitoring**: Web Vitals and error tracking
- # INFRASTRUCTURE:
Our production build is deployed via AWS Amplify Console. A Cloudflare proxy terminates TLS and routes traffic to the Amplify distribution. No Vercel services are used in this pipeline. The refereces to Vercel are incorrect and out of date. 

## Important Notes

### Security Requirements
- Never expose the service role key in client-side code
- Always validate user inputs with Zod schemas
- Use DOMPurify for any dynamic HTML content
- Implement rate limiting on all API routes
- Follow CSP guidelines for script and style sources

### Performance Guidelines
- Use server-side data functions with proper caching
- Implement lazy loading for images and components
- Monitor Web Vitals and maintain good Core Web Vitals scores
- Use appropriate cache headers for API responses

### Development Workflow
1. Run `npm run dev` for development server
2. Use `npm run lint` before committing
3. Run `npm run test` to ensure all tests pass
4. Use `npm run build` to verify production build
5. Check `npm run audit:seo` for SEO compliance

### Common Issues and Solutions
- **Build errors**: Run `npm run clean:build` to clear cache
- **Type errors**: Check `src/lib/data/types.ts` for interface definitions
- **Pagination issues**: Always use the `usePagination` hook family
- **Search not working**: Verify API route and data layer functions
- **Security headers**: Check `next.config.js` CSP configuration

## Memory: Comprehensive Documentation Suite

I have created a complete documentation suite for the Cashback Deals v2 codebase to assist with onboarding and development. Each file provides detailed technical guidance for specific aspects of the system.

### Documentation Files Summary & Links

#### [docs/ARCHITECTURE.md](docs/ARCHITECTURE.md)
**System Architecture & Design Patterns**
- Complete technology stack overview (Next.js 15, React 19, Supabase, TypeScript)
- Detailed architectural patterns including data layer, URL state management, and search system
- Component architecture with server/client component strategies
- Rendering strategies and performance optimization patterns
- Data flow diagrams and system interaction patterns
- - # INFRASTRUCTURE update:
Our production build is deployed via AWS Amplify Console. A Cloudflare proxy terminates TLS and routes traffic to the Amplify distribution. No Vercel services are used in this pipeline. The refereces to Vercel are incorrect and out of date. 

#### [docs/DATA_MODEL.md](docs/DATA_MODEL.md)
**Database Schema & Data Management**
- Complete PostgreSQL schema with all tables and relationships
- ER diagrams and entity definitions
- Caching strategy with Redis-like implementation
- Data transformation patterns and type definitions
- Query optimization and performance tuning strategies

#### [docs/WORKFLOWS.md](docs/WORKFLOWS.md)
**Developer Onboarding & Git Workflows**
- Day-1 setup instructions for new developers
- Complete npm scripts reference and usage
- Git workflow with feature branches and pull request process
- Code review guidelines and quality standards
- Development environment setup and troubleshooting

#### [docs/TESTING.md](docs/TESTING.md)
**Testing Strategy & Implementation**
- Testing pyramid with unit, integration, and E2E tests
- Jest configuration with React Testing Library
- Playwright E2E testing setup and best practices
- Security testing patterns and vulnerability scanning
- Performance testing and Web Vitals monitoring

#### [docs/SECURITY.md](docs/SECURITY.md)
**Security Implementation & Guidelines**
- Multi-layered security architecture (defense in depth)
- Authentication and authorization with Supabase Auth and RLS
- Input validation and XSS prevention with Zod and DOMPurify
- Rate limiting and DoS protection implementation
- Security monitoring, incident response, and audit procedures

#### [docs/CI_CD.md](docs/CI_CD.md)
**Continuous Integration & Deployment**
- GitHub Actions workflows for CI/CD pipeline
- Quality gates including lint, test, security, and build checks
- Vercel deployment configuration and environment management
- Branch strategy and deployment flow
- Monitoring, alerting, and rollback procedures

#### [docs/PERFORMANCE_SEO.md](docs/PERFORMANCE_SEO.md)
**Performance Optimization & SEO**
- Core Web Vitals targets and monitoring implementation
- Performance optimization strategies (caching, image optimization, code splitting)
- SEO implementation with structured data and metadata
- Lighthouse configuration and automated auditing
- Web Vitals monitoring and performance budgets

#### [docs/TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md)
**Common Issues & Solutions**
- Build and development issues with diagnostic steps
- Database and API troubleshooting procedures
- Search and pagination problem resolution
- Security issues and rate limiting problems
- Performance issues and memory leak detection
- Deployment and SSL certificate problems

#### [docs/LIBRARIES_AND_UTILITIES.md](docs/LIBRARIES_AND_UTILITIES.md)
**Dependencies & Internal Utilities**
- Complete reference for all external dependencies with usage patterns
- Internal utility functions and custom hooks documentation
- Package management guidelines and upgrade procedures
- Security scanning and license compliance
- Bundle size monitoring and optimization strategies

### When to Use Each Document

**For Architecture Questions**: Use `ARCHITECTURE.md` for system design, patterns, and component structure
**For Database Work**: Use `DATA_MODEL.md` for schema, relationships, and data operations
**For New Developer Setup**: Use `WORKFLOWS.md` for onboarding and development processes
**For Testing Issues**: Use `TESTING.md` for test setup, patterns, and debugging
**For Security Concerns**: Use `SECURITY.md` for authentication, validation, and security best practices
**For Deployment Issues**: Use `CI_CD.md` for pipeline problems and deployment procedures
**For Performance Problems**: Use `PERFORMANCE_SEO.md` for optimization and monitoring
**For Debugging**: Use `TROUBLESHOOTING.md` for common issues and solutions
**For Dependency Management**: Use `LIBRARIES_AND_UTILITIES.md` for package information and utilities

### Quick Reference for Common Tasks

- **Adding new features**: Start with `ARCHITECTURE.md` → `DATA_MODEL.md` → `TESTING.md`
- **Fixing bugs**: Check `TROUBLESHOOTING.md` first, then relevant technical docs
- **Performance issues**: Use `PERFORMANCE_SEO.md` and `TROUBLESHOOTING.md`
- **Security concerns**: Review `SECURITY.md` and `TESTING.md` security sections
- **Deployment problems**: Check `CI_CD.md` and `TROUBLESHOOTING.md`
- **New developer onboarding**: Follow `WORKFLOWS.md` then review `ARCHITECTURE.md`

## Memory: Security Upgrade - Next.js 15.3.5 + React 19.1.0

**Date:** July 9, 2025  
**Status:** ✅ Successfully Completed

### Major Framework Security Upgrade
Successfully completed comprehensive security upgrade from Next.js 15.1.4 → 15.3.5 and React 19.0.0 → 19.1.0 with all runtime issues resolved and UI functionality maintained.

**Key Changes:**
- **Security patches** applied via Next.js 15.3.5
- **React 19.1.0 stable release** with performance improvements
- **TailwindCSS 4.x compatibility** via @tailwindcss/postcss
- **Enhanced Content Security Policy** for Sentry integration
- **Fixed React 19.1.0 Image component warnings**

**Files Modified:**
- `package.json` - Framework version updates
- `postcss.config.mjs` - TailwindCSS 4.x PostCSS plugin fix
- `tailwind.config.ts` - Fixed darkMode TypeScript syntax
- `next.config.js` - Enhanced CSP for Sentry domains + worker-src
- `src/components/FeaturedProductCard.tsx` - Fixed Image positioning
- `jest.config.js` - Excluded Playwright tests from Jest

**New Infrastructure:**
- `.github/workflows/ci.yml` - CI pipeline with Node 18.x/20.x/22.x matrix
- `amplify.yml` - AWS Amplify deployment configuration

**Verification Results:**
- ✅ UI renders correctly with all sections displaying
- ✅ No CSP violations in console logs
- ✅ Data fetching works (Products, Promotions, Brands, Retailers)
- ✅ Server stable on Next.js 15.3.5 + React 19.1.0
- ✅ All tests pass and build succeeds

**📋 Complete Details:** See [SECURITY_UPGRADE_CHANGELOG.md](SECURITY_UPGRADE_CHANGELOG.md) for comprehensive documentation of all changes, rollback procedures, and security impact analysis.

**Next Actions:**
- Monitor production for any runtime issues
- Plan Node.js 22.x migration before Sep 15, 2025
- Set up automated security scanning for future vulnerabilities
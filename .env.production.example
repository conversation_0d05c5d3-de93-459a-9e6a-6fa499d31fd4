# Production Environment Variables Example
# Copy this file to .env.production and update with actual values

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Email Configuration
EMAIL_SERVER=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Cloudflare Turnstile Configuration
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your-site-key
TURNSTILE_SECRET_KEY=your-secret-key

# Sentry Configuration (PR3)
SENTRY_DSN=https://<EMAIL>/project-id
ENABLE_SENTRY=true
ENABLE_SENTRY_LOCAL=false
SENTRY_TRACES_SAMPLE_RATE=0.01
SENTRY_ENVIRONMENT=production

# IP Allowlist Configuration (PR3)
# IMPORTANT: Start with ENABLE_IP_ALLOWLIST=false for initial deployment
# For localhost development, IP allowlist is disabled by default
ENABLE_IP_ALLOWLIST=false
IP_ALLOWLIST_CIDRS=10.0.0.0/8,**********/12,***********/16,127.0.0.1/32
IP_ALLOWLIST_LOG_VIOLATIONS=true
IP_ALLOWLIST_BLOCK_BY_DEFAULT=true
IP_ALLOWLIST_INCLUDE_DEBUG=false

# Development Note: 
# - IPv4-mapped IPv6 (::ffff:***********) is automatically supported
# - Reserved IP ranges are handled by ipaddr.js
# - Localhost (127.0.0.1, ::1) is always allowed when enabled

# Authentication Configuration
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
PARTNER_SECRET_DEFAULT=your-production-secret-minimum-32-characters
PARTNER_SECRET_TEST_PARTNER=your-test-secret-minimum-32-characters-long
HMAC_TIMESTAMP_WINDOW=300

# Debug Configuration (disable in production)
NEXT_PUBLIC_DEBUG_ENABLED=false
NEXT_PUBLIC_DEBUG_LEVEL=error

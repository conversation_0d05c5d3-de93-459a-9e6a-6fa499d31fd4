# Security Upgrade Report - Next.js 15.3.5 + React 19.1.0

## Framework Core Updates (Security Priority)

| Package | Old Version | New Version | Change Type |
|---------|-------------|-------------|-------------|
| **next** | 15.1.4 | **15.3.5** | 🔒 Security Patch |
| **react** | 19.0.0 | **19.1.0** | 🔄 Minor Update |
| **react-dom** | 19.0.0 | **19.1.0** | 🔄 Minor Update |
| **eslint-config-next** | 15.1.4 | **15.3.5** | 🔄 Minor Update |
| **path-to-regexp** | 1.8.0 | **8.2.0** | ⚠️ Major Update (ReDoS fix) |

## Dependencies Updated

| Package | Old Version | New Version | Change Type |
|---------|-------------|-------------|-------------|
| @babel/plugin-transform-modules-commonjs | ^7.24.7 | ^7.27.1 | 🔄 Minor |
| @babel/plugin-transform-runtime | ^7.24.7 | ^7.28.0 | 🔄 Minor |
| @babel/preset-react | ^7.24.7 | ^7.27.1 | 🔄 Minor |
| @cloudflare/next-on-pages | ^1.13.7 | ^1.13.12 | 🔄 Patch |
| @hookform/resolvers | ^3.10.0 | ^5.1.1 | 🚨 Major |
| @playwright/test | ^1.53.1 | ^1.53.2 | 🔄 Patch |
| @radix-ui/react-accordion | ^1.2.3 | ^1.2.11 | 🔄 Patch |
| @radix-ui/react-collapsible | ^1.1.2 | ^1.1.11 | 🔄 Patch |
| @radix-ui/react-dialog | ^1.1.4 | ^1.1.14 | 🔄 Patch |
| @radix-ui/react-dropdown-menu | ^2.1.4 | ^2.1.15 | 🔄 Patch |
| @radix-ui/react-navigation-menu | ^1.2.3 | ^1.2.13 | 🔄 Patch |
| @radix-ui/react-select | ^2.1.4 | ^2.2.5 | 🔄 Minor |
| @radix-ui/react-slot | ^1.1.1 | ^1.2.3 | 🔄 Minor |
| @supabase/ssr | ^0.5.2 | ^0.6.1 | 🔄 Minor |
| @supabase/supabase-js | ^2.47.13 | ^2.50.4 | 🔄 Minor |
| @tanstack/react-query | ^5.81.5 | ^5.82.0 | 🔄 Patch |
| @tanstack/react-query-devtools | ^5.64.1 | ^5.82.0 | 🔄 Minor |
| @types/dompurify | ^3.0.5 | ^3.2.0 | 🔄 Minor |
| @types/jest | ^29.5.14 | ^30.0.0 | 🚨 Major |
| @types/node | ^20.17.13 | ^24.0.12 | 🚨 Major |
| @types/react | ^19.0.7 | ^19.1.8 | 🔄 Minor |
| @types/react-dom | ^19.0.3 | ^19.1.6 | 🔄 Minor |
| @types/react-query | ^1.2.8 | ^1.2.9 | 🔄 Patch |
| autoprefixer | ^10.4.20 | ^10.4.21 | 🔄 Patch |
| critters | ^0.0.23 | ^0.0.25 | 🔄 Patch |
| dotenv | ^16.4.7 | ^17.2.0 | 🚨 Major |
| eslint | ^8.56.0 | ^9.30.1 | 🚨 Major |
| framer-motion | ^11.18.0 | ^12.23.1 | 🚨 Major |
| graphql | ^16.10.0 | ^16.11.0 | 🔄 Minor |
| jest-environment-jsdom | ^30.0.0 | ^30.0.4 | 🔄 Patch |
| lucide-react | ^0.471.2 | ^0.525.0 | 🔄 Minor |
| next-i18next | ^15.4.1 | ^15.4.2 | 🔄 Patch |
| next-seo | ^6.6.0 | ^6.8.0 | 🔄 Minor |
| nodemailer | ^6.10.0 | **6.10.1** | 🔄 Patch (downgraded from 7.0.5) |
| papaparse | ^5.5.2 | ^5.5.3 | 🔄 Patch |
| react-hook-form | ^7.54.2 | ^7.60.0 | 🔄 Minor |
| react-hot-toast | ^2.5.1 | ^2.5.2 | 🔄 Patch |
| shadcn-ui | ^0.9.4 | ^0.9.5 | 🔄 Patch |
| supabase | ^2.6.8 | ^2.30.4 | 🔄 Minor |
| tailwind-merge | ^2.6.0 | ^3.3.1 | 🚨 Major |
| tailwindcss | ^3.4.1 | ^4.1.11 | 🚨 Major |
| typescript | ^5.7.3 | ^5.8.3 | 🔄 Minor |
| zod | ^4.0.0-beta.20250505T195954 | ^4.0.0 | 🔄 Stable Release |

## Infrastructure Updates

### New Files Created
- `.github/workflows/ci.yml` - CI/CD pipeline with Node 18.x, 20.x, 22.x matrix
- `amplify.yml` - Deployment configuration with Node 20.10.0

### Security Notes
- **Critical**: Next.js 15.3.5 includes security patches
- **Warning**: path-to-regexp updated from 1.8.0 to 8.2.0 (breaking changes possible)
- **Note**: nodemailer downgraded from 7.0.5 to 6.10.1 due to next-auth compatibility

### Node.js Compatibility
- **Current**: Node 22.11.0 ✅
- **CI Matrix**: Testing on Node 18.x, 20.x, 22.x
- **Amplify**: Using Node 20.10.0
- **TODO**: Bump to Node 22.x before September 15, 2025

### Deprecation Warnings
- `@types/react-query@1.2.9`: Stub types definition (react-query provides own types)
- `critters@0.0.25`: Moved to Nuxt team, consider switching to `beasties`
- `@types/dompurify@3.2.0`: Stub types definition (dompurify provides own types)

## Next Steps
1. Run full test suite to identify breaking changes
2. Update code for major version changes (especially ESLint 9.x, TailwindCSS 4.x)
3. Test path-to-regexp upgrade impact
4. Monitor for security vulnerabilities post-upgrade
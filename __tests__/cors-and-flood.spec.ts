/**
 * Tests for PR-5 — CORS Tightening + Automated Security & Flood-Protection Tests
 * 
 * This test suite validates:
 * 1. CORS enforcement blocks unauthorized origins
 * 2. Authentication still works after CORS implementation  
 * 3. Rate limiting functions correctly with CORS
 * 4. All protected routes behave consistently
 */

import request from 'supertest'
import { NextRequest } from 'next/server'
import { GET as catalogGET, OPTIONS as catalogOPTIONS } from '@/app/api/catalog/route'
import { GET as searchMoreGET, OPTIONS as searchMoreOPTIONS } from '@/app/api/search/more/route'
import { GET as productGET, OPTIONS as productOPTIONS } from '@/app/api/products/[id]/route'
import { GET as brandGET, OPTIONS as brandOPTIONS } from '@/app/api/brands/[id]/route'
import { GET as retailerGET, OPTIONS as retailerOPTIONS } from '@/app/api/retailers/[id]/route'

// Test configuration
const ALLOWED_ORIGIN = 'https://cashback-deals.com'
const BLOCKED_ORIGIN = 'https://evil.com'
const DEV_ORIGIN = 'http://localhost:3000'

// Mock environment for testing
const originalEnv = process.env

beforeAll(() => {
  // Enable CORS strict mode for testing
  process.env.ENABLE_CORS_STRICT = 'true'
  process.env.NODE_ENV = 'test'
})

afterAll(() => {
  // Restore original environment
  process.env = originalEnv
})

// Helper to create mock NextRequest
function createMockRequest(
  url: string, 
  options: {
    origin?: string
    method?: string
    headers?: Record<string, string>
    auth?: 'jwt' | 'hmac' | 'none'
  } = {}
): NextRequest {
  const {
    origin,
    method = 'GET',
    headers = {},
    auth = 'none'
  } = options

  const requestHeaders = new Headers({
    'content-type': 'application/json',
    ...headers
  })

  if (origin) {
    requestHeaders.set('origin', origin)
  }

  // Add authentication headers based on type
  if (auth === 'jwt') {
    requestHeaders.set('authorization', 'Bearer valid_jwt_token')
  } else if (auth === 'hmac') {
    requestHeaders.set('x-signature', 'valid_signature')
    requestHeaders.set('x-timestamp', Date.now().toString())
    requestHeaders.set('x-partner-id', 'test-partner')
  }

  return new NextRequest(url, {
    method,
    headers: requestHeaders
  })
}

// Helper to extract response data
async function getResponseData(response: Response) {
  const text = await response.text()
  try {
    return JSON.parse(text)
  } catch {
    return text
  }
}

describe('CORS + Authentication + Rate Limiting Integration', () => {
  
  describe('CORS Enforcement', () => {
    const routes = [
      { name: 'catalog', handler: catalogGET, url: 'https://test.com/api/catalog' },
      { name: 'search/more', handler: searchMoreGET, url: 'https://test.com/api/search/more?q=test&page=2' },
      { name: 'products/[id]', handler: productGET, url: 'https://test.com/api/products/123', params: { id: '123' } },
      { name: 'brands/[id]', handler: brandGET, url: 'https://test.com/api/brands/abc', params: { id: 'abc' } },
      { name: 'retailers/[id]', handler: retailerGET, url: 'https://test.com/api/retailers/xyz', params: { id: 'xyz' } }
    ]

    test.each(routes)('CORS 403 if bad origin — $name', async ({ handler, url, params }) => {
      const request = createMockRequest(url, { origin: BLOCKED_ORIGIN })
      
      let response
      if (params) {
        response = await handler(request, { params: Promise.resolve(params) })
      } else {
        response = await handler(request)
      }

      expect(response.status).toBe(403)
      
      const data = await getResponseData(response)
      expect(data.code).toBe('CORS_BLOCKED')
      expect(response.headers.get('access-control-allow-origin')).toBeNull()
    })

    test.each(routes)('200 when allowed origin — $name', async ({ handler, url, params }) => {
      const request = createMockRequest(url, { 
        origin: ALLOWED_ORIGIN,
        auth: 'jwt' // Provide auth for catalog endpoint
      })
      
      let response
      if (params) {
        response = await handler(request, { params: Promise.resolve(params) })
      } else {
        response = await handler(request)
      }

      // Should not be blocked by CORS (might still fail auth for some endpoints)
      expect(response.status).not.toBe(403)
      expect(response.headers.get('access-control-allow-origin')).toBe(ALLOWED_ORIGIN)
    })
  })

  describe('Authentication Requirements', () => {
    const authRequiredRoutes = [
      { name: 'catalog', handler: catalogGET, url: 'https://test.com/api/catalog' },
      { name: 'search/more', handler: searchMoreGET, url: 'https://test.com/api/search/more?q=test' }
    ]

    const publicRoutes = [
      { name: 'products/[id]', handler: productGET, url: 'https://test.com/api/products/123', params: { id: '123' } },
      { name: 'brands/[id]', handler: brandGET, url: 'https://test.com/api/brands/abc', params: { id: 'abc' } },
      { name: 'retailers/[id]', handler: retailerGET, url: 'https://test.com/api/retailers/xyz', params: { id: 'xyz' } }
    ]

    test.each(authRequiredRoutes)('401 when no JWT/HMAC — $name', async ({ handler, url }) => {
      const request = createMockRequest(url, { origin: ALLOWED_ORIGIN })
      const response = await handler(request)
      
      expect([401, 403]).toContain(response.status)
      
      const data = await getResponseData(response)
      expect(data.error).toMatch(/authentication|unauthorized/i)
    })

    test.each(authRequiredRoutes)('200 when JWT + good origin — $name', async ({ handler, url }) => {
      const request = createMockRequest(url, { 
        origin: ALLOWED_ORIGIN,
        auth: 'jwt'
      })
      const response = await handler(request)
      
      // Should pass CORS and auth (might fail for other reasons like missing data)
      expect(response.status).not.toBe(401)
      expect(response.status).not.toBe(403)
    })

    test.each(authRequiredRoutes)('200 when HMAC + good origin — $name', async ({ handler, url }) => {
      const request = createMockRequest(url, { 
        origin: ALLOWED_ORIGIN,
        auth: 'hmac'
      })
      const response = await handler(request)
      
      // Should pass CORS and auth (might fail for other reasons like missing data)
      expect(response.status).not.toBe(401)
      expect(response.status).not.toBe(403)
    })

    test.each(publicRoutes)('200 when good origin (no auth required) — $name', async ({ handler, url, params }) => {
      const request = createMockRequest(url, { origin: ALLOWED_ORIGIN })
      
      let response
      if (params) {
        response = await handler(request, { params: Promise.resolve(params) })
      } else {
        response = await handler(request)
      }
      
      // Should pass CORS (might fail for other reasons like missing data, but not auth)
      expect(response.status).not.toBe(401)
      expect(response.status).not.toBe(403)
    })
  })

  describe('Rate Limiting', () => {
    test('429 after >10 req/s on /api/catalog', async () => {
      const baseRequest = createMockRequest('https://test.com/api/catalog', {
        origin: ALLOWED_ORIGIN,
        auth: 'jwt'
      })

      // Make 11 rapid requests
      const requests = Array.from({ length: 11 }, () => 
        catalogGET(baseRequest)
      )

      const responses = await Promise.all(requests)
      
      // At least one response should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429)
      expect(rateLimitedResponses.length).toBeGreaterThan(0)

      // Rate limited response should have retry-after header
      const rateLimited = rateLimitedResponses[0]
      expect(rateLimited.headers.get('retry-after')).toBeDefined()
      
      // Should still have CORS headers
      expect(rateLimited.headers.get('access-control-allow-origin')).toBe(ALLOWED_ORIGIN)
    })

    test('Rate limiting preserves CORS headers', async () => {
      // This test ensures rate limited responses still have proper CORS headers
      const request = createMockRequest('https://test.com/api/catalog', {
        origin: ALLOWED_ORIGIN,
        auth: 'jwt'
      })

      // Make enough requests to trigger rate limiting
      let rateLimitedResponse
      for (let i = 0; i < 15; i++) {
        const response = await catalogGET(request)
        if (response.status === 429) {
          rateLimitedResponse = response
          break
        }
      }

      if (rateLimitedResponse) {
        expect(rateLimitedResponse.headers.get('access-control-allow-origin')).toBe(ALLOWED_ORIGIN)
        expect(rateLimitedResponse.headers.get('access-control-allow-methods')).toContain('GET')
      }
    })
  })

  describe('OPTIONS Preflight Requests', () => {
    const optionsHandlers = [
      { name: 'catalog', handler: catalogOPTIONS, url: 'https://test.com/api/catalog' },
      { name: 'search/more', handler: searchMoreOPTIONS, url: 'https://test.com/api/search/more' },
      { name: 'products/[id]', handler: productOPTIONS, url: 'https://test.com/api/products/123' },
      { name: 'brands/[id]', handler: brandOPTIONS, url: 'https://test.com/api/brands/abc' },
      { name: 'retailers/[id]', handler: retailerOPTIONS, url: 'https://test.com/api/retailers/xyz' }
    ]

    test.each(optionsHandlers)('OPTIONS returns proper CORS headers — $name', async ({ handler, url }) => {
      const request = createMockRequest(url, { 
        origin: ALLOWED_ORIGIN,
        method: 'OPTIONS'
      })
      
      const response = await handler(request)
      
      expect(response.status).toBe(200)
      expect(response.headers.get('access-control-allow-origin')).toBe(ALLOWED_ORIGIN)
      expect(response.headers.get('access-control-allow-methods')).toContain('GET')
      expect(response.headers.get('access-control-allow-headers')).toContain('Authorization')
      expect(response.headers.get('access-control-max-age')).toBe('86400')
    })

    test.each(optionsHandlers)('OPTIONS blocked for bad origin — $name', async ({ handler, url }) => {
      const request = createMockRequest(url, { 
        origin: BLOCKED_ORIGIN,
        method: 'OPTIONS'
      })
      
      const response = await handler(request)
      
      // OPTIONS should still respond but with appropriate origin
      expect(response.status).toBe(200)
      expect(response.headers.get('access-control-allow-origin')).not.toBe(BLOCKED_ORIGIN)
    })
  })

  describe('Development Environment', () => {
    beforeAll(() => {
      process.env.NODE_ENV = 'development'
    })

    afterAll(() => {
      process.env.NODE_ENV = 'test'
    })

    test('Localhost allowed in development', async () => {
      const request = createMockRequest('https://test.com/api/products/123', {
        origin: DEV_ORIGIN
      })
      
      const response = await productGET(request, { params: Promise.resolve({ id: '123' }) })
      
      expect(response.status).not.toBe(403)
      expect(response.headers.get('access-control-allow-origin')).toBe(DEV_ORIGIN)
    })
  })

  describe('Feature Flag Control', () => {
    test('CORS disabled when feature flag off', async () => {
      process.env.ENABLE_CORS_STRICT = 'false'
      
      const request = createMockRequest('https://test.com/api/products/123', {
        origin: BLOCKED_ORIGIN
      })
      
      const response = await productGET(request, { params: Promise.resolve({ id: '123' }) })
      
      // Should not be blocked by CORS when disabled
      expect(response.status).not.toBe(403)
      
      // Restore for other tests
      process.env.ENABLE_CORS_STRICT = 'true'
    })
  })

  describe('Error Response Consistency', () => {
    test('CORS errors have consistent format', async () => {
      const request = createMockRequest('https://test.com/api/catalog', {
        origin: BLOCKED_ORIGIN
      })
      
      const response = await catalogGET(request)
      const data = await getResponseData(response)
      
      expect(data).toMatchObject({
        error: 'Forbidden',
        message: expect.stringContaining('Cross-Origin Request Blocked'),
        code: 'CORS_BLOCKED',
        timestamp: expect.any(String)
      })
    })

    test('Auth errors preserve CORS headers', async () => {
      const request = createMockRequest('https://test.com/api/catalog', {
        origin: ALLOWED_ORIGIN
        // No auth headers
      })
      
      const response = await catalogGET(request)
      
      expect(response.status).toBe(401)
      expect(response.headers.get('access-control-allow-origin')).toBe(ALLOWED_ORIGIN)
    })
  })
})

// Comprehensive OPTIONS preflight smoke tests  
describe('OPTIONS Preflight Smoke Tests', () => {
  const allProtectedRoutes = [
    { name: 'catalog', handler: catalogOPTIONS, url: 'https://test.com/api/catalog' },
    { name: 'search/more', handler: searchMoreOPTIONS, url: 'https://test.com/api/search/more' },
    { name: 'products/[id]', handler: productOPTIONS, url: 'https://test.com/api/products/123' },
    { name: 'brands/[id]', handler: brandOPTIONS, url: 'https://test.com/api/brands/abc' },
    { name: 'retailers/[id]', handler: retailerOPTIONS, url: 'https://test.com/api/retailers/xyz' }
  ]

  test.each(allProtectedRoutes)('OPTIONS preflight basic functionality — $name', async ({ handler, url }) => {
    const request = createMockRequest(url, { 
      origin: ALLOWED_ORIGIN,
      method: 'OPTIONS',
      headers: {
        'access-control-request-method': 'GET',
        'access-control-request-headers': 'authorization,content-type'
      }
    })
    
    const response = await handler(request)
    
    // Basic preflight requirements
    expect(response.status).toBe(200)
    expect(response.headers.get('access-control-allow-origin')).toBeTruthy()
    expect(response.headers.get('access-control-allow-methods')).toBeTruthy()
    expect(response.headers.get('access-control-allow-headers')).toBeTruthy()
    expect(response.headers.get('access-control-max-age')).toBeTruthy()
  })

  test.each(allProtectedRoutes)('OPTIONS includes required headers — $name', async ({ handler, url }) => {
    const request = createMockRequest(url, { 
      origin: ALLOWED_ORIGIN,
      method: 'OPTIONS'
    })
    
    const response = await handler(request)
    
    // Check for specific required headers
    const allowedHeaders = response.headers.get('access-control-allow-headers') || ''
    expect(allowedHeaders).toMatch(/authorization/i)
    expect(allowedHeaders).toMatch(/content-type/i)
    expect(allowedHeaders).toMatch(/x-signature/i)
    
    const allowedMethods = response.headers.get('access-control-allow-methods') || ''
    expect(allowedMethods).toContain('GET')
    expect(allowedMethods).toContain('OPTIONS')
  })

  test.each(allProtectedRoutes)('OPTIONS respects origin restrictions — $name', async ({ handler, url }) => {
    const blockedRequest = createMockRequest(url, { 
      origin: BLOCKED_ORIGIN,
      method: 'OPTIONS'
    })
    
    const response = await handler(blockedRequest)
    
    // Should not return blocked origin in allow-origin header
    expect(response.headers.get('access-control-allow-origin')).not.toBe(BLOCKED_ORIGIN)
    expect(response.status).toBe(200) // OPTIONS should still respond
  })

  test.each(allProtectedRoutes)('OPTIONS cache control headers — $name', async ({ handler, url }) => {
    const request = createMockRequest(url, { 
      origin: ALLOWED_ORIGIN,
      method: 'OPTIONS'
    })
    
    const response = await handler(request)
    
    const maxAge = response.headers.get('access-control-max-age')
    expect(maxAge).toBeTruthy()
    expect(parseInt(maxAge || '0')).toBeGreaterThan(0)
  })
})

// Additional integration tests for edge cases
describe('CORS Edge Cases', () => {
  test('No origin header allows request', async () => {
    const request = createMockRequest('https://test.com/api/products/123')
    // No origin header (same-origin request)
    
    const response = await productGET(request, { params: Promise.resolve({ id: '123' }) })
    
    expect(response.status).not.toBe(403)
  })

  test('Empty origin header blocks request', async () => {
    const request = createMockRequest('https://test.com/api/products/123', {
      origin: ''
    })
    
    const response = await productGET(request, { params: Promise.resolve({ id: '123' }) })
    
    expect(response.status).toBe(403)
  })

  test('Case sensitive origin matching', async () => {
    const request = createMockRequest('https://test.com/api/products/123', {
      origin: 'HTTPS://CASHBACK-DEALS.COM' // Wrong case
    })
    
    const response = await productGET(request, { params: Promise.resolve({ id: '123' }) })
    
    expect(response.status).toBe(403)
  })

  test('Subdomain pattern matching', async () => {
    const request = createMockRequest('https://test.com/api/products/123', {
      origin: 'https://staging-branch.amplifyapp.com'
    })
    
    const response = await productGET(request, { params: Promise.resolve({ id: '123' }) })
    
    expect(response.status).not.toBe(403)
    expect(response.headers.get('access-control-allow-origin')).toBe('https://staging-branch.amplifyapp.com')
  })
})
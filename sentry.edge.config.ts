// This file configures the initialization of Sentry for edge features (middleware, edge routes, and so on).
// The config you add here will be used whenever one of the edge features is loaded.
// Note that this config is unrelated to the AWS Amplify Edge Runtime and is also required when running locally.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

// Environment-based Sentry configuration with feature flags
const ENABLE_SENTRY = process.env.ENABLE_SENTRY === 'true';
const ENABLE_SENTRY_LOCAL = process.env.ENABLE_SENTRY_LOCAL === 'true';
const SENTRY_DSN = process.env.SENTRY_DSN;
const SENTRY_TRACES_SAMPLE_RATE = parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE || '0.01');
const SENTRY_ENVIRONMENT = process.env.SENTRY_ENVIRONMENT || process.env.NODE_ENV || 'development';

// Only initialize Sentry if enabled via environment variables
const shouldInitializeSentry = () => {
  // In production, require ENABLE_SENTRY=true
  if (process.env.NODE_ENV === 'production') {
    return ENABLE_SENTRY && SENTRY_DSN;
  }

  // In development, require explicit ENABLE_SENTRY_LOCAL=true
  if (process.env.NODE_ENV === 'development') {
    return ENABLE_SENTRY_LOCAL && SENTRY_DSN;
  }

  // For other environments (test, staging), follow ENABLE_SENTRY
  return ENABLE_SENTRY && SENTRY_DSN;
};

if (shouldInitializeSentry()) {
  console.log(`[Sentry] Initializing edge-side Sentry for ${SENTRY_ENVIRONMENT} environment`);

  Sentry.init({
    dsn: SENTRY_DSN,
    environment: SENTRY_ENVIRONMENT,

    // Production: 1% sampling, Development: 100% sampling for debugging
    tracesSampleRate: SENTRY_TRACES_SAMPLE_RATE,

    // Enable debug in development only
    debug: process.env.NODE_ENV === 'development',
  });
} else {
  console.log('[Sentry] Edge-side Sentry disabled via environment configuration');
}

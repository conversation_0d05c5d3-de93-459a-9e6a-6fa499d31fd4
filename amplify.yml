version: 1
frontend:
  phases:
    preBuild:
      commands:
        - nvm install 20.10.0
        - nvm use 20.10.0
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*

# TODO: Bump to Node 22.x before September 15, 2025 when tests are green
# ADVISORY: If using Amazon Linux 2 build image, consider switching to AL2023 for better performance and security
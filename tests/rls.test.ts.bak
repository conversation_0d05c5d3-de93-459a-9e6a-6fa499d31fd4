import { createClient } from '@supabase/supabase-js';
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';
import { getProducts, getBrands, getRetailers, getPromotions } from '@/lib/data';
import { v4 as uuidv4 } from 'uuid';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Supabase client with service role key for admin operations (e.g., user deletion)
const adminSupabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Helper to create a Supabase client for a specific user
async function createSupabaseClientWithToken(token: string) {
  return createClient(supabaseUrl, supabaseAnonKey, {
    auth: { persistSession: false, autoRefreshToken: false },
    global: { headers: { Authorization: `Bearer ${token}` } },
  });
}

describe('Row Level Security (RLS) Enforcement', () => {
  let user1Email: string;
  let user2Email: string;
  let user1Password = 'TestPassword123!';
  let user2Password = 'TestPassword123!';
  let user1Id: string;
  let user2Id: string;
  let user1Client: any;
  let user2Client: any;
  let anonClient: any;

  // Tables to test
  const publicTables = ['brands', 'products', 'retailers', 'promotions', 'categories', 'product_retailer_offers', 'skus', 'translations', 'product_retailer_promotions'];
  const userSpecificTables = ['user_favorites', 'cashback_reminders', 'user_cashback_claims', 'user_purchases', 'profiles', 'user_roles', 'audit_log', 'cache_invalidation_log', 'sku_audit_log']; // 'users' table is special, handled separately

  beforeAll(async () => {
    // Setup anonymous client
    anonClient = createServerSupabaseReadOnlyClient();

    // Setup test users
    user1Email = `testuser1_${uuidv4()}@example.com`;
    user2Email = `testuser2_${uuidv4()}@example.com`;

    // Sign up and sign in user 1
    const { data: signUpData1, error: signUpError1 } = await adminSupabase.auth.signUp({ email: user1Email, password: user1Password });
    if (signUpError1) throw new Error(`User 1 sign up error: ${signUpError1.message}`);
    const { data: signInData1, error: signInError1 } = await adminSupabase.auth.signInWithPassword({ email: user1Email, password: user1Password });
    if (signInError1) throw new Error(`User 1 sign in error: ${signInError1.message}`);
    user1Client = await createSupabaseClientWithToken(signInData1.session?.access_token!);
    user1Id = signInData1.user?.id!;

    // Sign up and sign in user 2
    const { data: signUpData2, error: signUpError2 } = await adminSupabase.auth.signUp({ email: user2Email, password: user2Password });
    if (signUpError2) throw new Error(`User 2 sign up error: ${signUpError2.message}`);
    const { data: signInData2, error: signInError2 } = await adminSupabase.auth.signInWithPassword({ email: user2Email, password: user2Password });
    if (signInError2) throw new Error(`User 2 sign in error: ${signInError2.message}`);
    user2Client = await createSupabaseClientWithToken(signInData2.session?.access_token!);
    user2Id = signInData2.user?.id!;

    // Ensure profiles are created for the users (if not automatically by trigger)
    await adminSupabase.from('profiles').upsert({ id: user1Id, email: user1Email }, { onConflict: 'id' });
    await adminSupabase.from('profiles').upsert({ id: user2Id, email: user2Email }, { onConflict: 'id' });

    // Insert some data for user1 in user-specific tables
    await adminSupabase.from('user_favorites').insert({ user_id: user1Id, product_id: uuidv4() });
    await adminSupabase.from('cashback_reminders').insert({ user_id: user1Id, promotion_id: uuidv4(), reminder_date: new Date().toISOString() });
    await adminSupabase.from('user_cashback_claims').insert({ user_id: user1Id, product_retailer_promotion_id: uuidv4(), status: 'pending' });
    await adminSupabase.from('user_purchases').insert({ user_id: user1Id, product_id: uuidv4(), retailer_id: uuidv4(), promotion_id: uuidv4(), sku_id: uuidv4(), purchase_price: 10.0 });
    await adminSupabase.from('user_roles').insert({ user_id: user1Id, role: 'sys_architect' });

    // Insert some data for user1 in the newly added user-specific tables
    await adminSupabase.from('audit_log').insert({ table_name: 'test_table', record_id: uuidv4(), operation: 'INSERT', changed_by: user1Id });
    await adminSupabase.from('cache_invalidation_log').insert({ cache_key: 'test_key', triggered_by: user1Id });
    await adminSupabase.from('sku_audit_log').insert({ sku_id: uuidv4(), field_name: 'status', old_value: 'active', new_value: 'inactive', changed_by: user1Id });

  }, 30000); // Increase timeout for setup

  afterAll(async () => {
    // Cleanup test users and their data
    try {
      // Delete data associated with users first
      await adminSupabase.from('user_favorites').delete().eq('user_id', user1Id);
      await adminSupabase.from('cashback_reminders').delete().eq('user_id', user1Id);
      await adminSupabase.from('user_cashback_claims').delete().eq('user_id', user1Id);
      await adminSupabase.from('user_purchases').delete().eq('user_id', user1Id);
      await adminSupabase.from('user_roles').delete().eq('user_id', user1Id);
      await adminSupabase.from('audit_log').delete().eq('changed_by', user1Id);
      await adminSupabase.from('cache_invalidation_log').delete().eq('triggered_by', user1Id);
      await adminSupabase.from('sku_audit_log').delete().eq('changed_by', user1Id);
      await adminSupabase.from('profiles').delete().eq('id', user1Id);
      await adminSupabase.from('profiles').delete().eq('id', user2Id);

      // Delete users
      await adminSupabase.auth.admin.deleteUser(user1Id);
      await adminSupabase.auth.admin.deleteUser(user2Id);
    } catch (error: any) {
      console.error('Error during test cleanup:', error.message);
    }
  }, 30000); // Increase timeout for cleanup

  describe('Public Data Access via createServerSupabaseReadOnlyClient()', () => {
    test.each(publicTables)('should allow read access to %s table for anonymous client', async (table) => {
      const { data, error } = await anonClient.from(table).select('*').limit(1);
      expect(error).toBeNull();
      expect(data).toBeInstanceOf(Array);
    });

    test.each(publicTables)('should deny write access to %s table for anonymous client', async (table) => {
      const dummyId = uuidv4();
      const insertData: any = { id: dummyId }; // Use id for consistency, though it might not be primary key for all
      if (table === 'brands') insertData.name = `Test Brand ${uuidv4()}`;
      else if (table === 'products') insertData.name = `Test Product ${uuidv4()}`;
      else if (table === 'retailers') insertData.name = `Test Retailer ${uuidv4()}`;
      else if (table === 'promotions') insertData.title = `Test Promotion ${uuidv4()}`;
      else if (table === 'categories') insertData.name = `Test Category ${uuidv4()}`;
      else if (table === 'product_retailer_offers') {
        insertData.product_id = uuidv4();
        insertData.retailer_id = uuidv4();
        insertData.price = 10.0;
      }
      else if (table === 'skus') insertData.product_id = uuidv4();
      else if (table === 'translations') {
        insertData.content_type = 'test';
        insertData.language_code = 'en';
        insertData.translated_text = { key: 'value' };
      }
      else if (table === 'product_retailer_promotions') {
        insertData.product_id = uuidv4();
        insertData.retailer_id = uuidv4();
        insertData.promotion_id = uuidv4();
        insertData.price = 10.0;
        insertData.valid_from = new Date().toISOString().split('T')[0];
        insertData.valid_until = new Date().toISOString().split('T')[0];
      }

      // Attempt INSERT
      let { error: insertError } = await anonClient.from(table).insert(insertData);
      expect(insertError).not.toBeNull();
      expect(insertError.message).toContain('permission denied');

      // Attempt UPDATE
      let { error: updateError } = await anonClient.from(table).update({ name: 'Updated Name' }).eq('id', dummyId);
      expect(updateError).not.toBeNull();
      expect(updateError.message).toContain('permission denied');

      // Attempt DELETE
      let { error: deleteError } = await anonClient.from(table).delete().eq('id', dummyId);
      expect(deleteError).not.toBeNull();
      expect(deleteError.message).toContain('permission denied');
    });
  });

  describe('RLS on Public Tables (Write Operations) for Authenticated Users', () => {
    test.each(publicTables)('should allow read access to %s table for authenticated client', async (table) => {
      const { data, error } = await user1Client.from(table).select('*').limit(1);
      expect(error).toBeNull();
      expect(data).toBeInstanceOf(Array);
    });

    test.each(publicTables)('should deny write access to %s table for authenticated client', async (table) => {
      const dummyId = uuidv4();
      const insertData: any = { id: dummyId };
      if (table === 'brands') insertData.name = `Test Brand ${uuidv4()}`;
      else if (table === 'products') insertData.name = `Test Product ${uuidv4()}`;
      else if (table === 'retailers') insertData.name = `Test Retailer ${uuidv4()}`;
      else if (table === 'promotions') insertData.title = `Test Promotion ${uuidv4()}`;
      else if (table === 'categories') insertData.name = `Test Category ${uuidv4()}`;
      else if (table === 'product_retailer_offers') {
        insertData.product_id = uuidv4();
        insertData.retailer_id = uuidv4();
        insertData.price = 10.0;
      }
      else if (table === 'skus') insertData.product_id = uuidv4();
      else if (table === 'translations') {
        insertData.content_type = 'test';
        insertData.language_code = 'en';
        insertData.translated_text = { key: 'value' };
      }
      else if (table === 'product_retailer_promotions') {
        insertData.product_id = uuidv4();
        insertData.retailer_id = uuidv4();
        insertData.promotion_id = uuidv4();
        insertData.price = 10.0;
        insertData.valid_from = new Date().toISOString().split('T')[0];
        insertData.valid_until = new Date().toISOString().split('T')[0];
      }

      // Attempt INSERT
      let { error: insertError } = await user1Client.from(table).insert(insertData);
      expect(insertError).not.toBeNull();
      expect(insertError.message).toContain('permission denied');

      // Attempt UPDATE
      let { error: updateError } = await user1Client.from(table).update({ name: 'Updated Name' }).eq('id', dummyId);
      expect(updateError).not.toBeNull();
      expect(updateError.message).toContain('permission denied');

      // Attempt DELETE
      let { error: deleteError } = await user1Client.from(table).delete().eq('id', dummyId);
      expect(deleteError).not.toBeNull();
      expect(deleteError.message).toContain('permission denied');
    });
  });

  describe('RLS on User-Specific Tables', () => {
    // Test 'users' table (special case, usually managed by auth service)
    test('should allow authenticated user to read their own "users" profile', async () => {
      const { data, error } = await user1Client.from('users').select('*').eq('id', user1Id).single();
      expect(error).toBeNull();
      expect(data).not.toBeNull();
      expect(data.id).toBe(user1Id);
    });

    test('should deny authenticated user from reading another user profile', async () => {
      const { data, error } = await user1Client.from('users').select('*').eq('id', user2Id).single();
      expect(error).not.toBeNull();
      expect(error.message).toContain('Row not found'); // Or 'permission denied' depending on RLS policy
      expect(data).toBeNull();
    });

    test.each(userSpecificTables)('should allow authenticated user to read their own data in %s table', async (table) => {
      // For profiles and user_roles, the ID is the user_id
      const idColumn = (table === 'profiles' || table === 'user_roles') ? 'id' : 'user_id';
      const { data, error } = await user1Client.from(table).select('*').eq(idColumn, user1Id).limit(1);
      expect(error).toBeNull();
      expect(data).toBeInstanceOf(Array);
      expect(data.length).toBeGreaterThan(0);
    });

    test.each(userSpecificTables)('should deny authenticated user from reading another user data in %s table', async (table) => {
      const idColumn = (table === 'profiles' || table === 'user_roles') ? 'id' : 'user_id';
      const { data, error } = await user1Client.from(table).select('*').eq(idColumn, user2Id).limit(1);
      expect(error).not.toBeNull();
      expect(error.message).toContain('permission denied'); // Or 'Row not found'
      expect(data).toBeInstanceOf(Array);
      expect(data.length).toBe(0);
    });

    test.each(userSpecificTables)('should allow authenticated user to insert their own data in %s table', async (table) => {
      const insertData: any = { user_id: user1Id };
      if (table === 'user_favorites') insertData.product_id = uuidv4();
      else if (table === 'cashback_reminders') {
        insertData.promotion_id = uuidv4();
        insertData.reminder_date = new Date().toISOString();
      } else if (table === 'user_cashback_claims') {
        insertData.product_retailer_promotion_id = uuidv4();
        insertData.status = 'pending';
      } else if (table === 'user_purchases') {
        insertData.product_id = uuidv4();
        insertData.retailer_id = uuidv4();
        insertData.promotion_id = uuidv4();
        insertData.sku_id = uuidv4();
        insertData.purchase_price = 10.0;
      } else if (table === 'profiles') {
        insertData.id = user1Id; // Overwrite user_id for profiles
        insertData.email = user1Email;
      } else if (table === 'user_roles') {
        insertData.user_id = user1Id;
        insertData.role = 'sys_architect'; // Assuming 'sys_architect' is a valid role
      } else if (table === 'audit_log') {
        insertData.table_name = 'test_table';
        insertData.record_id = uuidv4();
        insertData.operation = 'INSERT';
        insertData.changed_by = user1Id;
      } else if (table === 'cache_invalidation_log') {
        insertData.cache_key = 'test_key';
        insertData.triggered_by = user1Id;
      } else if (table === 'sku_audit_log') {
        insertData.sku_id = uuidv4();
        insertData.field_name = 'status';
        insertData.old_value = 'active';
        insertData.new_value = 'inactive';
        insertData.changed_by = user1Id;
      }

      const { error } = await user1Client.from(table).insert(insertData);
      expect(error).toBeNull();
      // Clean up the inserted data to avoid conflicts in subsequent runs
      if (table !== 'profiles' && table !== 'user_roles') { // Profiles and user_roles are upserted/managed differently
        await adminSupabase.from(table).delete().eq('user_id', user1Id).order('created_at', { ascending: false }).limit(1);
      }
    });

    test.each(userSpecificTables)('should deny authenticated user from inserting data for another user in %s table', async (table) => {
      const insertData: any = { user_id: user2Id }; // Attempt to insert for user2
      if (table === 'user_favorites') insertData.product_id = uuidv4();
      else if (table === 'cashback_reminders') {
        insertData.promotion_id = uuidv4();
        insertData.reminder_date = new Date().toISOString();
      } else if (table === 'user_cashback_claims') {
        insertData.product_retailer_promotion_id = uuidv4();
        insertData.status = 'pending';
      } else if (table === 'user_purchases') {
        insertData.product_id = uuidv4();
        insertData.retailer_id = uuidv4();
        insertData.promotion_id = uuidv4();
        insertData.sku_id = uuidv4();
        insertData.purchase_price = 10.0;
      } else if (table === 'profiles') {
        insertData.id = user2Id;
        insertData.email = user2Email;
      } else if (table === 'user_roles') {
        insertData.user_id = user2Id;
        insertData.role = 'sys_architect';
      } else if (table === 'audit_log') {
        insertData.table_name = 'test_table';
        insertData.record_id = uuidv4();
        insertData.operation = 'INSERT';
        insertData.changed_by = user2Id;
      } else if (table === 'cache_invalidation_log') {
        insertData.cache_key = 'test_key';
        insertData.triggered_by = user2Id;
      } else if (table === 'sku_audit_log') {
        insertData.sku_id = uuidv4();
        insertData.field_name = 'status';
        insertData.old_value = 'active';
        insertData.new_value = 'inactive';
        insertData.changed_by = user2Id;
      }

      const { error } = await user1Client.from(table).insert(insertData);
      expect(error).not.toBeNull();
      expect(error.message).toContain('permission denied');
    });

    test.each(userSpecificTables)('should allow authenticated user to update their own data in %s table', async (table) => {
      // Insert a record for user1 to update
      let recordIdToUpdate: string;
      if (table === 'profiles') {
        recordIdToUpdate = user1Id; // Profiles are updated by user ID
      } else if (table === 'user_roles') {
        const { data: roleData } = await adminSupabase.from('user_roles').select('id').eq('user_id', user1Id).single();
        recordIdToUpdate = roleData?.id;
      } else if (table === 'audit_log') {
        const { data: auditData } = await adminSupabase.from('audit_log').insert({ table_name: 'test_table', record_id: uuidv4(), operation: 'INSERT', changed_by: user1Id }).select('id').single();
        recordIdToUpdate = auditData?.id;
      } else if (table === 'cache_invalidation_log') {
        const { data: cacheData } = await adminSupabase.from('cache_invalidation_log').insert({ cache_key: 'test_key', triggered_by: user1Id }).select('id').single();
        recordIdToUpdate = cacheData?.id;
      } else if (table === 'sku_audit_log') {
        const { data: skuAuditData } = await adminSupabase.from('sku_audit_log').insert({ sku_id: uuidv4(), field_name: 'status', old_value: 'active', new_value: 'inactive', changed_by: user1Id }).select('id').single();
        recordIdToUpdate = skuAuditData?.id;
      } else {
        const { data: insertedData } = await adminSupabase.from(table).insert({ user_id: user1Id, product_id: uuidv4(), promotion_id: uuidv4(), reminder_date: new Date().toISOString(), product_retailer_promotion_id: uuidv4(), retailer_id: uuidv4(), sku_id: uuidv4(), purchase_price: 10.0 }).select('id').single();
        recordIdToUpdate = insertedData?.id;
      }

      if (!recordIdToUpdate) throw new Error(`Failed to create record for update test in ${table}`);

      const updatePayload: any = {};
      if (table === 'profiles') updatePayload.full_name = 'Updated Name';
      else if (table === 'user_favorites') updatePayload.product_id = uuidv4();
      else if (table === 'cashback_reminders') updatePayload.is_sent = true;
      else if (table === 'user_cashback_claims') updatePayload.status = 'approved';
      else if (table === 'user_purchases') updatePayload.purchase_price = 20.0;
      else if (table === 'user_roles') updatePayload.role = 'resource_specialist';
      else if (table === 'audit_log') updatePayload.operation = 'UPDATE';
      else if (table === 'cache_invalidation_log') updatePayload.reason = 'test_reason';
      else if (table === 'sku_audit_log') updatePayload.new_value = 'active';

      const idColumn = (table === 'profiles' || table === 'user_roles') ? 'id' : 'user_id';
      const { error } = await user1Client.from(table).update(updatePayload).eq(idColumn, recordIdToUpdate);
      expect(error).toBeNull();

      // Clean up if a temporary record was created
      if (table !== 'profiles' && table !== 'user_roles') {
        await adminSupabase.from(table).delete().eq('id', recordIdToUpdate);
      }
    });

    test.each(userSpecificTables)('should deny authenticated user from updating another user data in %s table', async (table) => {
      // Insert a record for user2 (owned by user2)
      let recordIdToUpdate: string;
      if (table === 'profiles') {
        recordIdToUpdate = user2Id;
      } else if (table === 'user_roles') {
        const { data: roleData } = await adminSupabase.from('user_roles').select('id').eq('user_id', user2Id).single();
        recordIdToUpdate = roleData?.id;
      } else {
        const { data: insertedData } = await adminSupabase.from(table).insert({ user_id: user2Id, product_id: uuidv4(), promotion_id: uuidv4(), reminder_date: new Date().toISOString(), product_retailer_promotion_id: uuidv4(), retailer_id: uuidv4(), sku_id: uuidv4(), purchase_price: 10.0 }).select('id').single();
        recordIdToUpdate = insertedData?.id;
      }

      if (!recordIdToUpdate) throw new Error(`Failed to create record for update test in ${table}`);

      const updatePayload: any = {};
      if (table === 'profiles') updatePayload.full_name = 'Attempted Update';
      else if (table === 'user_favorites') updatePayload.product_id = uuidv4();
      else if (table === 'cashback_reminders') updatePayload.is_sent = true;
      else if (table === 'user_cashback_claims') updatePayload.status = 'approved';
      else if (table === 'user_purchases') updatePayload.purchase_price = 20.0;
      else if (table === 'user_roles') updatePayload.role = 'resource_specialist';
      else if (table === 'audit_log') updatePayload.operation = 'ATTEMPTED_UPDATE';
      else if (table === 'cache_invalidation_log') updatePayload.reason = 'attempted_reason';
      else if (table === 'sku_audit_log') updatePayload.new_value = 'attempted_value';

      const idColumn = (table === 'profiles' || table === 'user_roles') ? 'id' : 'user_id';
      const { error } = await user1Client.from(table).update(updatePayload).eq(idColumn, recordIdToUpdate);
      expect(error).not.toBeNull();
      expect(error.message).toContain('permission denied');

      // Clean up if a temporary record was created
      if (table !== 'profiles' && table !== 'user_roles') {
        await adminSupabase.from(table).delete().eq('id', recordIdToUpdate);
      }
    });

    test.each(userSpecificTables)('should allow authenticated user to delete their own data in %s table', async (table) => {
      // Insert a record for user1 to delete
      let recordIdToDelete: string;
      if (table === 'profiles') {
        recordIdToDelete = user1Id; // Profiles are deleted by user ID
      } else if (table === 'user_roles') {
        const { data: roleData } = await adminSupabase.from('user_roles').select('id').eq('user_id', user1Id).single();
        recordIdToDelete = roleData?.id;
      } else if (table === 'audit_log') {
        const { data: auditData } = await adminSupabase.from('audit_log').insert({ table_name: 'test_table', record_id: uuidv4(), operation: 'DELETE', changed_by: user1Id }).select('id').single();
        recordIdToDelete = auditData?.id;
      } else if (table === 'cache_invalidation_log') {
        const { data: cacheData } = await adminSupabase.from('cache_invalidation_log').insert({ cache_key: 'test_key', triggered_by: user1Id }).select('id').single();
        recordIdToDelete = cacheData?.id;
      } else if (table === 'sku_audit_log') {
        const { data: skuAuditData } = await adminSupabase.from('sku_audit_log').insert({ sku_id: uuidv4(), field_name: 'status', old_value: 'active', new_value: 'inactive', changed_by: user1Id }).select('id').single();
        recordIdToDelete = skuAuditData?.id;
      } else {
        const { data: insertedData } = await adminSupabase.from(table).insert({ user_id: user1Id, product_id: uuidv4(), promotion_id: uuidv4(), reminder_date: new Date().toISOString(), product_retailer_promotion_id: uuidv4(), retailer_id: uuidv4(), sku_id: uuidv4(), purchase_price: 10.0 }).select('id').single();
        recordIdToDelete = insertedData?.id;
      }

      if (!recordIdToDelete) throw new Error(`Failed to create record for delete test in ${table}`);

      const idColumn = (table === 'profiles' || table === 'user_roles') ? 'id' : 'user_id';
      const { error } = await user1Client.from(table).delete().eq(idColumn, recordIdToDelete);
      expect(error).toBeNull();
    });

    test.each(userSpecificTables)('should deny authenticated user from deleting another user's data in %s table', async (table) => {
      // Insert a record for user2 (owned by user2)
      let recordIdToDelete: string;
      if (table === 'profiles') {
        recordIdToDelete = user2Id;
      } else if (table === 'user_roles') {
        const { data: roleData } = await adminSupabase.from('user_roles').select('id').eq('user_id', user2Id).single();
        recordIdToDelete = roleData?.id;
      } else if (table === 'audit_log') {
        const { data: auditData } = await adminSupabase.from('audit_log').insert({ table_name: 'test_table', record_id: uuidv4(), operation: 'DELETE', changed_by: user2Id }).select('id').single();
        recordIdToDelete = auditData?.id;
      } else if (table === 'cache_invalidation_log') {
        const { data: cacheData } = await adminSupabase.from('cache_invalidation_log').insert({ cache_key: 'test_key', triggered_by: user2Id }).select('id').single();
        recordIdToDelete = cacheData?.id;
      } else if (table === 'sku_audit_log') {
        const { data: skuAuditData } = await adminSupabase.from('sku_audit_log').insert({ sku_id: uuidv4(), field_name: 'status', old_value: 'active', new_value: 'inactive', changed_by: user2Id }).select('id').single();
        recordIdToDelete = skuAuditData?.id;
      } else {
        const { data: insertedData } = await adminSupabase.from(table).insert({ user_id: user2Id, product_id: uuidv4(), promotion_id: uuidv4(), reminder_date: new Date().toISOString(), product_retailer_promotion_id: uuidv4(), retailer_id: uuidv4(), sku_id: uuidv4(), purchase_price: 10.0 }).select('id').single();
        recordIdToDelete = insertedData?.id;
      }

      if (!recordIdToDelete) throw new Error(`Failed to create record for delete test in ${table}`);

      const idColumn = (table === 'profiles' || table === 'user_roles') ? 'id' : 'user_id';
      const { error } = await user1Client.from(table).delete().eq(idColumn, recordIdToDelete);
      expect(error).not.toBeNull();
      expect(error.message).toContain('permission denied');

      // Clean up if a temporary record was created
      if (table !== 'profiles' && table !== 'user_roles') {
        await adminSupabase.from(table).delete().eq('id', recordIdToDelete);
      }
    });
  });

  describe('Integration with Application Data Layer Functions', () => {
    test('getProducts should return data for anonymous client', async () => {
      const result = await getProducts(anonClient, { page: 1, pageSize: 1 });
      expect(result.product).toBeInstanceOf(Array);
      expect(result.product.length).toBeGreaterThanOrEqual(0); // Can be 0 if no products exist
      expect(result.pagination.total).toBeGreaterThanOrEqual(0);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.pageSize).toBe(1);
    });

    test('getProducts should return data for authenticated client', async () => {
      const result = await getProducts(user1Client, { page: 1, pageSize: 1 });
      expect(result.product).toBeInstanceOf(Array);
      expect(result.product.length).toBeGreaterThanOrEqual(0);
      expect(result.pagination.total).toBeGreaterThanOrEqual(0);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.pageSize).toBe(1);
    });

    test('getBrands should return data for anonymous client', async () => {
      const result = await getBrands(anonClient, 1, 1);
      expect(result.data).toBeInstanceOf(Array);
      expect(result.data.length).toBeGreaterThanOrEqual(0);
      expect(result.pagination.total).toBeGreaterThanOrEqual(0);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.pageSize).toBe(1);
    });

    test('getBrands should return data for authenticated client', async () => {
      const result = await getBrands(user1Client, 1, 1);
      expect(result.data).toBeInstanceOf(Array);
      expect(result.data.length).toBeGreaterThanOrEqual(0);
      expect(result.pagination.total).toBeGreaterThanOrEqual(0);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.pageSize).toBe(1);
    });

    test('getRetailers should return data for anonymous client', async () => {
      const result = await getRetailers(anonClient, {}, 1, 1);
      expect(result.data).toBeInstanceOf(Array);
      expect(result.data.length).toBeGreaterThanOrEqual(0);
      expect(result.pagination.total).toBeGreaterThanOrEqual(0);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.pageSize).toBe(1);
    });

    test('getRetailers should return data for authenticated client', async () => {
      const result = await getRetailers(user1Client, {}, 1, 1);
      expect(result.data).toBeInstanceOf(Array);
      expect(result.data.length).toBeGreaterThanOrEqual(0);
      expect(result.pagination.total).toBeGreaterThanOrEqual(0);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.pageSize).toBe(1);
    });

    test('getPromotions should return data for anonymous client', async () => {
      const result = await getPromotions(anonClient, {}, 1, 1);
      expect(result.data).toBeInstanceOf(Array);
      expect(result.data.length).toBeGreaterThanOrEqual(0);
      expect(result.pagination.total).toBeGreaterThanOrEqual(0);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.pageSize).toBe(1);
    });

    test('getPromotions should return data for authenticated client', async () => {
      const result = await getPromotions(user1Client, {}, 1, 1);
      expect(result.data).toBeInstanceOf(Array);
      expect(result.data.length).toBeGreaterThanOrEqual(0);
      expect(result.pagination.total).toBeGreaterThanOrEqual(0);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.pageSize).toBe(1);
    });
  });
});
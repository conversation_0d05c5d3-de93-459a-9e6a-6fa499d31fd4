/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ProductsContent from '../../src/app/products/components/ProductsContent';
import { TransformedProduct } from '../../src/lib/data/types';

// Helper function to generate mock products
const generateMockProducts = (count: number, startId = 1): TransformedProduct[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: (startId + i).toString(),
    slug: `product-${startId + i}`,
    name: `Product ${startId + i}`,
    description: `Description ${startId + i}`,
    images: [],
    status: 'active' as const,
    isFeatured: false,
    isSponsored: false,
    cashbackAmount: 0,
    minPrice: (i + 1) * 10,
    maxPrice: (i + 1) * 15,
    rating: 4.5,
    reviewCount: 10,
    categories: [],
    tags: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    modelNumber: `MODEL-${startId + i}`,
    brand: null,
    category: null,
    promotion: null,
    retailerOffers: [],
  }));
};

// Mock data for products pages
const mockProductsPage1 = generateMockProducts(20, 1);
const mockProductsPage2 = generateMockProducts(20, 21);
const mockProductsPage3 = generateMockProducts(16, 41); // Last page with 16 items

// Mock all products for testing total count
const allMockProducts = [...mockProductsPage1, ...mockProductsPage2, ...mockProductsPage3];

// Mock fetch function to simulate API calls with delay
const fetchProducts = jest.fn((page) => {
  // Simulate network delay
  return new Promise((resolve) => {
    setTimeout(() => {
      if (page === 1) resolve(mockProductsPage1);
      else if (page === 2) resolve(mockProductsPage2);
      else if (page === 3) resolve(mockProductsPage3);
      else resolve([]);
    }, 100);
  });
});

jest.mock('../../src/lib/data/products', () => ({
  getProducts: (params: { page: number }) => fetchProducts(params.page),
}));

describe('ProductsContent Pagination', () => {
  const queryClient = new QueryClient();

  beforeEach(() => {
    fetchProducts.mockClear();
  });

  function renderComponent(initialPage = 1, hasMore = true) {
    const initialData = {
      data: initialPage === 1 ? mockProductsPage1 : initialPage === 2 ? mockProductsPage2 : mockProductsPage3,
      error: null,
      pagination: { 
        page: initialPage, 
        pageSize: 20,
        total: allMockProducts.length, 
        totalPages: 3, 
        hasNext: hasMore,
        hasPrev: initialPage > 1
      } 
    };
    
    render(
      <QueryClientProvider client={queryClient}>
        <ProductsContent 
          initialData={initialData} 
          filterOptions={{ brands: [], promotions: [] }} 
        />
      </QueryClientProvider>
    );
    
    return { initialData };
  }

  test('renders initial products and pagination controls', () => {
    renderComponent();
    expect(screen.getByText('Product 1')).toBeInTheDocument();
    expect(screen.getByText('Product 2')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /page 1/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /page 2/i })).toBeInTheDocument();
  });

  test('clicking on page 2 fetches and displays new products', async () => {
    renderComponent();

    const page2Button = screen.getByRole('button', { name: /page 2/i });
    fireEvent.click(page2Button);

    await waitFor(() => {
      expect(fetchProducts).toHaveBeenCalledWith(2);
    });

    await waitFor(() => {
      expect(screen.getByText('Product 3')).toBeInTheDocument();
      expect(screen.getByText('Product 4')).toBeInTheDocument();
    });
  });

  test('pagination buttons update active page', async () => {
    renderComponent();

    const page1Button = screen.getByRole('button', { name: /page 1/i });
    const page2Button = screen.getByRole('button', { name: /page 2/i });

    expect(page1Button).toHaveAttribute('aria-current', 'page');
    expect(page2Button).not.toHaveAttribute('aria-current');

    fireEvent.click(page2Button);

    await waitFor(() => {
      expect(page2Button).toHaveAttribute('aria-current', 'page');
      expect(page1Button).not.toHaveAttribute('aria-current');
    });
  });

  test('loads more products when clicking Load More button', async () => {
    // Render first page with hasNext = true
    renderComponent(1, true);
    
    // Initial products should be visible
    expect(screen.getByText('Product 1')).toBeInTheDocument();
    expect(screen.getByText('Product 20')).toBeInTheDocument();
    expect(screen.queryByText('Product 21')).not.toBeInTheDocument();
    
    // Find and click Load More button
    const loadMoreButton = screen.getByRole('button', { name: /load more/i });
    fireEvent.click(loadMoreButton);
    
    // Should show loading state
    expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
    
    // Wait for next page to load
    await waitFor(() => {
      expect(fetchProducts).toHaveBeenCalledWith(2);
      expect(screen.getByText('Product 21')).toBeInTheDocument();
      expect(screen.getByText('Product 40')).toBeInTheDocument();
      expect(screen.queryByTestId('loading-indicator')).not.toBeInTheDocument();
    });
    
    // Total count should be visible
    expect(screen.getByText(/showing 40 of 56 products/i)).toBeInTheDocument();
  });
  
  test('hides Load More button when all products are loaded', async () => {
    // Render last page with hasNext = false
    renderComponent(3, false);
    
    // Last page products should be visible
    expect(screen.getByText('Product 41')).toBeInTheDocument();
    expect(screen.getByText('Product 56')).toBeInTheDocument();
    
    // Load More button should not be present
    expect(screen.queryByRole('button', { name: /load more/i })).not.toBeInTheDocument();
    
    // Should show total count
    expect(screen.getByText(/showing 56 of 56 products/i)).toBeInTheDocument();
  });
  
  test('handles loading state during data fetching', async () => {
    // Mock a delayed response
    const originalFetch = fetchProducts;
    fetchProducts.mockImplementationOnce(() => {
      return new Promise((resolve) => {
        setTimeout(() => resolve(mockProductsPage2), 200);
      });
    });
    
    renderComponent(1);
    
    // Click Load More
    const loadMoreButton = screen.queryByRole('button', { name: /load more/i });
    if (loadMoreButton) {
      fireEvent.click(loadMoreButton);
    }
    
    // Should show loading state if Load More was clicked
    if (loadMoreButton) {
      const loadingIndicator = screen.queryByTestId('loading-indicator');
      expect(loadingIndicator).toBeInTheDocument();
    }
    
    // Wait for loading to complete
    if (loadMoreButton) {
      await waitFor(() => {
        const loadingIndicator = screen.queryByTestId('loading-indicator');
        expect(loadingIndicator).not.toBeInTheDocument();
      });
    }
    
    // Restore original mock
    fetchProducts.mockImplementation(originalFetch);
  });
});

// tests/e2e/auth-hmac.spec.ts
// End-to-end tests for HMAC authentication using Playwright

import { test, expect } from '@playwright/test'
import crypto from 'crypto'

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'
const PARTNER_ID = 'test-partner'
const PARTNER_SECRET = 'test-secret-minimum-32-characters-long'

// HMAC helper functions for testing
function generateHMACSignature(
  method: string,
  path: string,
  timestamp: number,
  body: string = '',
  secret: string = PARTNER_SECRET
): string {
  const bodyHash = crypto.createHash('sha256').update(body).digest('hex')
  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`
  return crypto.createHmac('sha256', secret).update(message).digest('hex')
}

function createHMACHeaders(
  method: string,
  path: string,
  body: string = '',
  options?: { 
    secret?: string
    partnerId?: string
    includeVersion?: boolean
    customTimestamp?: number
  }
): Record<string, string> {
  const timestamp = options?.customTimestamp || Math.floor(Date.now() / 1000)
  const secret = options?.secret || PARTNER_SECRET
  const partnerId = options?.partnerId || PARTNER_ID
  
  const signature = generateHMACSignature(method, path, timestamp, body, secret)
  
  const headers: Record<string, string> = {
    'X-Signature': `sha256=${signature}`,
    'X-Timestamp': timestamp.toString(),
    'X-Partner-ID': partnerId,
    'Content-Type': 'application/json'
  }
  
  if (options?.includeVersion) {
    headers['X-Version'] = '1.0'
  }
  
  return headers
}

test.describe('HMAC Authentication E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up any necessary test environment
    await page.goto(BASE_URL)
  })

  test.describe('Search API Authentication', () => {
    test('should reject unauthenticated requests', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/search?q=laptop`)
      
      expect(response.status()).toBe(401)
      
      const body = await response.json()
      expect(body.error).toBe('Unauthorized')
      expect(body.code).toBe('MISSING_AUTH')
      expect(body.supportedMethods).toContain('HMAC')
      expect(body.traceId).toMatch(/^hmac-/)
    })

    test('should accept valid HMAC authentication', async ({ request }) => {
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path, '', { includeVersion: true })
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      
      expect(response.status()).toBe(200)
      
      const body = await response.json()
      expect(body).toHaveProperty('products')
      expect(Array.isArray(body.products)).toBe(true)
    })

    test('should reject invalid HMAC signature', async ({ request }) => {
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path, '', { secret: 'wrong-secret' })
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      
      expect(response.status()).toBe(401)
      
      const body = await response.json()
      expect(body.code).toBe('MISSING_AUTH')
    })

    test('should reject expired timestamp', async ({ request }) => {
      const path = '/api/search'
      const expiredTimestamp = Math.floor(Date.now() / 1000) - 400 // 400 seconds ago
      const headers = createHMACHeaders('GET', path, '', { customTimestamp: expiredTimestamp })
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      
      expect(response.status()).toBe(401)
    })

    test('should reject unknown partner ID', async ({ request }) => {
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path, '', { partnerId: 'unknown-partner' })
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      
      expect(response.status()).toBe(401)
    })
  })

  test.describe('Search Suggestions API', () => {
    test('should authenticate search suggestions endpoint', async ({ request }) => {
      const path = '/api/search/suggestions'
      const headers = createHMACHeaders('GET', path)
      
      const response = await request.get(`${BASE_URL}${path}?q=lap`, { headers })
      
      expect(response.status()).toBe(200)
      
      const body = await response.json()
      expect(body).toHaveProperty('suggestions')
      expect(Array.isArray(body.suggestions)).toBe(true)
    })

    test('should reject unauthenticated suggestions requests', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/search/suggestions?q=lap`)
      
      expect(response.status()).toBe(401)
    })
  })

  test.describe('Search More API', () => {
    test('should authenticate search more endpoint', async ({ request }) => {
      const path = '/api/search/more'
      const headers = createHMACHeaders('GET', path)
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop&page=2`, { headers })
      
      expect(response.status()).toBe(200)
      
      const body = await response.json()
      expect(body).toHaveProperty('products')
    })

    test('should reject unauthenticated more requests', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/search/more?q=laptop&page=2`)
      
      expect(response.status()).toBe(401)
    })
  })

  test.describe('POST Requests with Body', () => {
    test('should handle POST requests with JSON body', async ({ request }) => {
      const path = '/api/search'
      const body = JSON.stringify({
        query: 'laptop',
        filters: {
          brand: 'samsung',
          priceRange: { min: 500, max: 1500 }
        }
      })
      
      const headers = createHMACHeaders('POST', path, body, { includeVersion: true })
      
      const response = await request.post(`${BASE_URL}${path}`, {
        headers,
        data: body
      })
      
      expect(response.status()).toBe(200)
      
      const responseBody = await response.json()
      expect(responseBody).toHaveProperty('products')
    })

    test('should reject POST with body hash mismatch', async ({ request }) => {
      const path = '/api/search'
      const actualBody = JSON.stringify({ query: 'laptop' })
      const signatureBody = JSON.stringify({ query: 'different' }) // Different body for signature
      
      const headers = createHMACHeaders('POST', path, signatureBody)
      
      const response = await request.post(`${BASE_URL}${path}`, {
        headers,
        data: actualBody
      })
      
      expect(response.status()).toBe(401)
    })
  })

  test.describe('Error Response Format', () => {
    test('should return structured error responses', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/search?q=laptop`)
      
      expect(response.status()).toBe(401)
      
      const body = await response.json()
      expect(body).toHaveProperty('error')
      expect(body).toHaveProperty('message')
      expect(body).toHaveProperty('code')
      expect(body).toHaveProperty('traceId')
      expect(body).toHaveProperty('serverTime')
      expect(body).toHaveProperty('supportedMethods')
      expect(body).toHaveProperty('documentation')
      
      // Check Date header for clock skew detection
      const dateHeader = response.headers()['date']
      expect(dateHeader).toBeDefined()
    })

    test('should include trace ID in responses', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/search?q=laptop`)
      
      const body = await response.json()
      expect(body.traceId).toMatch(/^hmac-get-[a-z0-9]+-[a-z0-9]+$/)
      
      // Check X-Trace-ID header
      const traceHeader = response.headers()['x-trace-id']
      expect(traceHeader).toBe(body.traceId)
    })
  })

  test.describe('Rate Limiting Integration', () => {
    test('should apply rate limiting after authentication', async ({ request }) => {
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path)
      
      // Make multiple requests quickly
      const requests = Array.from({ length: 5 }, () =>
        request.get(`${BASE_URL}${path}?q=test${Math.random()}`, { headers })
      )
      
      const responses = await Promise.all(requests)
      
      // All should either succeed (200) or be rate limited (429)
      // None should be authentication failures (401)
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status())
      })
    })
  })

  test.describe('API Version Support', () => {
    test('should accept supported API version', async ({ request }) => {
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path, '', { includeVersion: true })
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      
      expect(response.status()).toBe(200)
    })

    test('should work without version header', async ({ request }) => {
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path) // No version header
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      
      expect(response.status()).toBe(200)
    })
  })

  test.describe('Concurrent Requests', () => {
    test('should handle concurrent authenticated requests', async ({ request }) => {
      const path = '/api/search'
      
      // Create 10 concurrent requests with different signatures
      const requests = Array.from({ length: 10 }, (_, i) => {
        const headers = createHMACHeaders('GET', `${path}?q=test${i}`)
        return request.get(`${BASE_URL}${path}?q=test${i}`, { headers })
      })
      
      const responses = await Promise.all(requests)
      
      // All should succeed
      responses.forEach(response => {
        expect(response.status()).toBe(200)
      })
    })
  })

  test.describe('Feature Flag Behavior', () => {
    test('should respect authentication when enabled', async ({ request }) => {
      // This test assumes authentication is enabled in the test environment
      const response = await request.get(`${BASE_URL}/api/search?q=laptop`)
      
      expect(response.status()).toBe(401)
    })
  })

  test.describe('Performance Validation', () => {
    test('should respond within acceptable time limits', async ({ request }) => {
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path)
      
      const startTime = Date.now()
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      const endTime = Date.now()
      
      expect(response.status()).toBe(200)
      
      const responseTime = endTime - startTime
      expect(responseTime).toBeLessThan(5000) // Should respond within 5 seconds
    })
  })
})

# Troubleshooting Guide

*This file is auto-generated troubleshooting documentation for common issues and solutions. Last updated: January 2025*

## Overview

This guide provides solutions to common issues encountered while developing, testing, and deploying the Cashback Deals platform. Issues are organized by category with step-by-step resolution procedures.

## Build & Development Issues

### TypeScript Compilation Errors

#### Issue: `Cannot find module '@/lib/data'`
**Symptoms:**
- TypeScript error during build
- Import statements not resolving
- Module path aliases not working

**Solution:**
```bash
# 1. Check tsconfig.json path mappings
cat tsconfig.json | grep -A 5 "paths"

# 2. Clear TypeScript cache
rm -rf .next/cache
rm -rf node_modules/.cache

# 3. Restart TypeScript server (VS Code)
# Command Palette > TypeScript: Restart TS Server

# 4. Verify import paths
npm run typecheck
```

**Root Cause:** Path mapping configuration or cache issues

#### Issue: `Type 'undefined' is not assignable to type 'string'`
**Symptoms:**
- TypeScript strict mode errors
- Environment variable type errors
- Undefined value assignments

**Solution:**
```typescript
// ❌ Bad - Can cause TypeScript error
const url = process.env.NEXT_PUBLIC_SUPABASE_URL

// ✅ Good - Proper type handling
const url = process.env.NEXT_PUBLIC_SUPABASE_URL!
// or
const url = process.env.NEXT_PUBLIC_SUPABASE_URL || 'fallback'
// or
const url = process.env.NEXT_PUBLIC_SUPABASE_URL as string
```

**Root Cause:** Missing type assertions or default values

### Next.js Build Failures

#### Issue: `Error: ENOENT: no such file or directory`
**Symptoms:**
- Build process fails
- File not found errors
- Missing dependency errors

**Diagnostic Steps:**
```bash
# 1. Check for missing files
ls -la src/lib/data/

# 2. Verify package.json dependencies
npm ls

# 3. Clean install
rm -rf node_modules package-lock.json
npm install

# 4. Check build logs
npm run build 2>&1 | tee build.log
```

**Solution:**
```bash
# Clean build approach
npm run clean:build

# If still failing, check for missing files
find src -name "*.ts" -o -name "*.tsx" | head -10
```

#### Issue: `Module not found: Can't resolve 'fs'`
**Symptoms:**
- Build fails with Node.js module errors
- Server-side code running in browser
- Module resolution errors

**Solution:**
```javascript
// next.config.js - Add webpack configuration
module.exports = {
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
        path: false,
        os: false,
        crypto: false,
      }
    }
    return config
  },
}
```

**Root Cause:** Server-side modules being bundled for browser

### Environment Configuration Issues

#### Issue: `Error: Invalid Supabase URL`
**Symptoms:**
- Supabase connection failures
- Authentication errors
- Database query failures

**Diagnostic Flowchart:**
```mermaid
graph TD
    A[Supabase Error] --> B{Check .env.local}
    B -->|Missing| C[Copy from .env.example]
    B -->|Present| D{Check URL format}
    D -->|Invalid| E[Verify Supabase dashboard]
    D -->|Valid| F{Check network}
    F -->|Offline| G[Check internet connection]
    F -->|Online| H[Check Supabase status]
```

**Solution:**
```bash
# 1. Verify environment variables
echo $NEXT_PUBLIC_SUPABASE_URL
echo $SUPABASE_SERVICE_ROLE_KEY

# 2. Test connection
curl -H "apikey: $SUPABASE_SERVICE_ROLE_KEY" \
  "$NEXT_PUBLIC_SUPABASE_URL/rest/v1/products?limit=1"

# 3. Check .env.local format
cat .env.local | grep SUPABASE

# 4. Restart development server
npm run dev
```

## Database & API Issues

### Supabase Connection Problems

#### Issue: `Failed to fetch products: 401 Unauthorized`
**Symptoms:**
- API returns 401 errors
- Authentication failures
- Permission denied errors

**Diagnostic Steps:**
```bash
# 1. Check API key permissions
curl -H "apikey: $SUPABASE_SERVICE_ROLE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_ROLE_KEY" \
  "$NEXT_PUBLIC_SUPABASE_URL/rest/v1/products?limit=1"

# 2. Verify RLS policies
# Check Supabase dashboard > Authentication > Policies

# 3. Test with anon key
curl -H "apikey: $NEXT_PUBLIC_SUPABASE_ANON_KEY" \
  "$NEXT_PUBLIC_SUPABASE_URL/rest/v1/products?limit=1"
```

**Solution:**
```sql
-- Check and fix RLS policies
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'products';

-- Enable RLS if disabled
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Check user permissions
SELECT * FROM auth.users LIMIT 1;
```

#### Issue: `Connection timeout` or `Too many connections`
**Symptoms:**
- Database connection timeouts
- Slow query responses
- Connection pool exhaustion

**Solution:**
```typescript
// src/lib/supabase/server.ts - Connection optimization
export function createServerSupabaseReadOnlyClient() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      },
      global: {
        headers: {
          'X-Client-Info': 'cashback-deals-server',
        },
      },
    }
  )
}

// Add connection pooling
const connectionPool = new Map()

export function getPooledConnection(key: string) {
  if (!connectionPool.has(key)) {
    connectionPool.set(key, createServerSupabaseReadOnlyClient())
  }
  return connectionPool.get(key)
}
```

### Query Performance Issues

#### Issue: Slow database queries
**Symptoms:**
- Page load times > 3 seconds
- API timeouts
- High database CPU usage

**Diagnostic Steps:**
```sql
-- Check query performance
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM products 
WHERE brand_id = 'uuid-here' 
AND status = 'active';

-- Check missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE tablename = 'products'
ORDER BY n_distinct DESC;

-- Check slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE query LIKE '%products%'
ORDER BY mean_time DESC
LIMIT 10;
```

**Solution:**
```sql
-- Add missing indexes
CREATE INDEX CONCURRENTLY idx_products_brand_status 
ON products(brand_id, status);

CREATE INDEX CONCURRENTLY idx_products_featured_created 
ON products(is_featured, created_at DESC);

-- Optimize queries
-- Instead of:
SELECT * FROM products WHERE brand_id = $1;

-- Use:
SELECT id, name, slug, brand_id, price, images 
FROM products 
WHERE brand_id = $1 AND status = 'active'
LIMIT 20;
```

## Search & Pagination Issues

### Search Not Working

#### Issue: `No search results returned`
**Symptoms:**
- Search returns empty results
- Search suggestions not working
- Search API errors

**Diagnostic Flowchart:**
```mermaid
graph TD
    A[Search Issue] --> B{Check API response}
    B -->|200 OK| C{Check query format}
    B -->|400 Error| D[Check input validation]
    B -->|500 Error| E[Check server logs]
    C -->|Valid| F{Check database data}
    C -->|Invalid| G[Fix query encoding]
    F -->|Has data| H[Check search vector]
    F -->|No data| I[Check seed data]
```

**Solution:**
```typescript
// Debug search functionality
export async function debugSearch(query: string) {
  console.log('=== Search Debug ===')
  console.log('Query:', query)
  
  // Test API endpoint
  const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`)
  console.log('API Status:', response.status)
  
  const data = await response.json()
  console.log('API Response:', data)
  
  // Test direct database query
  const { data: dbData, error } = await supabase
    .from('products')
    .select('*')
    .textSearch('name', query)
    .limit(5)
  
  console.log('DB Data:', dbData)
  console.log('DB Error:', error)
}
```

**Database Fix:**
```sql
-- Check search vector
SELECT id, name, search_vector 
FROM products 
WHERE search_vector @@ plainto_tsquery('english', 'laptop')
LIMIT 5;

-- Rebuild search vector if needed
UPDATE products 
SET search_vector = to_tsvector('english', name || ' ' || COALESCE(description, ''))
WHERE search_vector IS NULL;

-- Check search index
REINDEX INDEX products_search_idx;
```

### Pagination State Issues

#### Issue: `Pagination not updating URL correctly`
**Symptoms:**
- URL doesn't reflect current page
- Browser back/forward broken
- Page resets unexpectedly

**Solution:**
```typescript
// Debug pagination hook
export function useDebugPagination() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const debugPagination = () => {
    console.log('=== Pagination Debug ===')
    console.log('Search Params:', searchParams.toString())
    console.log('Current URL:', window.location.href)
    console.log('Router ready:', router)
  }
  
  useEffect(() => {
    debugPagination()
  }, [searchParams])
  
  return { debugPagination }
}

// Fix pagination issues
export function usePagination({
  defaultPage = 1,
  pageSize = 20,
  basePath = ''
}: UsePaginationOptions = {}) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const goToPage = useCallback((page: number) => {
    const params = new URLSearchParams(searchParams?.toString() || '')
    
    // Always remove page=1 to keep URLs clean
    if (page === 1) {
      params.delete('page')
    } else {
      params.set('page', page.toString())
    }
    
    const url = basePath + (params.toString() ? `?${params.toString()}` : '')
    console.log('Navigating to:', url) // Debug log
    
    router.push(url, { scroll: false })
  }, [router, searchParams, basePath])
  
  return { goToPage }
}
```

## Security Issues

### Rate Limiting Problems

#### Issue: `429 Too Many Requests`
**Symptoms:**
- API requests being blocked
- Rate limit exceeded errors
- Legitimate users blocked

**Solution:**
```typescript
// Debug rate limiting
export function debugRateLimit(ip: string, endpoint: string) {
  const key = `${ip}:${endpoint}`
  const data = ipRequestCounts.get(key)
  
  console.log('=== Rate Limit Debug ===')
  console.log('IP:', ip)
  console.log('Endpoint:', endpoint)
  console.log('Current count:', data?.count || 0)
  console.log('Reset time:', data?.resetTime ? new Date(data.resetTime) : 'N/A')
  console.log('Time until reset:', data?.resetTime ? data.resetTime - Date.now() : 'N/A')
}

// Adjust rate limits if needed
export const rateLimits = {
  search: {
    maxRequests: 200, // Increased from 100
    windowSizeInSeconds: 60,
    identifier: 'search'
  },
  // Add IP whitelist for development
  whitelist: ['127.0.0.1', '::1'],
}

export function applyRateLimit(request: NextRequest, config: RateLimitConfig) {
  const ip = getClientIP(request)
  
  // Skip rate limiting for whitelisted IPs
  if (rateLimits.whitelist.includes(ip)) {
    return null
  }
  
  // Continue with normal rate limiting
  // ...
}
```

### Authentication Issues

#### Issue: `JWT verification failed`
**Symptoms:**
- Users getting logged out
- Authentication errors
- Token validation failures

**Solution:**
```typescript
// Debug JWT issues
export async function debugJWT(token: string) {
  console.log('=== JWT Debug ===')
  console.log('Token length:', token.length)
  console.log('Token start:', token.substring(0, 20))
  
  try {
    const decoded = jwtDecode(token)
    console.log('Decoded token:', decoded)
    console.log('Expiry:', new Date(decoded.exp * 1000))
    console.log('Is expired:', Date.now() > decoded.exp * 1000)
  } catch (error) {
    console.log('JWT decode error:', error)
  }
  
  // Test with Supabase
  const { data: { user }, error } = await supabase.auth.getUser(token)
  console.log('Supabase user:', user)
  console.log('Supabase error:', error)
}

// Fix common JWT issues
export function fixJWTIssues() {
  // Clear invalid tokens
  localStorage.removeItem('sb-project-auth-token')
  
  // Refresh session
  supabase.auth.getSession().then(({ data: { session } }) => {
    if (session) {
      console.log('Valid session found')
    } else {
      console.log('No valid session, redirecting to login')
    }
  })
}
```

## Performance Issues

### Slow Page Load Times

#### Issue: `Page takes > 3 seconds to load`
**Symptoms:**
- Poor Core Web Vitals scores
- High Time to Interactive (TTI)
- Large bundle sizes

**Diagnostic Steps:**
```bash
# 1. Run Lighthouse audit
npm run audit:performance

# 2. Check bundle size
npm run build:analyze

# 3. Profile with DevTools
# Open Chrome DevTools > Performance tab > Record

# 4. Check network waterfall
# DevTools > Network tab > Reload page
```

**Solution:**
```typescript
// 1. Optimize images
export function OptimizedImage({ src, alt, ...props }) {
  return (
    <Image
      src={src}
      alt={alt}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,..."
      priority={props.priority}
      sizes="(max-width: 768px) 100vw, 50vw"
      quality={85}
      {...props}
    />
  )
}

// 2. Lazy load components
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <div>Loading...</div>,
  ssr: false,
})

// 3. Optimize database queries
export async function getProducts(filters, page, limit) {
  // Use specific field selection
  const { data, error } = await supabase
    .from('products')
    .select('id, name, slug, price, images, brand:brands(name, logo_url)')
    .eq('status', 'active')
    .range((page - 1) * limit, page * limit - 1)
  
  return data
}
```

### Memory Leaks

#### Issue: `Memory usage keeps increasing`
**Symptoms:**
- Browser becomes slow over time
- Tab crashes
- High memory usage in DevTools

**Solution:**
```typescript
// Fix memory leaks
export function useMemoryOptimization() {
  const [data, setData] = useState(null)
  const abortController = useRef(new AbortController())
  
  useEffect(() => {
    // Cleanup on unmount
    return () => {
      abortController.current.abort()
      setData(null)
    }
  }, [])
  
  const fetchData = useCallback(async () => {
    try {
      const response = await fetch('/api/data', {
        signal: abortController.current.signal
      })
      const result = await response.json()
      setData(result)
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Fetch error:', error)
      }
    }
  }, [])
  
  return { data, fetchData }
}

// Clean up event listeners
export function useEventListener(event: string, handler: Function) {
  useEffect(() => {
    window.addEventListener(event, handler)
    return () => window.removeEventListener(event, handler)
  }, [event, handler])
}
```

## Deployment Issues

### Vercel Deployment Failures

#### Issue: `Build fails on Vercel but works locally`
**Symptoms:**
- Local build succeeds
- Vercel deployment fails
- Environment-specific errors

**Solution:**
```bash
# 1. Check Vercel build logs
vercel logs --follow

# 2. Test with production environment locally
NODE_ENV=production npm run build
NODE_ENV=production npm start

# 3. Check environment variables
vercel env ls

# 4. Debug with Vercel CLI
vercel dev --debug
```

**Common Fixes:**
```javascript
// next.config.js - Handle environment differences
module.exports = {
  // Ignore build errors during deployment
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },
  
  // Handle missing environment variables
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY || 'default-value',
  },
  
  // Fix serverless function issues
  experimental: {
    outputFileTracingIncludes: {
      '/api/**/*': ['./node_modules/**/*.wasm'],
    },
  },
}
```

### SSL Certificate Issues

#### Issue: `SSL certificate verification failed`
**Symptoms:**
- HTTPS errors
- Mixed content warnings
- Certificate validation failures

**Solution:**
```bash
# 1. Check certificate status
curl -I https://your-domain.com

# 2. Test SSL configuration
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# 3. Check DNS configuration
nslookup your-domain.com
dig your-domain.com

# 4. Force SSL renewal (if using Let's Encrypt)
# Contact hosting provider or check Vercel domain settings
```

## Testing Issues

### Test Failures

#### Issue: `Tests pass locally but fail in CI`
**Symptoms:**
- Local tests succeed
- CI pipeline fails
- Environment-specific test failures

**Solution:**
```bash
# 1. Check CI environment
echo $NODE_ENV
echo $CI

# 2. Run tests with same conditions as CI
NODE_ENV=test npm run test

# 3. Check for timing issues
npm run test -- --runInBand

# 4. Debug test environment
npm run test -- --verbose
```

**Common Fixes:**
```typescript
// Fix timing issues
test('should load data', async () => {
  render(<Component />)
  
  // Wait for async operations
  await waitFor(() => {
    expect(screen.getByText('Data loaded')).toBeInTheDocument()
  }, { timeout: 5000 })
})

// Fix environment issues
beforeEach(() => {
  // Reset mocks
  jest.clearAllMocks()
  
  // Set environment
  process.env.NODE_ENV = 'test'
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
})
```

## Quick Reference

### Common Commands

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Debugging
npm run lint            # Check code quality
npm run test           # Run tests
npm run typecheck      # Check TypeScript

# Performance
npm run audit:performance  # Performance audit
npm run build:analyze     # Bundle analysis

# Database
npm run db:reset          # Reset database
npm run db:seed          # Seed database
npm run db:migrate       # Run migrations
```

### Environment Variables Checklist

```bash
# Required for development
✓ NEXT_PUBLIC_SUPABASE_URL
✓ NEXT_PUBLIC_SUPABASE_ANON_KEY
✓ SUPABASE_SERVICE_ROLE_KEY

# Required for production
✓ All development variables
✓ NEXT_PUBLIC_SITE_URL
✓ TURNSTILE_SECRET_KEY
✓ EMAIL_SERVER credentials

# Optional
○ DEBUG flags
○ Analytics keys
○ Monitoring tokens
```

### Log Analysis

```bash
# Check application logs
tail -f .next/server.log

# Check error logs
grep -i error .next/server.log

# Check performance logs
grep -i "slow" .next/server.log

# Check security logs
grep -i "rate limit\|security" .next/server.log
```

## Escalation Procedures

### When to Escalate

1. **Critical Issues (Immediate Escalation)**
   - Production site down
   - Security breaches
   - Data loss
   - Payment system failures

2. **High Priority Issues (4-hour window)**
   - API endpoints returning 500 errors
   - Database connection failures
   - Authentication system down
   - Major performance degradation

3. **Medium Priority Issues (24-hour window)**
   - Search functionality broken
   - Pagination issues
   - Minor performance issues
   - Non-critical feature failures

### Escalation Contacts

```yaml
# Internal escalation
Level 1: Development Team
Level 2: Lead Developer
Level 3: Technical Architect
Level 4: CTO

# External escalation
Database: Supabase Support
Hosting: Vercel Support
CDN: Cloudflare Support
Monitoring: Third-party service providers
```

## Next Steps / TODO

- [ ] Add automated error detection and alerting
- [ ] Implement comprehensive logging system
- [ ] Add performance monitoring dashboard
- [ ] Create automated troubleshooting scripts
- [ ] Add chaos engineering for resilience testing
- [ ] Implement advanced debugging tools
- [ ] Add comprehensive monitoring and alerting
- [ ] Create runbooks for common operational tasks
- [ ] Add automated recovery procedures
- [ ] Implement comprehensive backup and restore procedures
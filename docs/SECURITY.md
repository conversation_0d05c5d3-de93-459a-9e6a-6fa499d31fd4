# Security Implementation & Guidelines

*This file is auto-generated documentation for security measures and implementation. Last updated: January 2025*

## Security Overview

The Cashback Deals platform implements a comprehensive security-first approach with multiple layers of protection against common web vulnerabilities, including XSS, CSRF, injection attacks, and unauthorized access.

## Security Architecture

### Defense in Depth Strategy

```mermaid
graph TB
    User[User Request] --> CF[Cloudflare Protection]
    CF --> CSP[Content Security Policy]
    CSP --> Headers[HTTP Security Headers]
    Headers --> Rate[Rate Limiting]
    Rate --> Validation[Input Validation]
    Validation --> Auth[Authentication]
    Auth --> RLS[Row Level Security]
    RLS --> Audit[Audit Logging]
    
    subgraph "Layer 1: Network"
        CF
    end
    
    subgraph "Layer 2: Application"
        CSP
        Headers
        Rate
    end
    
    subgraph "Layer 3: Input"
        Validation
    end
    
    subgraph "Layer 4: Access"
        Auth
        RLS
    end
    
    subgraph "Layer 5: Monitoring"
        Audit
    end
```

## Authentication & Authorization

### Dual Authentication Architecture

The application supports both JWT and HMAC authentication for API endpoints:

- **JWT Authentication**: Used for user-facing features (contact forms, user actions)
- **HMAC Authentication**: Used for partner API access with cryptographic signatures
- **Public Endpoints**: Search suggestions are public with IP-based rate limiting

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant P as Partner
    participant A as API Route
    participant S as Supabase Auth
    participant DB as Database
    
    Note over U,DB: JWT Authentication Flow
    U->>F: User Action
    F->>S: Get JWT Token
    S->>F: Return JWT
    F->>A: API Request + JWT Bearer Token
    A->>A: Validate JWT
    A->>DB: Query with User Context
    DB->>A: User-specific Results
    A->>F: Response Data
    
    Note over P,DB: HMAC Authentication Flow
    P->>P: Generate HMAC Signature
    P->>A: API Request + HMAC Headers
    A->>A: Verify HMAC Signature
    A->>A: Check Replay Protection
    A->>DB: Query with Partner Context
    DB->>A: Partner-specific Results
    A->>P: Response Data
    
    Note over U,A: Public Endpoint (Suggestions)
    U->>A: Search Suggestions Request
    A->>A: Apply IP Rate Limiting
    A->>DB: Query Public Data
    DB->>A: Public Results
    A->>U: Suggestions Response
```

### Authentication Decision Matrix

| Endpoint | Authentication | Rate Limiting | Notes |
|----------|---------------|---------------|-------|
| `/api/search` | JWT OR HMAC | Standard | Full search functionality |
| `/api/search/more` | JWT OR HMAC | Standard | Load more results |
| `/api/search/suggestions` | **Public** | IP-based (10 req/sec) | Better UX, non-sensitive data |
| `/api/contact` | JWT + Turnstile | Strict (5 req/10min) | Contact form protection |
| `/api/auth/*` | Public | Standard | Authentication endpoints |

### JWT Token Management

```typescript
// src/lib/security/jwt.ts
import { jwtVerify, SignJWT } from 'jose'
import { NextRequest } from 'next/server'

const secret = new TextEncoder().encode(process.env.JWT_SECRET!)

export async function verifyJWT(token: string) {
  try {
    const { payload } = await jwtVerify(token, secret)
    return payload
  } catch (error) {
    console.error('JWT verification failed:', error)
    return null
  }
}

export async function createJWT(payload: any) {
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('24h')
    .setIssuedAt()
    .sign(secret)
}

// Extract JWT from request headers
export function getJWTFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('Authorization')
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }
  
  // Also check cookies for client-side requests
  const cookieToken = request.cookies.get('supabase-auth-token')
  return cookieToken?.value || null
}
```

### HMAC Authentication

HMAC (Hash-based Message Authentication Code) provides cryptographically secure API access for partners:

```typescript
// src/lib/security/hmac.ts
import crypto from 'crypto'

export function generateHMACSignature(
  method: string,
  path: string,
  timestamp: number,
  body: string = '',
  secret: string
): string {
  // Create SHA256 hash of request body
  const bodyHash = crypto.createHash('sha256').update(body).digest('hex')
  
  // Create message string for signing
  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`
  
  // Generate HMAC signature
  return crypto
    .createHmac('sha256', secret)
    .update(message)
    .digest('hex')
}

export function verifyHMACSignature(
  signature: string,
  method: string,
  path: string,
  timestamp: number,
  body: string = '',
  secret: string
): boolean {
  const expectedSignature = generateHMACSignature(method, path, timestamp, body, secret)
  
  // Use constant-time comparison to prevent timing attacks
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  )
}
```

#### HMAC Request Headers

Partners must include the following headers for HMAC authentication:

```typescript
const headers = {
  'X-Signature': `sha256=${signature}`,
  'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
  'X-Partner-ID': 'your-partner-id',
  'Content-Type': 'application/json'
}
```

#### HMAC Security Features

- **Timing-safe comparison**: Prevents timing attacks
- **Replay protection**: 5-minute timestamp window with duplicate request detection
- **Partner isolation**: Each partner has unique secrets via environment variables
- **Automatic cleanup**: Memory management for replay cache
- **Comprehensive logging**: Security events tracked for audit purposes

### Row Level Security (RLS) Policies

#### User Data Protection
```sql
-- Users can only access their own data
CREATE POLICY "Users can view own profile"
    ON public.users FOR SELECT
    TO authenticated
    USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
    ON public.users FOR UPDATE
    TO authenticated
    USING (auth.uid() = id);

-- User purchases are private
CREATE POLICY "Users can view own purchases"
    ON public.user_purchases FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can create purchase records"
    ON public.user_purchases FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());
```

#### Public Data Access
```sql
-- Products are viewable by everyone (only active ones)
CREATE POLICY "Products viewable by everyone"
    ON public.products FOR SELECT
    TO anon
    USING (status = 'active');

-- Brands are public
CREATE POLICY "Brands viewable by everyone"
    ON public.brands FOR SELECT
    TO anon
    USING (true);

-- Promotions are public but filtered
CREATE POLICY "Active promotions viewable by everyone"
    ON public.promotions FOR SELECT
    TO anon
    USING (status = 'active' AND purchase_end_date >= current_date);
```

#### Administrative Access
```sql
-- Staff can view audit logs
CREATE POLICY "Staff can view audit logs"
    ON public.audit_log FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM auth.users
            WHERE auth.uid() = id
            AND user_metadata->>'role' = 'staff'
        )
    );

-- Admin-only access to sensitive data
CREATE POLICY "Admin can manage all data"
    ON public.products FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM auth.users
            WHERE auth.uid() = id
            AND user_metadata->>'role' = 'admin'
        )
    );
```

## Input Validation & Sanitization

### Zod Schema Validation

```typescript
// src/lib/validation/schemas.ts
import { z } from 'zod'

// Security-focused validation patterns
const SUSPICIOUS_PATTERNS = [
  /<script/i,
  /javascript:/i,
  /vbscript:/i,
  /on\w+=/i,
  /<iframe/i,
  /eval\(/i,
  /alert\(/i,
  /document\./i,
  /window\./i,
  /select\s+.*\s+from/i,
  /union\s+select/i,
  /insert\s+into/i,
  /delete\s+from/i,
  /drop\s+table/i,
]

export const safeString = z.string().refine((val) => {
  return !SUSPICIOUS_PATTERNS.some(pattern => pattern.test(val))
}, 'Input contains potentially dangerous content')

export const searchQuery = z.string()
  .min(1, 'Search query is required')
  .max(200, 'Search query too long')
  .refine((val) => {
    return !SUSPICIOUS_PATTERNS.some(pattern => pattern.test(val))
  }, 'Search query contains suspicious content')

// Email validation with security checks
export const email = z.string()
  .email('Invalid email format')
  .max(255, 'Email too long')
  .refine((val) => {
    const suspiciousPatterns = [
      /javascript:/i,
      /vbscript:/i,
      /<.*>/,
      /script/i,
      /\.\./,
    ]
    return !suspiciousPatterns.some(pattern => pattern.test(val))
  }, 'Email contains suspicious content')
```

### API Input Validation

```typescript
// src/app/api/search/route.ts
import { validateInput, searchApiSchema } from '@/lib/validation/schemas'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const params = Object.fromEntries(searchParams)
    
    // Validate all input parameters
    const validation = validateInput(searchApiSchema, params)
    
    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid input parameters',
          details: validation.details
        },
        { status: 400 }
      )
    }
    
    // Use validated data
    const { q, category, brand, sort, page, limit } = validation.data
    
    // Continue with validated inputs only
    const results = await searchProducts(supabase, {
      query: q,
      category,
      brand,
      sortBy: sort
    }, page, limit)
    
    return NextResponse.json(results)
  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
```

### XSS Prevention

```typescript
// src/lib/security/xss.ts
import DOMPurify from 'isomorphic-dompurify'

interface SanitizeOptions {
  allowedTags?: string[]
  allowedAttributes?: Record<string, string[]>
  stripIgnoreTag?: boolean
}

export function sanitizeHtml(
  dirty: string,
  options: SanitizeOptions = {}
): string {
  const config = {
    ALLOWED_TAGS: options.allowedTags || ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: options.allowedAttributes || {
      '*': ['class'],
      'a': ['href', 'title'],
      'img': ['src', 'alt', 'title'],
    },
    STRIP_IGNORE_TAG: options.stripIgnoreTag !== false,
    REMOVE_SCRIPT_TAG: true,
    REMOVE_COMMENTS: true,
  }
  
  return DOMPurify.sanitize(dirty, config)
}

// React component for safe HTML rendering
export function SafeHtml({ 
  html, 
  className = '',
  options = {} 
}: {
  html: string
  className?: string
  options?: SanitizeOptions
}) {
  const sanitizedHtml = sanitizeHtml(html, options)
  
  return (
    <div 
      className={className}
      dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
    />
  )
}
```

## HTTP Security Headers

### Content Security Policy (CSP)

```typescript
// next.config.js - Security Headers Configuration
const getSecurityHeaders = () => {
  const cspDirectives = {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "https://challenges.cloudflare.com", // Turnstile
      ...(isDevelopment ? ["'unsafe-eval'", "'unsafe-inline'"] : [])
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'" // Required for Tailwind CSS
    ],
    'img-src': [
      "'self'",
      "data:",
      "https://*.supabase.co",
      "https://images.samsung.com",
      "https://placehold.co",
      "https://*.amazonaws.com",
      "https://*.cloudfront.net"
    ],
    'connect-src': [
      "'self'",
      "https://*.supabase.co",
      "wss://*.supabase.co"
    ],
    'font-src': ["'self'", "data:"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-src': ["https://challenges.cloudflare.com"],
    'upgrade-insecure-requests': []
  }
  
  const cspString = Object.entries(cspDirectives)
    .map(([directive, sources]) => {
      if (sources.length === 0) return directive
      return `${directive} ${sources.join(' ')}`
    })
    .join('; ')
  
  return [
    {
      key: 'Content-Security-Policy',
      value: cspString
    },
    {
      key: 'X-Content-Type-Options',
      value: 'nosniff'
    },
    {
      key: 'X-Frame-Options',
      value: 'DENY'
    },
    {
      key: 'X-XSS-Protection',
      value: '1; mode=block'
    },
    {
      key: 'Referrer-Policy',
      value: 'strict-origin-when-cross-origin'
    },
    {
      key: 'Permissions-Policy',
      value: 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), browsing-topics=()'
    }
  ]
}
```

### HSTS and Security Headers

```typescript
// Production-only security headers
if (isProduction || isStaging) {
  baseHeaders.push({
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  })
  
  baseHeaders.push({
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  })
}

// API-specific headers
const apiHeaders = [
  {
    key: 'Cache-Control',
    value: 'public, s-maxage=1800, stale-while-revalidate=3600'
  },
  {
    key: 'Access-Control-Allow-Origin',
    value: process.env.NODE_ENV === 'production' 
      ? 'https://yourdomain.com' 
      : '*'
  },
  {
    key: 'Access-Control-Allow-Methods',
    value: 'GET, POST, PUT, DELETE, OPTIONS'
  },
  {
    key: 'Access-Control-Allow-Headers',
    value: 'Content-Type, Authorization'
  }
]
```

## Rate Limiting & DoS Protection

### Dual Rate Limiting Strategy

The application implements two types of rate limiting:

1. **Authentication-based rate limiting**: For protected endpoints
2. **IP-based rate limiting**: For public endpoints

### IP-based Rate Limiting (Public Endpoints)

```typescript
// src/lib/security/ip-rate-limiter.ts
interface IPRateLimitConfig {
  requestsPerSecond: number
  windowSeconds: number
}

export const SUGGESTIONS_RATE_LIMIT: IPRateLimitConfig = {
  requestsPerSecond: 10,  // 10 requests per second
  windowSeconds: 60       // 1-minute window = 600 total requests
}

export function applyIPRateLimit(
  request: NextRequest, 
  config: IPRateLimitConfig
): NextResponse | null {
  const ip = getClientIP(request)
  const now = Date.now()
  const windowMs = config.windowSeconds * 1000
  
  // Get or create rate limit entry for this IP
  let entry = ipRateLimits.get(ip)
  
  if (!entry || now - entry.lastReset > windowMs) {
    entry = { requests: 0, lastReset: now }
    ipRateLimits.set(ip, entry)
  }
  
  const maxRequests = config.requestsPerSecond * config.windowSeconds
  
  if (entry.requests >= maxRequests) {
    return NextResponse.json(
      {
        error: 'Rate limit exceeded',
        message: `Too many requests. Limit: ${config.requestsPerSecond} requests per second.`,
        retryAfter: Math.ceil((entry.lastReset + windowMs - now) / 1000)
      },
      { status: 429 }
    )
  }
  
  entry.requests++
  return null
}
```

### Authentication-based Rate Limiting

```typescript
// src/lib/rateLimiter.ts
interface RateLimitConfig {
  maxRequests: number
  windowSizeInSeconds: number
  identifier?: string
}

// Rate limit configurations by endpoint
export const rateLimits = {
  search: {
    maxRequests: 100,
    windowSizeInSeconds: 60,
    identifier: 'search'
  },
  contact: {
    maxRequests: 5,
    windowSizeInSeconds: 600, // 10 minutes
    identifier: 'contact'
  },
  products: {
    maxRequests: 30,
    windowSizeInSeconds: 60,
    identifier: 'products'
  }
}

export function applyRateLimit(
  request: NextRequest,
  config: RateLimitConfig
): NextResponse | null {
  const ip = getClientIP(request)
  const rateLimitKey = `${ip}:${config.identifier}`
  
  let requestData = ipRequestCounts.get(rateLimitKey)
  const now = Date.now()
  
  if (!requestData || now > requestData.resetTime) {
    requestData = {
      count: 0,
      resetTime: now + (config.windowSizeInSeconds * 1000)
    }
  }
  
  requestData.count++
  ipRequestCounts.set(rateLimitKey, requestData)
  
  if (requestData.count > config.maxRequests) {
    const timeUntilReset = Math.ceil((requestData.resetTime - now) / 1000)
    
    // Log rate limit exceeded
    console.warn(`Rate limit exceeded for ${ip} on ${config.identifier}`)
    
    return new NextResponse(
      JSON.stringify({
        error: 'Too many requests',
        message: `Rate limit exceeded. Try again in ${timeUntilReset} seconds.`
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Limit': config.maxRequests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': Math.ceil(requestData.resetTime / 1000).toString(),
          'Retry-After': timeUntilReset.toString()
        }
      }
    )
  }
  
  return null
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfIP = request.headers.get('cf-connecting-ip')
  
  return cfIP || realIP || forwarded?.split(',')[0].trim() || 'unknown'
}
```

### Cloudflare Turnstile Integration

```typescript
// src/lib/security/turnstile.ts
export async function verifyTurnstileToken(token: string, ip: string): Promise<boolean> {
  try {
    const response = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        secret: process.env.TURNSTILE_SECRET_KEY,
        response: token,
        remoteip: ip,
      }),
    })
    
    const data = await response.json()
    
    if (data.success) {
      return true
    } else {
      console.warn('Turnstile verification failed:', data['error-codes'])
      return false
    }
  } catch (error) {
    console.error('Turnstile verification error:', error)
    return false
  }
}

// Usage in API routes
export async function POST(request: NextRequest) {
  const { turnstileToken, ...formData } = await request.json()
  const clientIP = getClientIP(request)
  
  // Verify Turnstile token
  const isValidToken = await verifyTurnstileToken(turnstileToken, clientIP)
  
  if (!isValidToken) {
    return NextResponse.json(
      { error: 'Invalid security token' },
      { status: 400 }
    )
  }
  
  // Process form submission
  // ...
}
```

## Database Security

### Connection Security

```typescript
// src/lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'

export function createServerSupabaseReadOnlyClient() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        // Disable cookie handling for server-side client
        get: () => undefined,
        set: () => {},
        remove: () => {},
      },
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      },
    }
  )
}

// Separate client for authenticated operations
export function createServerSupabaseClient() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        get: (name) => cookies().get(name)?.value,
        set: (name, value, options) => cookies().set(name, value, options),
        remove: (name, options) => cookies().delete(name),
      },
    }
  )
}
```

### SQL Injection Prevention

```typescript
// src/lib/data/products.ts
// Always use parameterized queries
export async function getProductsByBrand(
  supabase: SupabaseClient,
  brandId: string,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<TransformedProduct>> {
  const offset = (page - 1) * limit
  
  // Parameterized query - prevents SQL injection
  const { data, error, count } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url),
      category:category_id (id, name, slug)
    `, { count: 'exact' })
    .eq('brand_id', brandId) // Parameterized
    .eq('status', 'active')
    .range(offset, offset + limit - 1)
    .order('created_at', { ascending: false })
  
  if (error) {
    console.error('Database error:', error)
    throw new Error('Failed to fetch products')
  }
  
  return {
    data: data.map(transformProduct),
    pagination: {
      page,
      pageSize: limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit),
      hasNext: page < Math.ceil((count || 0) / limit),
      hasPrev: page > 1
    }
  }
}
```

### Database Audit Logging

```sql
-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_changes()
RETURNS trigger AS $$
BEGIN
    INSERT INTO audit_log (
        table_name,
        record_id,
        operation,
        old_data,
        new_data,
        changed_by
    )
    VALUES (
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN row_to_json(NEW) ELSE NULL END,
        auth.uid()
    );
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER audit_products
    AFTER INSERT OR UPDATE OR DELETE ON products
    FOR EACH ROW EXECUTE FUNCTION audit_changes();

CREATE TRIGGER audit_users
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION audit_changes();
```

## Secrets Management

### Environment Variables

```bash
# Production secrets (never commit these)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
JWT_SECRET=your-jwt-secret-key-minimum-32-characters
TURNSTILE_SECRET_KEY=your-turnstile-secret

# Authentication feature flags
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true

# HMAC configuration
HMAC_TIMESTAMP_WINDOW=300
PARTNER_SECRET_DEFAULT=your-default-partner-secret-minimum-32-chars
PARTNER_SECRET_PARTNER1=partner1-specific-secret-minimum-32-chars
PARTNER_SECRET_PARTNER2=partner2-specific-secret-minimum-32-chars

# Email configuration
EMAIL_SERVER=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# API keys
STRIPE_SECRET_KEY=sk_live_...
WEBHOOK_SECRET=whsec_...
```

#### Environment Variable Security Requirements

- **JWT_SECRET**: Minimum 32 characters, cryptographically random
- **PARTNER_SECRET_***: Minimum 32 characters, unique per partner
- **HMAC_TIMESTAMP_WINDOW**: Seconds (default: 300 = 5 minutes)
- **Feature flags**: Use 'true'/'false' strings for authentication control

### Key Rotation Procedures

```typescript
// src/lib/security/keyRotation.ts
interface KeyRotationConfig {
  keyId: string
  rotationIntervalDays: number
  notificationEmail: string
}

export async function rotateJWTSecret(oldSecret: string, newSecret: string) {
  // 1. Update environment variable
  process.env.JWT_SECRET = newSecret
  
  // 2. Invalidate all existing tokens
  await invalidateAllTokens()
  
  // 3. Log rotation event
  await logSecurityEvent('JWT_SECRET_ROTATED', {
    timestamp: new Date().toISOString(),
    rotatedBy: 'system'
  })
  
  // 4. Send notification
  await sendSecurityNotification(
    'JWT secret has been rotated',
    'All users will need to re-authenticate'
  )
}

export async function checkKeyRotationSchedule() {
  const lastRotation = await getLastKeyRotation('JWT_SECRET')
  const daysSinceRotation = getDaysSince(lastRotation)
  
  if (daysSinceRotation >= 90) { // Rotate every 90 days
    await scheduleKeyRotation('JWT_SECRET')
  }
}
```

### CI/CD Secrets Management

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
        
    - name: Run security checks
      env:
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        TURNSTILE_SECRET_KEY: ${{ secrets.TURNSTILE_SECRET_KEY }}
      run: |
        npm run test:security
        npm run audit:security
```

## Security Monitoring & Incident Response

### Security Event Logging

```typescript
// src/lib/security/hmac.ts
interface SecurityEvent {
  type: 'HMAC_AUTH_SUCCESS' | 'HMAC_AUTH_FAILURE' | 'JWT_AUTH_SUCCESS' | 'JWT_AUTH_FAILURE'
  endpoint: string
  method: string
  partnerId?: string
  ip: string
  timestamp: string
  traceId: string
  error?: HMACError
  errorMessage?: string
}

export function logSecurityEvent(event: SecurityEvent): void {
  // Downgrade log level for suggestions endpoint to DEBUG to reduce log noise
  let level = event.type.includes('FAILURE') ? 'WARN' : 'INFO'
  if (event.endpoint === '/api/search/suggestions' && event.type.includes('FAILURE')) {
    level = 'DEBUG'
  }
  
  const logEntry = {
    level,
    message: `Security Event: ${event.type}`,
    traceId: event.traceId,
    endpoint: event.endpoint,
    method: event.method,
    partnerId: event.partnerId,
    ip: event.ip,
    timestamp: event.timestamp,
    error: event.error,
    errorMessage: event.errorMessage
  }

  // Only log DEBUG level in development
  if (level === 'DEBUG' && process.env.NODE_ENV === 'production') {
    return
  }

  // Log to CloudWatch via console (structured logging)
  console.log(JSON.stringify(logEntry))
}

// Generate trace ID for request correlation
export function generateTraceId(method?: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 9)
  const methodPrefix = method ? `${method.toLowerCase()}-` : ''
  const shortHash = timestamp.toString(36).substring(-4)
  return `hmac-${methodPrefix}${shortHash}-${random}`
}
```

#### Security Logging Features

- **Structured logging**: JSON format for easy parsing by log aggregators
- **Trace IDs**: Correlation across multiple requests and services
- **Smart log levels**: DEBUG level for suggestions endpoint to reduce noise
- **Comprehensive context**: IP, endpoint, method, partner ID, error details
- **Production optimization**: DEBUG logs filtered out in production

### Automated Security Scanning

```typescript
// src/lib/security/scanning.ts
export async function runSecurityScan() {
  const results = {
    vulnerabilities: [],
    recommendations: [],
    score: 0
  }
  
  // Check for common vulnerabilities
  results.vulnerabilities.push(...await scanForXSS())
  results.vulnerabilities.push(...await scanForSQLInjection())
  results.vulnerabilities.push(...await scanForCSRF())
  
  // Check security headers
  results.vulnerabilities.push(...await scanSecurityHeaders())
  
  // Check dependencies
  results.vulnerabilities.push(...await scanDependencies())
  
  // Calculate security score
  results.score = calculateSecurityScore(results.vulnerabilities)
  
  return results
}

export async function scanForXSS(): Promise<SecurityVulnerability[]> {
  const vulnerabilities: SecurityVulnerability[] = []
  
  // Check for dangerouslySetInnerHTML usage
  const dangerousUsage = await findDangerouslySetInnerHTML()
  if (dangerousUsage.length > 0) {
    vulnerabilities.push({
      type: 'XSS_RISK',
      severity: 'HIGH',
      description: 'Unsafe HTML rendering detected',
      files: dangerousUsage
    })
  }
  
  return vulnerabilities
}
```

## Incident Response Procedures

### Security Incident Classification

| Severity | Examples | Response Time | Actions |
|----------|----------|---------------|---------|
| **Critical** | Data breach, SQL injection, XSS exploitation | 15 minutes | Immediate containment, disable affected systems |
| **High** | Authentication bypass, privilege escalation | 1 hour | Investigate, patch, monitor |
| **Medium** | Rate limiting bypass, suspicious activity | 4 hours | Review logs, implement fixes |
| **Low** | Configuration issues, minor vulnerabilities | 24 hours | Schedule fix, update documentation |

### Incident Response Checklist

#### Immediate Response (0-15 minutes)
- [ ] **Identify** the security incident
- [ ] **Classify** severity level
- [ ] **Contain** the threat (disable systems if needed)
- [ ] **Notify** security team
- [ ] **Document** initial findings

#### Short-term Response (15 minutes - 1 hour)
- [ ] **Investigate** the extent of the breach
- [ ] **Preserve** evidence and logs
- [ ] **Implement** temporary fixes
- [ ] **Notify** stakeholders
- [ ] **Monitor** for continued threats

#### Medium-term Response (1-24 hours)
- [ ] **Develop** permanent fixes
- [ ] **Test** security patches
- [ ] **Deploy** fixes to production
- [ ] **Update** security policies
- [ ] **Conduct** post-incident review

#### Long-term Response (24+ hours)
- [ ] **Analyze** root cause
- [ ] **Implement** preventive measures
- [ ] **Update** security training
- [ ] **Review** security architecture
- [ ] **Document** lessons learned

### Communication Templates

```typescript
// src/lib/security/templates.ts
export const SECURITY_INCIDENT_TEMPLATE = `
SECURITY INCIDENT ALERT

Severity: {{severity}}
Incident Type: {{type}}
Detected At: {{timestamp}}
Affected Systems: {{systems}}

Description:
{{description}}

Immediate Actions Taken:
{{actions}}

Current Status: {{status}}

Contact: <EMAIL>
Reference: {{incidentId}}
`

export const SECURITY_PATCH_TEMPLATE = `
SECURITY PATCH NOTIFICATION

Patch ID: {{patchId}}
Release Date: {{releaseDate}}
Severity: {{severity}}

Vulnerabilities Fixed:
{{vulnerabilities}}

Deployment Schedule:
- Staging: {{stagingDate}}
- Production: {{productionDate}}

Downtime Expected: {{downtime}}
`
```

## Security Testing & Validation

### Automated Security Tests

```typescript
// src/__tests__/security/comprehensive.test.ts
describe('Comprehensive Security Tests', () => {
  describe('Input Validation', () => {
    it('prevents XSS attacks', async () => {
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        '<img src="x" onerror="alert(\'xss\')">',
        'vbscript:alert("xss")'
      ]
      
      for (const input of maliciousInputs) {
        const result = await validateInput(searchApiSchema, { q: input })
        expect(result.success).toBe(false)
      }
    })
    
    it('prevents SQL injection', async () => {
      const sqlInjectionInputs = [
        "'; DROP TABLE products; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --"
      ]
      
      for (const input of sqlInjectionInputs) {
        const result = await validateInput(searchApiSchema, { q: input })
        expect(result.success).toBe(false)
      }
    })
  })
  
  describe('Authentication Security', () => {
    it('rejects invalid JWT tokens', async () => {
      const invalidTokens = [
        'invalid.token.here',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature',
        ''
      ]
      
      for (const token of invalidTokens) {
        const result = await verifyJWT(token)
        expect(result).toBeNull()
      }
    })
    
    it('enforces rate limiting', async () => {
      const request = new NextRequest('http://localhost/api/search', {
        headers: { 'x-forwarded-for': '***********' }
      })
      
      // Exceed rate limit
      for (let i = 0; i <= rateLimits.search.maxRequests; i++) {
        const result = applyRateLimit(request, rateLimits.search)
        
        if (i < rateLimits.search.maxRequests) {
          expect(result).toBeNull()
        } else {
          expect(result?.status).toBe(429)
        }
      }
    })
  })
})
```

## Best Practices Summary

### Development Security Guidelines

1. **Input Validation**: Always validate and sanitize user inputs
2. **Authentication**: Use strong authentication mechanisms
3. **Authorization**: Implement proper access controls
4. **Secrets Management**: Never commit secrets to version control
5. **HTTPS**: Always use HTTPS in production
6. **Security Headers**: Implement comprehensive security headers
7. **Rate Limiting**: Protect against abuse and DoS attacks
8. **Audit Logging**: Log all security-relevant events
9. **Regular Updates**: Keep dependencies updated
10. **Security Testing**: Implement comprehensive security testing

### Security Review Checklist

- [ ] **Input validation** implemented for all user inputs
- [ ] **Authentication** properly configured and tested
- [ ] **Authorization** controls in place and verified
- [ ] **Rate limiting** implemented on all API endpoints
- [ ] **Security headers** configured correctly
- [ ] **Secrets** properly managed and rotated
- [ ] **Audit logging** enabled for security events
- [ ] **Dependencies** scanned for vulnerabilities
- [ ] **Security tests** passing
- [ ] **Incident response** procedures documented

## Next Steps / TODO

- [ ] Implement automated vulnerability scanning
- [ ] Add penetration testing procedures
- [ ] Implement security awareness training
- [ ] Add automated security patch management
- [ ] Implement WAF (Web Application Firewall)
- [ ] Add DDoS protection and monitoring
- [ ] Implement security metrics dashboard
- [ ] Add automated incident response workflows
- [ ] Implement security compliance reporting
- [ ] Add advanced threat detection and response
# PR 2 Implementation Plan - HM<PERSON> Helper + Search Routes Authentication

**Date:** January 12, 2025  
**Sprint:** Auth Layer Implementation  
**PR Scope:** HMAC authentication helper + protect search endpoints  
**Estimated Effort:** 8-12 hours development + testing  

## Executive Summary

This implementation plan details PR 2 of the Auth Sprint, focusing on creating an HMAC authentication system and protecting search endpoints with dual authentication support (JWT or HMAC). Building on the successful JWT implementation from PR 1, this PR will secure search APIs against scraping and abuse while maintaining backward compatibility.

## 1. Technical Analysis & Architecture

### Current State Assessment

#### Search Endpoints Identified
- **`/api/search/route.ts`** - Main search API with filtering and pagination
- **`/api/search/suggestions/route.ts`** - Search suggestions and autocomplete
- **`/api/search/more/route.ts`** - Additional search results pagination (missing from original scope)

#### Current Security Status
- ✅ **Rate Limiting**: All endpoints have rate limiting implemented
- ✅ **Input Validation**: Zod schemas for parameter validation
- ❌ **Authentication**: No authentication required (public access)
- ❌ **Abuse Protection**: Vulnerable to systematic scraping

#### JWT Infrastructure (From PR 1)
- **JWT Helper**: `src/lib/security/jwt.ts` with dual transport (cookies + headers)
- **Verification Functions**: `verifyRequestJWT()`, `createUnauthorizedResponse()`
- **Cookie Management**: Secure HttpOnly cookies with proper expiration
- **Test Coverage**: Comprehensive test suite with 15/15 tests passing

### HMAC Authentication Design

#### Why HMAC for Search APIs?
1. **Partner Integration**: Allows API partners to authenticate without browser sessions
2. **Stateless**: No server-side session storage required
3. **Timestamp Protection**: Prevents replay attacks with time-based validation
4. **Signature Verification**: Cryptographic proof of request authenticity

#### HMAC vs JWT Decision Matrix
| Use Case | Authentication Method | Rationale |
|----------|----------------------|-----------|
| **Browser Users** | JWT (from PR 1) | Session-based, CAPTCHA-protected |
| **API Partners** | HMAC | Stateless, programmatic access |
| **Internal Services** | HMAC | Service-to-service authentication |
| **Mobile Apps** | JWT or HMAC | Flexible based on implementation |

### Architecture Overview

```mermaid
graph TB
    Client[Client Request] --> Auth{Authentication Check}
    Auth -->|JWT Present| JWT[Verify JWT Token]
    Auth -->|HMAC Present| HMAC[Verify HMAC Signature]
    Auth -->|None| Reject[401 Unauthorized]
    
    JWT -->|Valid| Process[Process Request]
    JWT -->|Invalid| Reject
    
    HMAC -->|Valid| Process
    HMAC -->|Invalid| Reject
    
    Process --> Response[Return Results]
    
    subgraph "HMAC Verification"
        HMAC --> Timestamp[Check Timestamp]
        Timestamp --> Signature[Verify Signature]
        Signature --> Partner[Validate Partner]
    end
```

## 2. Implementation Specifications

### HMAC Helper (`src/lib/security/hmac.ts`)

#### Core Functions Required
```typescript
// Generate HMAC signature for outgoing requests
export function generateHMACSignature(
  method: string,
  path: string,
  timestamp: number,
  body?: string,
  secret?: string
): string

// Verify incoming HMAC signature
export function verifyHMACSignature(
  signature: string,
  method: string,
  path: string,
  timestamp: number,
  body?: string,
  secret?: string
): boolean

// Extract HMAC data from request
export function extractHMACFromRequest(
  request: NextRequest
): HMACData | null

// Verify request with HMAC authentication
export function verifyRequestHMAC(
  request: NextRequest
): Promise<HMACPayload | null>

// Create HMAC authentication headers
export function createHMACHeaders(
  method: string,
  path: string,
  body?: string
): Record<string, string>
```

#### HMAC Request Format
```
Headers:
  X-Signature: sha256=<signature>
  X-Timestamp: <unix_timestamp>
  X-Partner-ID: <partner_identifier>

Signature Calculation:
  message = METHOD + '\n' + PATH + '\n' + TIMESTAMP + '\n' + BODY_HASH
  signature = HMAC-SHA256(message, PARTNER_SECRET)
```

#### Security Parameters
- **Algorithm**: HMAC-SHA256
- **Timestamp Window**: 300 seconds (5 minutes)
- **Partner Secrets**: Environment-based configuration
- **Replay Protection**: Timestamp validation + optional nonce tracking

### Search Endpoints Protection

#### Authentication Middleware Pattern
```typescript
// Dual authentication check
async function authenticateRequest(request: NextRequest): Promise<AuthResult> {
  // Try JWT first (for browser users)
  const jwtPayload = await verifyRequestJWT(request)
  if (jwtPayload) {
    return { success: true, method: 'JWT', payload: jwtPayload }
  }
  
  // Try HMAC second (for API partners)
  const hmacPayload = await verifyRequestHMAC(request)
  if (hmacPayload) {
    return { success: true, method: 'HMAC', payload: hmacPayload }
  }
  
  return { success: false, error: 'No valid authentication found' }
}
```

#### Implementation Strategy
1. **Minimal Changes**: Add authentication middleware before existing logic
2. **Backward Compatibility**: Maintain existing response formats
3. **Error Handling**: Consistent 401 responses for authentication failures
4. **Logging**: Track authentication method and partner usage

## 3. Development Plan

### Phase 1: HMAC Helper Implementation (4 hours)
- [ ] Create `src/lib/security/hmac.ts` with core functions
- [ ] Implement signature generation and verification
- [ ] Add request parsing and validation helpers
- [ ] Create TypeScript interfaces and types
- [ ] Add environment variable validation

### Phase 2: Search Endpoints Protection (3 hours)
- [ ] Update `/api/search/route.ts` with dual authentication
- [ ] Update `/api/search/suggestions/route.ts` with dual authentication  
- [ ] Update `/api/search/more/route.ts` with dual authentication
- [ ] Implement consistent error handling across endpoints
- [ ] Add authentication logging and monitoring

### Phase 3: Testing & Validation (3 hours)
- [ ] Create HMAC test suite (`src/__tests__/security/hmac.test.ts`)
- [ ] Test search endpoint authentication scenarios
- [ ] Verify JWT backward compatibility
- [ ] Test partner HMAC authentication flows
- [ ] Performance testing for authentication overhead

### Phase 4: Documentation & Deployment (2 hours)
- [ ] Update API documentation with authentication requirements
- [ ] Create partner integration guide
- [ ] Document environment variable requirements
- [ ] Prepare deployment checklist and rollback procedures

## 4. Testing Strategy

### Test Coverage Requirements
- **HMAC Functions**: 100% coverage of signature generation/verification
- **Authentication Flows**: JWT, HMAC, and failure scenarios
- **Search Endpoints**: All three endpoints with both auth methods
- **Edge Cases**: Invalid signatures, expired timestamps, missing headers
- **Performance**: Authentication overhead measurement

### Test Scenarios
1. **Valid JWT Authentication**: Browser user with valid JWT token
2. **Valid HMAC Authentication**: API partner with correct signature
3. **Invalid JWT**: Expired or malformed JWT tokens
4. **Invalid HMAC**: Wrong signature, expired timestamp, missing headers
5. **No Authentication**: Requests without any authentication
6. **Mixed Authentication**: Requests with both JWT and HMAC (JWT takes precedence)

## 5. Security Considerations

### Threat Mitigation
- **Replay Attacks**: Timestamp validation with 5-minute window
- **Signature Forgery**: HMAC-SHA256 with strong partner secrets
- **Partner Compromise**: Individual partner secret rotation capability
- **Timing Attacks**: Constant-time signature comparison
- **Information Disclosure**: Minimal error messages, no secret leakage

### Environment Variables Required
```bash
# Partner HMAC secrets (production)
PARTNER_SECRET_DEFAULT=your-default-partner-secret-32-chars-min
PARTNER_SECRET_PARTNER1=partner1-specific-secret-32-chars-min
PARTNER_SECRET_PARTNER2=partner2-specific-secret-32-chars-min

# Development/testing
HMAC_TIMESTAMP_WINDOW=300  # 5 minutes
HMAC_ALGORITHM=sha256      # HMAC algorithm
```

## 6. Deployment Considerations

### Environment Setup
- **Development**: Test partner secrets for local development
- **Staging**: Production-like secrets for integration testing
- **Production**: Secure partner secrets via AWS Amplify Environment Secrets

### Rollback Strategy
- **Feature Flags**: Environment variable to disable HMAC authentication
- **Gradual Rollout**: Enable authentication per endpoint incrementally
- **Monitoring**: Track authentication success/failure rates
- **Fallback**: Ability to revert to public access if needed

### Performance Impact
- **Expected Overhead**: 1-3ms per request for signature verification
- **Caching Strategy**: Partner secret caching to reduce environment lookups
- **Monitoring**: Track authentication performance metrics

## 7. Success Criteria

### Functional Requirements
- [ ] All search endpoints require JWT or HMAC authentication
- [ ] HMAC signature generation and verification working correctly
- [ ] JWT authentication from PR 1 continues to work unchanged
- [ ] Partner API integration possible with HMAC authentication
- [ ] Comprehensive test coverage (>95%) for new authentication code

### Security Requirements
- [ ] No bypass methods for authentication requirements
- [ ] Proper error handling without information disclosure
- [ ] Replay attack protection through timestamp validation
- [ ] Partner secret security and rotation capability

### Performance Requirements
- [ ] Authentication overhead <5ms per request
- [ ] No degradation in search response times
- [ ] Successful handling of concurrent authenticated requests

## Next Steps

1. **CTO Review**: Present this implementation plan for approval
2. **Environment Setup**: Configure partner secrets in development environment
3. **Development Start**: Begin Phase 1 implementation
4. **Iterative Testing**: Test each phase before proceeding to next
5. **Documentation**: Maintain detailed progress documentation

---

**Prepared by:** SecOps-Claude  
**Review Required:** CTO Approval  
**Implementation Timeline:** 2-3 days  
**Risk Level:** Low (building on proven JWT foundation)

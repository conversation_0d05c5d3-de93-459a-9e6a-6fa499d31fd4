# PR 2 Deployment Guide - HMAC Authentication & Search Routes

**Date:** January 12, 2025  
**Sprint:** Auth Layer Implementation  
**Component:** HMAC Helper + Search Routes Protection  
**Deployment Type:** Incremental with Rollback Capability  

## Pre-Deployment Checklist

### Code Quality Gates
- [ ] **All Tests Passing**: 50/50 tests (HMAC + Search Auth + Security + Performance + Compatibility)
- [ ] **Code Coverage**: >95% for new HMAC functionality
- [ ] **Lint Checks**: ESLint passing with no errors
- [ ] **Build Success**: Production build completes without errors
- [ ] **Security Scan**: No new vulnerabilities introduced

### Environment Preparation
- [ ] **Partner Secrets**: Configured in AWS Amplify Environment Secrets
- [ ] **Test Partners**: Development/staging test accounts ready
- [ ] **Monitoring**: Authentication metrics dashboard prepared
- [ ] **Alerts**: Authentication failure rate alerts configured

## Environment Configuration

### Development Environment
```bash
# .env.local (development)
PARTNER_SECRET_DEFAULT=dev-default-secret-minimum-32-characters
PARTNER_SECRET_TEST_PARTNER=test-secret-minimum-32-characters-long
PARTNER_SECRET_ACME_CORP=acme-dev-secret-minimum-32-characters
HMAC_TIMESTAMP_WINDOW=300
HMAC_ALGORITHM=sha256
```

### Staging Environment
```bash
# AWS Amplify Environment Secrets (staging)
PARTNER_SECRET_DEFAULT=staging-default-secret-minimum-32-chars
PARTNER_SECRET_TEST_PARTNER=staging-test-secret-minimum-32-chars
PARTNER_SECRET_ACME_CORP=staging-acme-secret-minimum-32-chars
HMAC_TIMESTAMP_WINDOW=300
HMAC_ALGORITHM=sha256
```

### Production Environment
```bash
# AWS Amplify Environment Secrets (production)
PARTNER_SECRET_DEFAULT=prod-default-secret-minimum-32-characters
PARTNER_SECRET_ACME_CORP=prod-acme-secret-minimum-32-characters
PARTNER_SECRET_BETA_CORP=prod-beta-secret-minimum-32-characters
HMAC_TIMESTAMP_WINDOW=300
HMAC_ALGORITHM=sha256

# Optional: Feature flags for gradual rollout
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
```

## Deployment Strategy

### Phase 1: Staging Deployment (Day 1)
```bash
# Deploy to staging
git checkout feature/pr2-hmac-auth
git push origin feature/pr2-hmac-auth

# Verify staging deployment
curl -X GET "https://staging.cashback-deals.com/api/search?q=test" \
  -H "X-Signature: sha256=..." \
  -H "X-Timestamp: $(date +%s)" \
  -H "X-Partner-ID: test-partner"
```

#### Staging Validation Checklist
- [ ] **HMAC Authentication**: Test partner can authenticate successfully
- [ ] **JWT Compatibility**: Existing JWT authentication still works
- [ ] **Error Handling**: Proper 401 responses for invalid authentication
- [ ] **Rate Limiting**: Rate limits still enforced after authentication
- [ ] **Performance**: Response times within acceptable range (<5ms overhead)

### Phase 2: Production Deployment (Day 2)
```bash
# Create production deployment
git checkout main
git merge feature/pr2-hmac-auth
git push origin main

# Monitor deployment
# AWS Amplify will automatically deploy to production
```

#### Production Validation Checklist
- [ ] **Health Check**: All search endpoints responding correctly
- [ ] **Authentication Metrics**: Monitor success/failure rates
- [ ] **Error Rates**: No increase in 5xx errors
- [ ] **Response Times**: No degradation in API performance
- [ ] **Partner Integration**: Test partners can authenticate successfully

### Phase 3: Partner Onboarding (Day 3+)
- [ ] **Partner Notification**: Inform partners of new authentication requirements
- [ ] **Integration Support**: Provide technical assistance for HMAC implementation
- [ ] **Gradual Migration**: Allow grace period for partner integration
- [ ] **Monitoring**: Track partner adoption and authentication patterns

## Monitoring & Alerting

### Key Metrics to Monitor
```typescript
// Authentication success rates
const authMetrics = {
  jwtSuccessRate: 'percentage of successful JWT authentications',
  hmacSuccessRate: 'percentage of successful HMAC authentications',
  authFailureRate: 'percentage of authentication failures',
  partnerAuthDistribution: 'authentication attempts by partner',
  responseTimeP95: '95th percentile response time with authentication'
}
```

### Alert Thresholds
```yaml
alerts:
  - name: "High Authentication Failure Rate"
    condition: "auth_failure_rate > 10%"
    severity: "warning"
    
  - name: "Critical Authentication Failure Rate"
    condition: "auth_failure_rate > 25%"
    severity: "critical"
    
  - name: "Authentication Performance Degradation"
    condition: "auth_response_time_p95 > 100ms"
    severity: "warning"
    
  - name: "Partner Authentication Issues"
    condition: "partner_auth_failure_rate > 50%"
    severity: "warning"
```

### Monitoring Dashboard
```typescript
// CloudWatch/Grafana dashboard metrics
const dashboardMetrics = [
  'API requests per minute by endpoint',
  'Authentication method distribution (JWT vs HMAC)',
  'Authentication success/failure rates',
  'Response time percentiles (P50, P95, P99)',
  'Partner authentication patterns',
  'Error rate by endpoint and authentication method'
]
```

## Rollback Procedures

### Immediate Rollback (Emergency)
If critical issues are detected within first 30 minutes:

```bash
# Emergency rollback to previous version
git revert HEAD --no-edit
git push origin main

# Or use AWS Amplify console to rollback to previous deployment
```

#### Emergency Rollback Triggers
- **Authentication Failure Rate >50%**: Mass authentication failures
- **API Downtime**: Search endpoints returning 5xx errors
- **Performance Degradation >200%**: Unacceptable response time increase
- **Partner Integration Failures**: Multiple partners unable to authenticate

### Gradual Rollback (Planned)
If issues are identified during monitoring period:

#### Option 1: Feature Flag Rollback
```bash
# Disable authentication via environment variables
ENABLE_SEARCH_AUTH=false
ENABLE_HMAC_AUTH=false

# Redeploy with authentication disabled
```

#### Option 2: Endpoint-Specific Rollback
```typescript
// Temporarily disable authentication on specific endpoints
const ROLLBACK_ENDPOINTS = [
  '/api/search/suggestions', // If suggestions endpoint has issues
  '/api/search/more'         // If pagination endpoint has issues
]

// Keep /api/search protected, rollback others
```

#### Option 3: Partner-Specific Rollback
```typescript
// Temporarily allow specific partners without authentication
const ROLLBACK_PARTNERS = ['acme-corp', 'beta-corp']

// Allow these partners to access APIs without HMAC temporarily
```

### Rollback Validation
After any rollback:
- [ ] **API Functionality**: All search endpoints working correctly
- [ ] **Performance**: Response times back to baseline
- [ ] **Error Rates**: Error rates back to normal levels
- [ ] **Partner Access**: Partners can access APIs as before
- [ ] **JWT Functionality**: Contact form JWT authentication still working

## Post-Deployment Tasks

### Day 1 (Deployment Day)
- [ ] **Monitor Metrics**: Watch authentication success rates for first 4 hours
- [ ] **Partner Communication**: Notify partners of successful deployment
- [ ] **Documentation Update**: Update API documentation with authentication requirements
- [ ] **Support Team Brief**: Brief support team on new authentication system

### Week 1 (Monitoring Period)
- [ ] **Daily Metrics Review**: Review authentication patterns and performance
- [ ] **Partner Feedback**: Collect feedback from early adopter partners
- [ ] **Performance Analysis**: Analyze impact on API response times
- [ ] **Error Pattern Analysis**: Identify common authentication failure patterns

### Week 2-4 (Optimization Period)
- [ ] **Performance Tuning**: Optimize authentication overhead if needed
- [ ] **Partner Onboarding**: Complete onboarding of all partners
- [ ] **Documentation Refinement**: Update documentation based on partner feedback
- [ ] **Monitoring Refinement**: Adjust alert thresholds based on observed patterns

## Success Criteria

### Technical Success Metrics
- [ ] **Authentication Success Rate**: >95% for both JWT and HMAC
- [ ] **API Performance**: <5ms authentication overhead
- [ ] **Error Rate**: No increase in 5xx errors
- [ ] **Partner Adoption**: >80% of partners successfully integrated within 2 weeks

### Business Success Metrics
- [ ] **Search API Abuse**: Reduction in unauthorized scraping attempts
- [ ] **Partner Satisfaction**: Positive feedback on integration experience
- [ ] **System Stability**: No authentication-related outages
- [ ] **Security Posture**: Improved protection against API abuse

## Troubleshooting Guide

### Common Issues and Solutions

#### High Authentication Failure Rate
```bash
# Check partner secret configuration
aws amplify get-app --app-id YOUR_APP_ID

# Verify environment variables are set correctly
# Check CloudWatch logs for authentication errors
```

#### Performance Degradation
```bash
# Monitor authentication overhead
# Check for memory leaks in HMAC verification
# Verify partner secret caching is working
```

#### Partner Integration Issues
```bash
# Provide partner with test signature generation
# Verify partner's timestamp is within window
# Check partner secret configuration
```

### Emergency Contacts
- **Technical Lead**: [email]
- **DevOps Team**: [email]
- **Partner Support**: [email]
- **Security Team**: [email]

## Documentation Updates

### Files to Update Post-Deployment
- [ ] **API Documentation**: Add authentication requirements
- [ ] **Partner Onboarding Guide**: Update with HMAC instructions
- [ ] **Troubleshooting Guide**: Add HMAC-specific troubleshooting
- [ ] **Security Documentation**: Update with new authentication methods

---

**Deployment Owner**: SecOps-Claude  
**Approval Required**: CTO Sign-off  
**Rollback Authority**: Technical Lead + DevOps  
**Go-Live Date**: TBD after CTO approval

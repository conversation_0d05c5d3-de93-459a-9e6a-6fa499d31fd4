Below is a focused technical review of the PR 2 documentation set (HMAC + search-route protection) with an eye toward shipping an MVP next week. Overall the docs are clear and thorough, but a handful of gaps and “TBDs” still need to be closed before code freeze.

1 — Material that already looks solid
Area	Why it’s in good shape	Key source
Business & risk framing	Executive summary quantifies cost-savings and ties HMAC to partner monetisation.	executive-summary
High-level architecture	Dual-auth mermaid diagram & decision matrix lay out JWT→browser / HMAC→API separation cleanly.	implementation-plan
Core algorithm spec	Message construction, header format and constant-time comparison rules are explicit.	technical-specifications
Partner guide	End-to-end code samples in JS/Python/PHP + test vectors enable self-service onboarding.	partner-integration-gui…
Happy-path tests	Unit tests already cover signature generation/verification, header extraction etc.	testing-strategy

2 — Issues to resolve for MVP launch
2.1 Environment & secret handling
Hard-coded / sample secrets are referenced throughout docs; environment-variable names vary (PARTNER_SECRET_DEFAULT, PARTNER_SECRET_ACME, etc.) and the storage mechanism is only “AWS Amplify Env Secrets” in a footnote technical-specifications.
▶ Action: add a short “Secret lifecycle” section: creation, rotation, revocation, and where exactly these live in each env (dev/stage/prod).

The timestamp window (300 s) is configured via an env var in some docs but hard-coded elsewhere implementation-plan.
▶ Action: define one source of truth—HMAC_TIMESTAMP_WINDOW—and update all snippets.

2.2 Replay & nonce protection
Spec mentions an optional X-Nonce but nothing in tests or helper-interfaces enforces uniqueness technical-specifications.
▶ Action: clarify whether nonce support is post-MVP or implement minimal in-memory map (no Redis) to block duplicate (timestamp, signature) pairs within the 5-min window.

2.3 Error handling & observability
Consistent 401 bodies are defined, but there’s no mapping from HMACError → response payload in code samples technical-specifications.
▶ Action: add a short table in Technical Specs mapping each error enum to response & log entry.

Security-event logging writes to console.log only technical-specifications.
▶ Action: specify which log sink (CloudWatch, Logtail, etc.) and include a trace-ID to allow correlation with search-service logs.



Rollback procedures are clear, but feature-flag names (ENABLE_SEARCH_AUTH, ENABLE_HMAC_AUTH) aren’t referenced in code snippets.
▶ Action: add a code example showing guards that read those flags.

2.5 Testing gaps
Test matrix lists performance (<5 ms overhead) but no k6/Artillery script is included to prove it.
▶ Action: add a simple perf test in __tests__/performance/auth-performance.test.ts that fires 100 concurrent signed GETs.

No negative-path integration test for mismatched body-hash: e.g. sign with empty body, then send a JSON body.
▶ Action: extend hmac-security.test.ts accordingly.



3 — Post-MVP nice-to-haves (can defer)
Item	Rationale
Redis/DB cache for nonce deduping	Improves replay protection at scale but not critical if window is short and traffic moderate.
OpenAPI / Postman collection	Speeds external integration; low effort but not on critical path.
Automated secret rotation playbook	Useful long-term security hygiene.

4 — Suggested doc tweaks summary
Secret lifecycle & single env-var schema

Nonce strategy or explicitly defer

Error-code → response table & log sink details

Fill feature-flag code sample

Add perf + body-hash mismatch tests



Addressing the above will leave us with a robust, auditable HMAC MVP that can ship confidently while keeping optional Redis and other enhancements on the backlog.
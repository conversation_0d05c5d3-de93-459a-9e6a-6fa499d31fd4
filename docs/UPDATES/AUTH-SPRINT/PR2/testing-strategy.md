# PR 2 Testing Strategy - HMAC Authentication & Search Routes

**Date:** January 12, 2025  
**Sprint:** Auth Layer Implementation  
**Component:** HMAC Helper + Search Routes Protection  
**Test Coverage Target:** >95%  

## Testing Overview

This document outlines the comprehensive testing strategy for PR 2, focusing on HMAC authentication implementation and search endpoint protection. The testing approach follows the same rigorous standards established in PR 1 (JWT authentication) with 15/15 tests passing.

## 1. Test Architecture

### Test Categories
1. **Unit Tests**: HMAC helper functions and utilities
2. **Integration Tests**: Search endpoint authentication flows
3. **Security Tests**: Attack scenarios and edge cases
4. **Performance Tests**: Authentication overhead measurement
5. **Compatibility Tests**: JWT + HMAC coexistence

### Test Environment Setup
```typescript
// Test configuration
const TEST_CONFIG = {
  partners: {
    'test-partner': 'test-secret-minimum-32-characters-long',
    'acme-corp': 'acme-secret-minimum-32-characters-long',
    'invalid-partner': 'short-secret' // For testing validation
  },
  timestampWindow: 300, // 5 minutes
  testEndpoints: [
    '/api/search',
    '/api/search/suggestions', 
    '/api/search/more'
  ]
}
```

## 2. Unit Tests - HMAC Helper Functions

### Test File: `src/__tests__/security/hmac.test.ts`

#### Core Function Tests (15 tests)
```typescript
describe('HMAC Helper Functions', () => {
  describe('generateHMACSignature', () => {
    it('generates correct signature for GET request', () => {
      const signature = generateHMACSignature(
        'GET',
        '/api/search',
        1705123456,
        '',
        'test-secret-minimum-32-characters-long'
      )
      expect(signature).toBe('expected-signature-hash')
    })
    
    it('generates correct signature for POST request with body', () => {
      const body = JSON.stringify({ query: 'laptop' })
      const signature = generateHMACSignature(
        'POST',
        '/api/search',
        1705123456,
        body,
        'test-secret-minimum-32-characters-long'
      )
      expect(signature).toMatch(/^[a-f0-9]{64}$/)
    })
    
    it('generates different signatures for different methods', () => {
      const getSignature = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret')
      const postSignature = generateHMACSignature('POST', '/api/search', 1705123456, '', 'secret')
      expect(getSignature).not.toBe(postSignature)
    })
    
    it('generates different signatures for different paths', () => {
      const searchSignature = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret')
      const suggestionsSignature = generateHMACSignature('GET', '/api/search/suggestions', 1705123456, '', 'secret')
      expect(searchSignature).not.toBe(suggestionsSignature)
    })
    
    it('generates different signatures for different timestamps', () => {
      const signature1 = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret')
      const signature2 = generateHMACSignature('GET', '/api/search', 1705123457, '', 'secret')
      expect(signature1).not.toBe(signature2)
    })
  })
  
  describe('verifyHMACSignature', () => {
    it('verifies valid signature', () => {
      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret')
      const isValid = verifyHMACSignature(signature, 'GET', '/api/search', 1705123456, '', 'secret')
      expect(isValid).toBe(true)
    })
    
    it('rejects invalid signature', () => {
      const isValid = verifyHMACSignature('invalid-signature', 'GET', '/api/search', 1705123456, '', 'secret')
      expect(isValid).toBe(false)
    })
    
    it('rejects signature with wrong secret', () => {
      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret1')
      const isValid = verifyHMACSignature(signature, 'GET', '/api/search', 1705123456, '', 'secret2')
      expect(isValid).toBe(false)
    })
  })
  
  describe('extractHMACFromRequest', () => {
    it('extracts valid HMAC data from request', () => {
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': 'sha256=abc123',
          'x-timestamp': '1705123456',
          'x-partner-id': 'test-partner'
        }
      })
      
      const hmacData = extractHMACFromRequest(request)
      expect(hmacData).toEqual({
        signature: 'abc123',
        timestamp: 1705123456,
        partnerId: 'test-partner'
      })
    })
    
    it('returns null for missing headers', () => {
      const request = new NextRequest('http://localhost/api/search')
      const hmacData = extractHMACFromRequest(request)
      expect(hmacData).toBeNull()
    })
    
    it('handles sha256= prefix in signature', () => {
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': 'sha256=abc123',
          'x-timestamp': '1705123456',
          'x-partner-id': 'test-partner'
        }
      })
      
      const hmacData = extractHMACFromRequest(request)
      expect(hmacData?.signature).toBe('abc123')
    })
  })
  
  describe('verifyRequestHMAC', () => {
    it('verifies valid HMAC request', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')
      
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': 'test-partner'
        }
      })
      
      const payload = await verifyRequestHMAC(request)
      expect(payload).toEqual({
        partnerId: 'test-partner',
        timestamp,
        method: 'GET',
        path: '/api/search',
        isValid: true
      })
    })
    
    it('rejects expired timestamp', async () => {
      const expiredTimestamp = Math.floor(Date.now() / 1000) - 400 // 400 seconds ago
      const signature = generateHMACSignature('GET', '/api/search', expiredTimestamp, '', 'test-secret-minimum-32-characters-long')
      
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': expiredTimestamp.toString(),
          'x-partner-id': 'test-partner'
        }
      })
      
      const payload = await verifyRequestHMAC(request)
      expect(payload).toBeNull()
    })
    
    it('rejects unknown partner', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'unknown-secret')
      
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': 'unknown-partner'
        }
      })
      
      const payload = await verifyRequestHMAC(request)
      expect(payload).toBeNull()
    })
  })
  
  describe('createHMACHeaders', () => {
    it('creates valid HMAC headers', () => {
      const headers = createHMACHeaders('GET', '/api/search', 'test-partner', '')
      
      expect(headers).toHaveProperty('X-Signature')
      expect(headers).toHaveProperty('X-Timestamp')
      expect(headers).toHaveProperty('X-Partner-ID')
      expect(headers['X-Partner-ID']).toBe('test-partner')
      expect(headers['X-Signature']).toMatch(/^sha256=[a-f0-9]{64}$/)
    })
  })
})
```

## 3. Integration Tests - Search Endpoint Authentication

### Test File: `src/__tests__/api/search-auth.test.ts`

#### Search Endpoint Tests (12 tests)
```typescript
describe('Search API Authentication', () => {
  describe('/api/search', () => {
    it('allows access with valid JWT', async () => {
      const jwt = await createJWT()
      const response = await fetch('/api/search?q=laptop', {
        headers: { 'Authorization': `Bearer ${jwt}` }
      })
      expect(response.status).toBe(200)
    })
    
    it('allows access with valid HMAC', async () => {
      const headers = createHMACHeaders('GET', '/api/search?q=laptop', 'test-partner', '')
      const response = await fetch('/api/search?q=laptop', { headers })
      expect(response.status).toBe(200)
    })
    
    it('rejects request without authentication', async () => {
      const response = await fetch('/api/search?q=laptop')
      expect(response.status).toBe(401)
      
      const data = await response.json()
      expect(data.error).toBe('Unauthorized')
      expect(data.supportedMethods).toEqual(['JWT', 'HMAC'])
    })
    
    it('rejects request with invalid HMAC signature', async () => {
      const headers = {
        'X-Signature': 'sha256=invalid-signature',
        'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
        'X-Partner-ID': 'test-partner'
      }
      const response = await fetch('/api/search?q=laptop', { headers })
      expect(response.status).toBe(401)
    })
  })
  
  describe('/api/search/suggestions', () => {
    it('allows access with valid HMAC', async () => {
      const headers = createHMACHeaders('GET', '/api/search/suggestions?q=lap', 'test-partner', '')
      const response = await fetch('/api/search/suggestions?q=lap', { headers })
      expect(response.status).toBe(200)
    })
    
    it('rejects request without authentication', async () => {
      const response = await fetch('/api/search/suggestions?q=lap')
      expect(response.status).toBe(401)
    })
  })
  
  describe('/api/search/more', () => {
    it('allows access with valid HMAC', async () => {
      const headers = createHMACHeaders('GET', '/api/search/more?q=laptop&page=2', 'test-partner', '')
      const response = await fetch('/api/search/more?q=laptop&page=2', { headers })
      expect(response.status).toBe(200)
    })
    
    it('rejects request without authentication', async () => {
      const response = await fetch('/api/search/more?q=laptop&page=2')
      expect(response.status).toBe(401)
    })
  })
})
```

## 4. Security Tests - Attack Scenarios

### Test File: `src/__tests__/security/hmac-security.test.ts`

#### Security Scenario Tests (10 tests)
```typescript
describe('HMAC Security Tests', () => {
  describe('Replay Attack Protection', () => {
    it('rejects replayed request with old timestamp', async () => {
      const oldTimestamp = Math.floor(Date.now() / 1000) - 400
      const signature = generateHMACSignature('GET', '/api/search', oldTimestamp, '', 'test-secret-minimum-32-characters-long')
      
      const headers = {
        'X-Signature': `sha256=${signature}`,
        'X-Timestamp': oldTimestamp.toString(),
        'X-Partner-ID': 'test-partner'
      }
      
      const response = await fetch('/api/search?q=laptop', { headers })
      expect(response.status).toBe(401)
    })
    
    it('rejects future timestamp', async () => {
      const futureTimestamp = Math.floor(Date.now() / 1000) + 400
      const signature = generateHMACSignature('GET', '/api/search', futureTimestamp, '', 'test-secret-minimum-32-characters-long')
      
      const headers = {
        'X-Signature': `sha256=${signature}`,
        'X-Timestamp': futureTimestamp.toString(),
        'X-Partner-ID': 'test-partner'
      }
      
      const response = await fetch('/api/search?q=laptop', { headers })
      expect(response.status).toBe(401)
    })
  })
  
  describe('Signature Tampering Protection', () => {
    it('rejects modified signature', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const validSignature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')
      const tamperedSignature = validSignature.slice(0, -1) + '0' // Change last character

      const headers = {
        'X-Signature': `sha256=${tamperedSignature}`,
        'X-Timestamp': timestamp.toString(),
        'X-Partner-ID': 'test-partner'
      }

      const response = await fetch('/api/search?q=laptop', { headers })
      expect(response.status).toBe(401)
    })

    it('rejects signature for different path', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search/suggestions', timestamp, '', 'test-secret-minimum-32-characters-long')

      const headers = {
        'X-Signature': `sha256=${signature}`,
        'X-Timestamp': timestamp.toString(),
        'X-Partner-ID': 'test-partner'
      }

      // Use signature for /suggestions on /search endpoint
      const response = await fetch('/api/search?q=laptop', { headers })
      expect(response.status).toBe(401)
    })

    it('rejects signature with mismatched body hash', async () => {
      const timestamp = Math.floor(Date.now() / 1000)

      // Generate signature with empty body
      const signature = generateHMACSignature('POST', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')

      const headers = {
        'X-Signature': `sha256=${signature}`,
        'X-Timestamp': timestamp.toString(),
        'X-Partner-ID': 'test-partner',
        'Content-Type': 'application/json'
      }

      // Send request with JSON body (should fail because signature was for empty body)
      const response = await fetch('/api/search', {
        method: 'POST',
        headers,
        body: JSON.stringify({ query: 'laptop', filters: { brand: 'samsung' } })
      })

      expect(response.status).toBe(401)

      const data = await response.json()
      expect(data.code).toBe('BODY_HASH_MISMATCH')
    })

    it('rejects signature with different method', async () => {
      const timestamp = Math.floor(Date.now() / 1000)

      // Generate signature for GET request
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')

      const headers = {
        'X-Signature': `sha256=${signature}`,
        'X-Timestamp': timestamp.toString(),
        'X-Partner-ID': 'test-partner'
      }

      // Use GET signature on POST request
      const response = await fetch('/api/search', {
        method: 'POST',
        headers,
        body: JSON.stringify({ query: 'laptop' })
      })

      expect(response.status).toBe(401)
    })
  })
  
  describe('Partner Validation', () => {
    it('rejects unknown partner ID', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'unknown-secret')
      
      const headers = {
        'X-Signature': `sha256=${signature}`,
        'X-Timestamp': timestamp.toString(),
        'X-Partner-ID': 'unknown-partner'
      }
      
      const response = await fetch('/api/search?q=laptop', { headers })
      expect(response.status).toBe(401)
    })
    
    it('rejects partner with short secret', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'short')
      
      const headers = {
        'X-Signature': `sha256=${signature}`,
        'X-Timestamp': timestamp.toString(),
        'X-Partner-ID': 'invalid-partner'
      }
      
      const response = await fetch('/api/search?q=laptop', { headers })
      expect(response.status).toBe(401)
    })
  })
})
```

## 5. Performance Tests

### Test File: `src/__tests__/performance/auth-performance.test.ts`

#### Performance Benchmarks (7 tests)
```typescript
describe('Authentication Performance', () => {
  it('HMAC verification completes within 5ms', async () => {
    const timestamp = Math.floor(Date.now() / 1000)
    const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')

    const request = new NextRequest('http://localhost/api/search', {
      headers: {
        'x-signature': `sha256=${signature}`,
        'x-timestamp': timestamp.toString(),
        'x-partner-id': 'test-partner'
      }
    })

    const startTime = performance.now()
    await verifyRequestHMAC(request)
    const endTime = performance.now()

    expect(endTime - startTime).toBeLessThan(5)
  })

  it('handles 100 concurrent HMAC verifications under 500ms', async () => {
    const requests = Array.from({ length: 100 }, (_, i) => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', `/api/search?q=test${i}`, timestamp, '', 'test-secret-minimum-32-characters-long')

      return new NextRequest(`http://localhost/api/search?q=test${i}`, {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': 'test-partner'
        }
      })
    })

    const startTime = performance.now()
    const results = await Promise.all(requests.map(verifyRequestHMAC))
    const endTime = performance.now()

    expect(results.every(result => result !== null)).toBe(true)
    expect(endTime - startTime).toBeLessThan(500) // 500ms for 100 concurrent requests
  })

  it('measures authentication overhead on search endpoint', async () => {
    // Test without authentication (baseline)
    const baselineStart = performance.now()
    const baselineResponse = await fetch('/api/search?q=laptop', {
      headers: { 'x-test-bypass-auth': 'true' } // Test bypass flag
    })
    const baselineEnd = performance.now()
    const baselineTime = baselineEnd - baselineStart

    // Test with HMAC authentication
    const headers = createHMACHeaders('GET', '/api/search?q=laptop', 'test-partner', '')
    const authStart = performance.now()
    const authResponse = await fetch('/api/search?q=laptop', { headers })
    const authEnd = performance.now()
    const authTime = authEnd - authStart

    // Authentication overhead should be less than 5ms
    const overhead = authTime - baselineTime
    expect(overhead).toBeLessThan(5)
    expect(authResponse.status).toBe(200)
  })

  it('stress test: 1000 sequential HMAC verifications', async () => {
    const startTime = performance.now()

    for (let i = 0; i < 1000; i++) {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')

      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': 'test-partner'
        }
      })

      const result = await verifyRequestHMAC(request)
      expect(result).not.toBeNull()
    }

    const endTime = performance.now()
    const totalTime = endTime - startTime
    const avgTime = totalTime / 1000

    expect(avgTime).toBeLessThan(2) // Average less than 2ms per verification
  })

  it('memory usage remains stable during extended operation', async () => {
    const initialMemory = process.memoryUsage().heapUsed

    // Perform 500 HMAC operations
    for (let i = 0; i < 500; i++) {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')

      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': 'test-partner'
        }
      })

      await verifyRequestHMAC(request)
    }

    // Force garbage collection if available
    if (global.gc) {
      global.gc()
    }

    const finalMemory = process.memoryUsage().heapUsed
    const memoryIncrease = finalMemory - initialMemory

    // Memory increase should be minimal (less than 10MB)
    expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
  })
})
```

## 6. Compatibility Tests

### Test File: `src/__tests__/integration/jwt-hmac-compatibility.test.ts`

#### JWT + HMAC Coexistence (8 tests)
```typescript
describe('JWT + HMAC Compatibility', () => {
  it('JWT takes precedence when both are present', async () => {
    const jwt = await createJWT()
    const hmacHeaders = createHMACHeaders('GET', '/api/search', 'test-partner', '')
    
    const response = await fetch('/api/search?q=laptop', {
      headers: {
        'Authorization': `Bearer ${jwt}`,
        ...hmacHeaders
      }
    })
    
    expect(response.status).toBe(200)
    // Should log JWT authentication, not HMAC
  })
  
  it('falls back to HMAC when JWT is invalid', async () => {
    const hmacHeaders = createHMACHeaders('GET', '/api/search', 'test-partner', '')
    
    const response = await fetch('/api/search?q=laptop', {
      headers: {
        'Authorization': 'Bearer invalid-jwt',
        ...hmacHeaders
      }
    })
    
    expect(response.status).toBe(200)
    // Should log HMAC authentication
  })
  
  it('existing JWT functionality remains unchanged', async () => {
    // Test that PR 1 JWT functionality still works
    const jwt = await createJWT()
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${jwt}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'Test User',
        email: '<EMAIL>',
        message: 'Test message'
      })
    })
    
    expect(response.status).toBe(200)
  })
})
```

## 7. Test Execution Strategy

### Test Commands
```bash
# Run all HMAC tests
npm test -- src/__tests__/security/hmac.test.ts

# Run search endpoint authentication tests
npm test -- src/__tests__/api/search-auth.test.ts

# Run security tests
npm test -- src/__tests__/security/hmac-security.test.ts

# Run performance tests
npm test -- src/__tests__/performance/auth-performance.test.ts

# Run compatibility tests
npm test -- src/__tests__/integration/jwt-hmac-compatibility.test.ts

# Run all PR 2 tests
npm test -- --testPathPattern="(hmac|search-auth)"

# Run with coverage
npm run test:coverage -- --testPathPattern="(hmac|search-auth)"
```

### Success Criteria
- [ ] **Unit Tests**: 15/15 HMAC helper function tests passing
- [ ] **Integration Tests**: 12/12 search endpoint authentication tests passing
- [ ] **Security Tests**: 10/10 attack scenario tests passing
- [ ] **Performance Tests**: 5/5 performance benchmark tests passing
- [ ] **Compatibility Tests**: 8/8 JWT+HMAC coexistence tests passing
- [ ] **Overall Coverage**: >95% code coverage for new HMAC functionality
- [ ] **Regression Tests**: All existing JWT tests (15/15) still passing

### Test Data Management
```typescript
// Test environment setup
beforeAll(async () => {
  // Set test environment variables
  process.env.PARTNER_SECRET_TEST_PARTNER = 'test-secret-minimum-32-characters-long'
  process.env.PARTNER_SECRET_ACME_CORP = 'acme-secret-minimum-32-characters-long'
  process.env.HMAC_TIMESTAMP_WINDOW = '300'
})

afterAll(async () => {
  // Clean up test environment
  delete process.env.PARTNER_SECRET_TEST_PARTNER
  delete process.env.PARTNER_SECRET_ACME_CORP
  delete process.env.HMAC_TIMESTAMP_WINDOW
})
```

---

**Target: 50/50 tests passing (100% success rate)**  
**Coverage Target: >95%**  
**Performance Target: <5ms authentication overhead**

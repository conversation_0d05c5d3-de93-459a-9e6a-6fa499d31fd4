# HMAC Authentication Deployment Guide

## Pre-Deployment Checklist

### 1. Code Review
- [ ] All HMAC authentication code reviewed and approved
- [ ] Security implementation validated
- [ ] Test coverage meets requirements (>90%)
- [ ] Performance benchmarks acceptable

### 2. Environment Configuration

#### Required Environment Variables

```bash
# Authentication Control
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true

# JWT Configuration (existing)
JWT_SECRET=your-secure-jwt-secret-key

# HMAC Configuration
HMAC_TIMESTAMP_WINDOW=300  # 5 minutes

# Partner Secrets (add as needed)
PARTNER_SECRET_DEFAULT=your-default-partner-secret
PARTNER_SECRET_PARTNER1=partner1-secret-key
PARTNER_SECRET_PARTNER2=partner2-secret-key
```

#### AWS Amplify Environment Secrets

1. Navigate to AWS Amplify Console
2. Select your app
3. Go to Environment variables
4. Add the following secrets:

```
JWT_SECRET (if not already set)
PARTNER_SECRET_DEFAULT
PARTNER_SECRET_[PARTNER_ID] (for each partner)
```

### 3. Build Verification

```bash
# Ensure clean build
npm run build

# Verify no TypeScript errors
npm run type-check

# Run all tests
npm test

# Run E2E tests (optional, requires browser setup)
npx playwright install
npm run test:e2e
```

## Deployment Steps

### Phase 1: Staging Deployment

1. **Deploy to Staging**
   ```bash
   # Deploy to staging environment
   git checkout main
   git pull origin main
   amplify publish --environment staging
   ```

2. **Verify Staging Environment**
   ```bash
   # Test unauthenticated request (should fail)
   curl -X GET "https://staging.yourapp.com/api/search?q=test"
   
   # Expected: 401 Unauthorized with MISSING_AUTH error
   ```

3. **Test HMAC Authentication**
   ```bash
   # Use the test script to verify HMAC works
   node scripts/test-hmac-auth.js staging
   ```

4. **Test JWT Authentication**
   - Login to staging frontend
   - Perform search operations
   - Verify functionality unchanged

### Phase 2: Production Deployment

1. **Final Pre-deployment Checks**
   - [ ] Staging tests all passing
   - [ ] Partner secrets configured in production
   - [ ] Monitoring alerts configured
   - [ ] Rollback plan prepared

2. **Deploy to Production**
   ```bash
   # Deploy to production
   amplify publish --environment production
   ```

3. **Post-deployment Verification**
   ```bash
   # Verify authentication is working
   curl -X GET "https://yourapp.com/api/search?q=test"
   
   # Should return 401 with proper error structure
   ```

## Testing Scripts

### HMAC Test Script

Create `scripts/test-hmac-auth.js`:

```javascript
const crypto = require('crypto')
const fetch = require('node-fetch')

function generateHMACSignature(method, path, timestamp, body, secret) {
  const message = `${method}\n${path}\n${timestamp}\n${body}`
  return crypto.createHmac('sha256', secret).update(message).digest('hex')
}

async function testHMACAuth(baseUrl, partnerId, secret) {
  const method = 'GET'
  const path = '/api/search?q=test'
  const timestamp = Math.floor(Date.now() / 1000)
  const body = ''
  
  const signature = generateHMACSignature(method, path, timestamp, body, secret)
  
  try {
    const response = await fetch(`${baseUrl}${path}`, {
      method,
      headers: {
        'X-Signature': `sha256=${signature}`,
        'X-Timestamp': timestamp.toString(),
        'X-Partner-ID': partnerId
      }
    })
    
    console.log(`Status: ${response.status}`)
    console.log(`Response: ${await response.text()}`)
    
    return response.status === 200
  } catch (error) {
    console.error('Test failed:', error)
    return false
  }
}

// Usage
const environment = process.argv[2] || 'staging'
const baseUrl = environment === 'production' 
  ? 'https://yourapp.com' 
  : 'https://staging.yourapp.com'

testHMACAuth(baseUrl, 'default', process.env.PARTNER_SECRET_DEFAULT)
```

### Health Check Script

Create `scripts/health-check.js`:

```javascript
const fetch = require('node-fetch')

async function healthCheck(baseUrl) {
  const checks = [
    {
      name: 'Unauthenticated Request',
      url: `${baseUrl}/api/search?q=test`,
      expectedStatus: 401,
      expectedError: 'MISSING_AUTH'
    },
    {
      name: 'Invalid Signature',
      url: `${baseUrl}/api/search?q=test`,
      headers: {
        'X-Signature': 'sha256=invalid',
        'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
        'X-Partner-ID': 'default'
      },
      expectedStatus: 401,
      expectedError: 'INVALID_SIGNATURE'
    }
  ]
  
  for (const check of checks) {
    try {
      const response = await fetch(check.url, {
        headers: check.headers || {}
      })
      
      const body = await response.json()
      
      console.log(`✓ ${check.name}: ${response.status}`)
      
      if (response.status !== check.expectedStatus) {
        console.error(`  Expected status ${check.expectedStatus}, got ${response.status}`)
      }
      
      if (check.expectedError && body.code !== check.expectedError) {
        console.error(`  Expected error ${check.expectedError}, got ${body.code}`)
      }
    } catch (error) {
      console.error(`✗ ${check.name}: ${error.message}`)
    }
  }
}

const environment = process.argv[2] || 'staging'
const baseUrl = environment === 'production' 
  ? 'https://yourapp.com' 
  : 'https://staging.yourapp.com'

healthCheck(baseUrl)
```

## Monitoring Setup

### 1. CloudWatch Alarms

Set up alarms for:
- High authentication failure rate
- Unusual request patterns
- Performance degradation

### 2. Log Monitoring

Monitor for these log patterns:
- `HMAC_AUTH_FAILURE` - Authentication failures
- `HMAC_AUTH_SUCCESS` - Successful authentications
- `REPLAY_DETECTED` - Potential replay attacks

### 3. Performance Metrics

Track:
- Authentication latency
- Request success rate
- Error distribution

## Rollback Plan

### Immediate Rollback (Emergency)

1. **Disable Authentication**
   ```bash
   # Set environment variable to disable auth
   amplify env update --environment production
   # Set ENABLE_SEARCH_AUTH=false
   amplify publish --environment production
   ```

2. **Revert Code**
   ```bash
   # Revert to previous commit
   git revert HEAD
   amplify publish --environment production
   ```

### Gradual Rollback

1. **Disable HMAC Only**
   ```bash
   # Keep JWT, disable HMAC
   # Set ENABLE_HMAC_AUTH=false
   ```

2. **Monitor and Adjust**
   - Monitor error rates
   - Adjust configuration as needed
   - Re-enable when issues resolved

## Partner Onboarding

### New Partner Setup

1. **Generate Partner Secret**
   ```bash
   # Generate secure random secret
   openssl rand -hex 32
   ```

2. **Configure Environment**
   ```bash
   # Add to environment variables
   PARTNER_SECRET_[PARTNER_ID]=generated-secret
   ```

3. **Provide Integration Guide**
   - Share HMAC authentication documentation
   - Provide code examples
   - Set up test environment access

4. **Test Integration**
   - Verify partner can authenticate
   - Test error scenarios
   - Validate request/response format

## Troubleshooting

### Common Issues

1. **Clock Synchronization**
   - Symptoms: EXPIRED_TIMESTAMP errors
   - Solution: Ensure client/server clocks synchronized
   - Workaround: Increase HMAC_TIMESTAMP_WINDOW

2. **Secret Mismatch**
   - Symptoms: INVALID_SIGNATURE errors
   - Solution: Verify partner secret configuration
   - Check: Environment variable naming

3. **Header Format**
   - Symptoms: MISSING_* errors
   - Solution: Verify header names and format
   - Check: X-Signature format (sha256=...)

4. **URL Encoding**
   - Symptoms: INVALID_SIGNATURE for URLs with special chars
   - Solution: Ensure consistent URL encoding
   - Check: Query parameter encoding

### Debug Commands

```bash
# Check environment variables
amplify env get --environment production

# View recent logs
amplify console api

# Test specific endpoint
curl -v -X GET "https://yourapp.com/api/search?q=test" \
  -H "X-Signature: sha256=test" \
  -H "X-Timestamp: $(date +%s)" \
  -H "X-Partner-ID: default"
```

## Security Considerations

### Production Security

1. **HTTPS Only**
   - Ensure all requests use HTTPS
   - Configure HSTS headers
   - Disable HTTP redirects

2. **Rate Limiting**
   - Implement per-partner rate limits
   - Monitor for abuse patterns
   - Set up automatic blocking

3. **Secret Rotation**
   - Plan regular secret rotation
   - Implement graceful secret updates
   - Maintain secret history for transition

### Monitoring Security Events

1. **Failed Authentication Patterns**
   - Multiple failures from same IP
   - Unusual request timing
   - Invalid signature attempts

2. **Replay Attack Detection**
   - Duplicate signature usage
   - Timestamp manipulation attempts
   - Unusual request patterns

## Success Criteria

### Deployment Success Indicators

- [ ] All authentication endpoints return 401 for unauthenticated requests
- [ ] Valid HMAC requests return 200 with expected data
- [ ] JWT authentication continues to work for frontend
- [ ] No increase in error rates
- [ ] Performance metrics within acceptable range
- [ ] All monitoring alerts configured and working

### Performance Benchmarks

- Authentication latency: < 10ms
- Request success rate: > 99.9%
- Error rate: < 0.1%
- Memory usage: No significant increase

## Post-Deployment Tasks

1. **Documentation Updates**
   - Update API documentation
   - Notify partner integrations
   - Update internal runbooks

2. **Monitoring Setup**
   - Configure dashboards
   - Set up alerting rules
   - Document incident response

3. **Partner Communication**
   - Notify partners of new authentication
   - Provide migration timeline
   - Offer integration support

## Contact Information

- **Development Team**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **On-call**: +1-xxx-xxx-xxxx

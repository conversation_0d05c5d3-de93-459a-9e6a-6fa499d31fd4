# PR 2 Technical Specifications - HMAC Authentication System

**Date:** January 12, 2025  
**Sprint:** Auth Layer Implementation  
**Component:** HMAC Helper + Search Routes Protection  

## 1. HMAC Authentication Specification

### Signature Algorithm: HMAC-SHA256

#### Message Construction
```
message = HTTP_METHOD + '\n' + 
          REQUEST_PATH + '\n' + 
          TIMESTAMP + '\n' + 
          SHA256(REQUEST_BODY || '')
```

#### Example Message
```
GET
/api/search
1705123456
e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
```

#### Signature Generation
```typescript
const signature = crypto
  .createHmac('sha256', partnerSecret)
  .update(message)
  .digest('hex')
```

### Request Headers Format

#### Required Headers
```http
X-Signature: sha256=<hex_signature>
X-Timestamp: <unix_timestamp>
X-Partner-ID: <partner_identifier>
Content-Type: application/json
```

#### Optional Headers
```http
X-Nonce: <unique_request_id>  # For additional replay protection
X-Version: 1.0                # API version for future compatibility
```

### Partner Configuration

#### Environment Variables Structure
```bash
# Default partner secret (fallback)
PARTNER_SECRET_DEFAULT=your-default-secret-minimum-32-characters

# Named partner secrets
PARTNER_SECRET_ACME=acme-corp-secret-minimum-32-characters
PARTNER_SECRET_BETA=beta-partner-secret-minimum-32-characters

# HMAC configuration
HMAC_TIMESTAMP_WINDOW=300     # 5 minutes (single source of truth)
HMAC_ALGORITHM=sha256
HMAC_ENCODING=hex

# Feature flags for rollback capability
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
```

#### Secret Lifecycle Management

##### Secret Creation
```bash
# Generate cryptographically secure secrets (32+ characters)
openssl rand -hex 32  # Generates 64-character hex string
```

##### Secret Storage by Environment
- **Development**: `.env.local` file (not committed to git)
- **Staging**: AWS Amplify Environment Variables (staging app)
- **Production**: AWS Amplify Environment Secrets (production app)

##### Secret Rotation Procedure
1. **Generate new secret**: `openssl rand -hex 32`
2. **Update environment**: Add new secret alongside old one
3. **Deploy with dual support**: Accept both old and new secrets
4. **Partner migration**: Notify partners to update to new secret
5. **Remove old secret**: After 30-day grace period

##### Secret Revocation
1. **Immediate**: Remove from environment variables
2. **Deploy**: Redeploy application to pick up changes
3. **Monitor**: Watch for authentication failures from revoked partner

## 2. TypeScript Interfaces

### Core Types
```typescript
interface HMACData {
  signature: string
  timestamp: number
  partnerId: string
  nonce?: string
  version?: string
}

interface HMACConfig {
  algorithm: 'sha256'
  timestampWindow: number
  encoding: 'hex'
  requireNonce: boolean
  enableReplayProtection: boolean
}

interface HMACPayload {
  partnerId: string
  timestamp: number
  method: string
  path: string
  isValid: boolean
  nonce?: string
}

interface AuthResult {
  success: boolean
  method: 'JWT' | 'HMAC' | null
  payload: JWTPayload | HMACPayload | null
  error?: string
}

interface HMACConfig {
  algorithm: 'sha256'
  timestampWindow: number
  encoding: 'hex'
  requireNonce: boolean
}
```

### Error Types
```typescript
type HMACError =
  | 'MISSING_SIGNATURE'
  | 'MISSING_TIMESTAMP'
  | 'MISSING_PARTNER_ID'
  | 'INVALID_SIGNATURE'
  | 'EXPIRED_TIMESTAMP'
  | 'UNKNOWN_PARTNER'
  | 'INVALID_FORMAT'
  | 'REPLAY_DETECTED'
  | 'BODY_HASH_MISMATCH'

#### Error Code to Response Mapping
| Error Code | HTTP Status | Response Message | Log Level |
|------------|-------------|------------------|-----------|
| `MISSING_SIGNATURE` | 401 | "Missing X-Signature header" | WARN |
| `MISSING_TIMESTAMP` | 401 | "Missing X-Timestamp header" | WARN |
| `MISSING_PARTNER_ID` | 401 | "Missing X-Partner-ID header" | WARN |
| `INVALID_SIGNATURE` | 401 | "Invalid HMAC signature" | WARN |
| `EXPIRED_TIMESTAMP` | 401 | "Request timestamp expired" | WARN |
| `UNKNOWN_PARTNER` | 401 | "Unknown partner ID" | ERROR |
| `INVALID_FORMAT` | 400 | "Invalid request format" | WARN |
| `REPLAY_DETECTED` | 401 | "Duplicate request detected" | ERROR |
| `BODY_HASH_MISMATCH` | 401 | "Request body hash mismatch" | ERROR |

interface HMACValidationResult {
  isValid: boolean
  error?: HMACError
  partnerId?: string
  timestamp?: number
}
```

## 3. Implementation Details

### HMAC Helper Functions

#### Core Signature Functions
```typescript
// Generate signature for outgoing requests
export function generateHMACSignature(
  method: string,
  path: string,
  timestamp: number,
  body: string = '',
  secret: string
): string {
  const bodyHash = crypto.createHash('sha256').update(body).digest('hex')
  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`
  
  return crypto
    .createHmac('sha256', secret)
    .update(message)
    .digest('hex')
}

// Verify incoming signature
export function verifyHMACSignature(
  signature: string,
  method: string,
  path: string,
  timestamp: number,
  body: string = '',
  secret: string
): boolean {
  const expectedSignature = generateHMACSignature(method, path, timestamp, body, secret)
  
  // Use constant-time comparison to prevent timing attacks
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  )
}
```

#### Request Processing Functions
```typescript
// Extract HMAC data from request headers
export function extractHMACFromRequest(request: NextRequest): HMACData | null {
  const signature = request.headers.get('x-signature')
  const timestamp = request.headers.get('x-timestamp')
  const partnerId = request.headers.get('x-partner-id')
  
  if (!signature || !timestamp || !partnerId) {
    return null
  }
  
  // Remove 'sha256=' prefix if present
  const cleanSignature = signature.replace(/^sha256=/, '')
  
  return {
    signature: cleanSignature,
    timestamp: parseInt(timestamp, 10),
    partnerId,
    nonce: request.headers.get('x-nonce') || undefined,
    version: request.headers.get('x-version') || undefined
  }
}

// Comprehensive request verification
export async function verifyRequestHMAC(request: NextRequest): Promise<HMACPayload | null> {
  const hmacData = extractHMACFromRequest(request)
  if (!hmacData) {
    return null
  }
  
  // Validate timestamp window
  const now = Math.floor(Date.now() / 1000)
  const timestampWindow = parseInt(process.env.HMAC_TIMESTAMP_WINDOW || '300', 10)
  
  if (Math.abs(now - hmacData.timestamp) > timestampWindow) {
    console.warn(`HMAC timestamp expired: ${hmacData.timestamp}, now: ${now}`)
    return null
  }
  
  // Get partner secret
  const partnerSecret = getPartnerSecret(hmacData.partnerId)
  if (!partnerSecret) {
    console.warn(`Unknown partner ID: ${hmacData.partnerId}`)
    return null
  }
  
  // Get request body for signature verification
  const body = await getRequestBody(request)
  const { pathname } = new URL(request.url)
  
  // Verify signature
  const isValid = verifyHMACSignature(
    hmacData.signature,
    request.method,
    pathname,
    hmacData.timestamp,
    body,
    partnerSecret
  )
  
  if (!isValid) {
    console.warn(`HMAC signature verification failed for partner: ${hmacData.partnerId}`)
    return null
  }
  
  return {
    partnerId: hmacData.partnerId,
    timestamp: hmacData.timestamp,
    method: request.method,
    path: pathname,
    isValid: true,
    nonce: hmacData.nonce
  }
}
```

#### Utility Functions
```typescript
// Get partner secret from environment
function getPartnerSecret(partnerId: string): string | null {
  const secretKey = `PARTNER_SECRET_${partnerId.toUpperCase()}`
  const secret = process.env[secretKey] || process.env.PARTNER_SECRET_DEFAULT
  
  if (!secret) {
    console.error(`No secret found for partner: ${partnerId}`)
    return null
  }
  
  if (secret.length < 32) {
    console.error(`Partner secret too short for: ${partnerId}`)
    return null
  }
  
  return secret
}

// Get request body (handling different content types)
async function getRequestBody(request: NextRequest): Promise<string> {
  try {
    if (request.method === 'GET' || request.method === 'HEAD') {
      return ''
    }
    
    const contentType = request.headers.get('content-type') || ''
    
    if (contentType.includes('application/json')) {
      const body = await request.json()
      return JSON.stringify(body)
    }
    
    if (contentType.includes('application/x-www-form-urlencoded')) {
      const formData = await request.formData()
      return new URLSearchParams(formData as any).toString()
    }
    
    // For other content types, get raw text
    return await request.text()
  } catch (error) {
    console.warn('Failed to parse request body for HMAC verification:', error)
    return ''
  }
}

// Create HMAC headers for outgoing requests
export function createHMACHeaders(
  method: string,
  path: string,
  partnerId: string,
  body: string = '',
  secret?: string
): Record<string, string> {
  const timestamp = Math.floor(Date.now() / 1000)
  const partnerSecret = secret || getPartnerSecret(partnerId)
  
  if (!partnerSecret) {
    throw new Error(`No secret available for partner: ${partnerId}`)
  }
  
  const signature = generateHMACSignature(method, path, timestamp, body, partnerSecret)
  
  return {
    'X-Signature': `sha256=${signature}`,
    'X-Timestamp': timestamp.toString(),
    'X-Partner-ID': partnerId,
    'Content-Type': 'application/json'
  }
}
```

## 4. Search Endpoint Integration

### Feature Flag Implementation
```typescript
// Check if authentication is enabled via feature flags
function isAuthenticationEnabled(): boolean {
  return process.env.ENABLE_SEARCH_AUTH === 'true'
}

function isHMACEnabled(): boolean {
  return process.env.ENABLE_HMAC_AUTH === 'true'
}

// Feature flag guards for rollback capability
function shouldEnforceAuthentication(endpoint: string): boolean {
  if (!isAuthenticationEnabled()) {
    console.log(`Authentication disabled via feature flag for ${endpoint}`)
    return false
  }
  return true
}
```

### Nonce Strategy (MVP: In-Memory Replay Protection)
```typescript
// Simple in-memory nonce tracking for MVP
// Note: For production scale, migrate to Redis
const recentRequests = new Map<string, number>()

function isReplayRequest(signature: string, timestamp: number): boolean {
  const key = `${signature}-${timestamp}`
  const now = Date.now() / 1000

  // Clean old entries (older than timestamp window)
  const timestampWindow = parseInt(process.env.HMAC_TIMESTAMP_WINDOW || '300', 10)
  for (const [existingKey, existingTime] of recentRequests.entries()) {
    if (now - existingTime > timestampWindow) {
      recentRequests.delete(existingKey)
    }
  }

  // Check if this request was seen before
  if (recentRequests.has(key)) {
    return true // Replay detected
  }

  // Store this request
  recentRequests.set(key, timestamp)
  return false
}
```

### Authentication Middleware
```typescript
// Dual authentication check for search endpoints
async function authenticateSearchRequest(request: NextRequest): Promise<AuthResult> {
  // Try JWT first (browser users with CAPTCHA)
  const jwtPayload = await verifyRequestJWT(request)
  if (jwtPayload) {
    return {
      success: true,
      method: 'JWT',
      payload: jwtPayload
    }
  }
  
  // Try HMAC second (API partners)
  const hmacPayload = await verifyRequestHMAC(request)
  if (hmacPayload) {
    return {
      success: true,
      method: 'HMAC',
      payload: hmacPayload
    }
  }
  
  return {
    success: false,
    method: null,
    payload: null,
    error: 'No valid authentication found'
  }
}

// Create consistent unauthorized response
function createSearchUnauthorizedResponse(): NextResponse {
  return NextResponse.json(
    {
      error: 'Unauthorized',
      message: 'Valid authentication required. Use JWT (browser) or HMAC (API) authentication.',
      code: 'AUTH_REQUIRED',
      supportedMethods: ['JWT', 'HMAC']
    },
    {
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'WWW-Authenticate': 'Bearer realm="Search API", HMAC realm="Partner API"'
      }
    }
  )
}
```

### Endpoint Implementation Pattern
```typescript
// Example: /api/search/route.ts integration
export async function GET(request: NextRequest): Promise<NextResponse> {
  // Apply rate limiting first
  const rateLimitResponse = applyRateLimit(request, rateLimits.search)
  if (rateLimitResponse) {
    return rateLimitResponse
  }
  
  // Apply authentication
  const authResult = await authenticateSearchRequest(request)
  if (!authResult.success) {
    console.warn(`Search API unauthorized access attempt from ${getClientIP(request)}`)
    return createSearchUnauthorizedResponse()
  }
  
  // Log successful authentication
  console.log(`Search API access: ${authResult.method} authentication successful`)
  
  // Continue with existing search logic...
  const { searchParams } = new URL(request.url)
  // ... rest of existing implementation
}
```

## 5. Error Handling & Logging

### Security Event Logging
```typescript
interface SecurityEvent {
  type: 'HMAC_AUTH_SUCCESS' | 'HMAC_AUTH_FAILURE' | 'JWT_AUTH_SUCCESS' | 'JWT_AUTH_FAILURE'
  endpoint: string
  method: string
  partnerId?: string
  ip: string
  timestamp: string
  traceId: string
  error?: HMACError
  errorMessage?: string
}

function logSecurityEvent(event: SecurityEvent): void {
  const logEntry = {
    level: event.type.includes('FAILURE') ? 'WARN' : 'INFO',
    message: `Security Event: ${event.type}`,
    traceId: event.traceId,
    endpoint: event.endpoint,
    method: event.method,
    partnerId: event.partnerId,
    ip: event.ip,
    timestamp: event.timestamp,
    error: event.error,
    errorMessage: event.errorMessage
  }

  // Log to CloudWatch via console (structured logging)
  console.log(JSON.stringify(logEntry))

  // Optional: Send to additional monitoring services
  if (process.env.NODE_ENV === 'production') {
    // Could integrate with Datadog, New Relic, etc.
  }
}

// Generate trace ID for request correlation
function generateTraceId(): string {
  return `hmac-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
}
```

### Error Response Generation
```typescript
function createHMACErrorResponse(error: HMACError, traceId: string): NextResponse {
  const errorMap = {
    MISSING_SIGNATURE: { status: 401, message: "Missing X-Signature header" },
    MISSING_TIMESTAMP: { status: 401, message: "Missing X-Timestamp header" },
    MISSING_PARTNER_ID: { status: 401, message: "Missing X-Partner-ID header" },
    INVALID_SIGNATURE: { status: 401, message: "Invalid HMAC signature" },
    EXPIRED_TIMESTAMP: { status: 401, message: "Request timestamp expired" },
    UNKNOWN_PARTNER: { status: 401, message: "Unknown partner ID" },
    INVALID_FORMAT: { status: 400, message: "Invalid request format" },
    REPLAY_DETECTED: { status: 401, message: "Duplicate request detected" },
    BODY_HASH_MISMATCH: { status: 401, message: "Request body hash mismatch" }
  }

  const { status, message } = errorMap[error]

  return NextResponse.json(
    {
      error: status === 401 ? 'Unauthorized' : 'Bad Request',
      message,
      code: error,
      traceId,
      supportedMethods: ['JWT', 'HMAC']
    },
    {
      status,
      headers: {
        'Content-Type': 'application/json',
        'X-Trace-ID': traceId,
        'WWW-Authenticate': 'Bearer realm="Search API", HMAC realm="Partner API"'
      }
    }
  )
}
```

### Error Response Standards
```typescript
// Consistent error responses across all search endpoints
const AUTH_ERROR_RESPONSES = {
  MISSING_AUTH: {
    error: 'Unauthorized',
    message: 'Authentication required. Provide JWT token or HMAC signature.',
    code: 'AUTH_MISSING'
  },
  INVALID_JWT: {
    error: 'Unauthorized', 
    message: 'Invalid JWT token. Please re-authenticate.',
    code: 'JWT_INVALID'
  },
  INVALID_HMAC: {
    error: 'Unauthorized',
    message: 'Invalid HMAC signature. Check signature calculation.',
    code: 'HMAC_INVALID'
  },
  EXPIRED_TIMESTAMP: {
    error: 'Unauthorized',
    message: 'Request timestamp expired. Check system clock.',
    code: 'TIMESTAMP_EXPIRED'
  }
}
```

## 6. Testing Specifications

### Test Data Setup
```typescript
// Test partner configuration
const TEST_PARTNERS = {
  'test-partner': 'test-secret-minimum-32-characters-long',
  'acme-corp': 'acme-secret-minimum-32-characters-long'
}

// Test request scenarios
const TEST_SCENARIOS = [
  {
    name: 'Valid HMAC Request',
    method: 'GET',
    path: '/api/search',
    partnerId: 'test-partner',
    expectSuccess: true
  },
  {
    name: 'Expired Timestamp',
    method: 'GET', 
    path: '/api/search',
    partnerId: 'test-partner',
    timestampOffset: -400, // 400 seconds ago
    expectSuccess: false
  },
  {
    name: 'Invalid Signature',
    method: 'GET',
    path: '/api/search',
    partnerId: 'test-partner',
    invalidSignature: true,
    expectSuccess: false
  }
]
```

---

**Next Steps:**
1. Review technical specifications with development team
2. Validate HMAC algorithm choice and parameters
3. Confirm partner secret management approach
4. Begin implementation of `src/lib/security/hmac.ts`

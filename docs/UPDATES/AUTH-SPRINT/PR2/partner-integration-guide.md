# Partner Integration Guide - HMAC Authentication

**Date:** January 12, 2025  
**API Version:** 1.0  
**Authentication Method:** HMAC-SHA256  

## Overview

This guide provides technical documentation for API partners to integrate with the Cashback Deals search APIs using HMAC authentication. HMAC (Hash-based Message Authentication Code) provides secure, stateless authentication for programmatic access to our search endpoints.

## Authentication Method: HMAC-SHA256

### Why HMAC?
- **Stateless**: No server-side session management required
- **Secure**: Cryptographic signature prevents tampering
- **Replay Protection**: Timestamp validation prevents replay attacks
- **Partner-Specific**: Individual secrets for each partner

## Getting Started

### 1. Sandbox Credentials (Immediate Testing)
Start testing immediately with these sandbox credentials:

```bash
# Sandbox Environment
Base URL: https://staging-api.cashback-deals.com
Partner ID: sandbox-partner
Partner Secret: sandbox-secret-32-chars-minimum-length-test
Rate Limit: 100 requests per minute
Valid Until: 30 days from first use
```

**⚠️ Sandbox Limitations:**
- Limited to staging environment only
- 30-day expiration from first use
- Shared credentials (not for production)
- Rate limited to 100 requests/minute

### 2. Production Partner Registration
For production access, contact our API team to receive:
- **Partner ID**: Your unique identifier (e.g., `acme-corp`)
- **Partner Secret**: 32+ character secret key for signature generation
- **API Endpoints**: List of accessible endpoints
- **Rate Limits**: Your specific rate limiting configuration

### 3. Required Headers
Every authenticated request must include:

```http
X-Signature: sha256=<hex_signature>
X-Timestamp: <unix_timestamp>
X-Partner-ID: <your_partner_id>
Content-Type: application/json
```

### 4. Optional Headers
For enhanced functionality:

```http
X-Version: 1.0                    # API version (recommended)
X-Nonce: <unique_request_id>      # Additional replay protection
```

### 3. Signature Generation

#### Step 1: Create Message String
```
message = HTTP_METHOD + '\n' + 
          REQUEST_PATH + '\n' + 
          TIMESTAMP + '\n' + 
          SHA256(REQUEST_BODY)
```

#### Step 2: Generate HMAC Signature
```
signature = HMAC-SHA256(message, partner_secret)
```

#### Step 3: Format Headers
```http
X-Signature: sha256=<hex_signature>
X-Timestamp: <unix_timestamp>
X-Partner-ID: <your_partner_id>
```

## Implementation Examples

### JavaScript/Node.js
```javascript
const crypto = require('crypto');

function generateHMACSignature(method, path, timestamp, body, secret) {
  // Create body hash (empty string for GET requests)
  const bodyHash = crypto.createHash('sha256').update(body || '').digest('hex');
  
  // Create message string
  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`;
  
  // Generate HMAC signature
  const signature = crypto
    .createHmac('sha256', secret)
    .update(message)
    .digest('hex');
    
  return signature;
}

function createAuthHeaders(method, path, partnerId, body, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const signature = generateHMACSignature(method, path, timestamp, body, secret);
  
  return {
    'X-Signature': `sha256=${signature}`,
    'X-Timestamp': timestamp.toString(),
    'X-Partner-ID': partnerId,
    'Content-Type': 'application/json'
  };
}

// Example usage
const headers = createAuthHeaders(
  'GET',
  '/api/search',
  'acme-corp',
  '',
  'your-partner-secret'
);

// Make authenticated request
fetch('https://api.cashback-deals.com/api/search?q=laptop', {
  method: 'GET',
  headers: headers
})
.then(response => response.json())
.then(data => console.log(data));
```

### Python
```python
import hashlib
import hmac
import time
import requests

def generate_hmac_signature(method, path, timestamp, body, secret):
    # Create body hash
    body_hash = hashlib.sha256((body or '').encode()).hexdigest()
    
    # Create message string
    message = f"{method}\n{path}\n{timestamp}\n{body_hash}"
    
    # Generate HMAC signature
    signature = hmac.new(
        secret.encode(),
        message.encode(),
        hashlib.sha256
    ).hexdigest()
    
    return signature

def create_auth_headers(method, path, partner_id, body, secret):
    timestamp = int(time.time())
    signature = generate_hmac_signature(method, path, timestamp, body, secret)
    
    return {
        'X-Signature': f'sha256={signature}',
        'X-Timestamp': str(timestamp),
        'X-Partner-ID': partner_id,
        'Content-Type': 'application/json'
    }

# Example usage
headers = create_auth_headers(
    'GET',
    '/api/search',
    'acme-corp',
    '',
    'your-partner-secret'
)

response = requests.get(
    'https://api.cashback-deals.com/api/search?q=laptop',
    headers=headers
)
print(response.json())
```

### PHP
```php
<?php
function generateHMACSignature($method, $path, $timestamp, $body, $secret) {
    // Create body hash
    $bodyHash = hash('sha256', $body ?: '');
    
    // Create message string
    $message = "$method\n$path\n$timestamp\n$bodyHash";
    
    // Generate HMAC signature
    $signature = hash_hmac('sha256', $message, $secret);
    
    return $signature;
}

function createAuthHeaders($method, $path, $partnerId, $body, $secret) {
    $timestamp = time();
    $signature = generateHMACSignature($method, $path, $timestamp, $body, $secret);
    
    return [
        'X-Signature' => "sha256=$signature",
        'X-Timestamp' => (string)$timestamp,
        'X-Partner-ID' => $partnerId,
        'Content-Type' => 'application/json'
    ];
}

// Example usage
$headers = createAuthHeaders(
    'GET',
    '/api/search',
    'acme-corp',
    '',
    'your-partner-secret'
);

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => implode("\r\n", array_map(
            fn($k, $v) => "$k: $v",
            array_keys($headers),
            $headers
        ))
    ]
]);

$response = file_get_contents(
    'https://api.cashback-deals.com/api/search?q=laptop',
    false,
    $context
);
echo $response;
?>
```

## Available Endpoints

### Search API
- **Endpoint**: `/api/search`
- **Method**: GET
- **Description**: Search products with filtering and sorting
- **Rate Limit**: 100 requests per minute

#### Parameters
```
q          - Search query string
category   - Category filter
brand      - Brand filter  
sort       - Sort order (relevance, price_asc, price_desc, newest)
page       - Page number (default: 1)
limit      - Items per page (default: 20, max: 50)
```

#### Example GET Request
```bash
curl -X GET "https://api.cashback-deals.com/api/search?q=laptop&brand=samsung&page=1&limit=20" \
  -H "X-Signature: sha256=abc123..." \
  -H "X-Timestamp: 1705123456" \
  -H "X-Partner-ID: acme-corp" \
  -H "X-Version: 1.0" \
  -H "Content-Type: application/json"
```

#### Example POST Request with Body
```bash
# Generate signature for this exact body
BODY='{"query":"laptop","filters":{"brand":"samsung","priceRange":{"min":500,"max":1500}}}'
TIMESTAMP=$(date +%s)
SIGNATURE=$(echo -n "POST\n/api/search\n$TIMESTAMP\n$(echo -n "$BODY" | sha256sum | cut -d' ' -f1)" | \
  openssl dgst -sha256 -hmac "your-partner-secret" | cut -d' ' -f2)

curl -X POST "https://api.cashback-deals.com/api/search" \
  -H "X-Signature: sha256=$SIGNATURE" \
  -H "X-Timestamp: $TIMESTAMP" \
  -H "X-Partner-ID: acme-corp" \
  -H "X-Version: 1.0" \
  -H "Content-Type: application/json" \
  -d "$BODY"
```

### Search Suggestions API
- **Endpoint**: `/api/search/suggestions`
- **Method**: GET
- **Description**: Get search suggestions and autocomplete
- **Rate Limit**: 100 requests per minute

#### Parameters
```
q     - Search query string (minimum 2 characters)
limit - Maximum suggestions (default: 10, max: 20)
```

### Search More API
- **Endpoint**: `/api/search/more`
- **Method**: GET
- **Description**: Get additional search results for pagination
- **Rate Limit**: 100 requests per minute

## Error Handling

### Complete Error Catalog

| Error Code | HTTP Status | Description | Resolution |
|------------|-------------|-------------|------------|
| `AUTH_MISSING` | 401 | No authentication headers provided | Add X-Signature, X-Timestamp, X-Partner-ID headers |
| `MISSING_SIGNATURE` | 401 | X-Signature header missing | Add X-Signature header with sha256= prefix |
| `MISSING_TIMESTAMP` | 401 | X-Timestamp header missing | Add X-Timestamp header with Unix timestamp |
| `MISSING_PARTNER_ID` | 401 | X-Partner-ID header missing | Add X-Partner-ID header with your partner ID |
| `INVALID_SIGNATURE` | 401 | HMAC signature verification failed | Check signature calculation and secret |
| `EXPIRED_TIMESTAMP` | 401 | Request timestamp outside 5-minute window | Check system clock and timestamp generation |
| `UNKNOWN_PARTNER` | 401 | Partner ID not recognized | Verify partner ID or contact support |
| `REPLAY_DETECTED` | 401 | Duplicate request detected | Use unique timestamps or add nonce |
| `BODY_HASH_MISMATCH` | 401 | Request body doesn't match signature | Ensure body used for signature matches sent body |
| `INVALID_FORMAT` | 400 | Malformed request headers | Check header format and values |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests | Implement exponential backoff |

### Example Error Responses

#### Authentication Missing
```json
{
  "error": "Unauthorized",
  "message": "Valid authentication required. Use JWT (browser) or HMAC (API) authentication.",
  "code": "AUTH_MISSING",
  "traceId": "hmac-get-a1b2-c3d4e5f6",
  "serverTime": "2025-01-12T10:30:00.000Z",
  "supportedMethods": ["JWT", "HMAC"],
  "documentation": "https://docs.cashback-deals.com/api/authentication"
}
```

#### Invalid Signature
```json
{
  "error": "Unauthorized",
  "message": "Invalid HMAC signature",
  "code": "INVALID_SIGNATURE",
  "traceId": "hmac-get-a1b2-c3d4e5f6",
  "serverTime": "2025-01-12T10:30:00.000Z",
  "supportedMethods": ["JWT", "HMAC"],
  "documentation": "https://docs.cashback-deals.com/api/authentication"
}
```

#### Timestamp Expired
```json
{
  "error": "Unauthorized",
  "message": "Request timestamp expired",
  "code": "EXPIRED_TIMESTAMP",
  "traceId": "hmac-get-a1b2-c3d4e5f6",
  "serverTime": "2025-01-12T10:30:00.000Z",
  "supportedMethods": ["JWT", "HMAC"],
  "documentation": "https://docs.cashback-deals.com/api/authentication"
}
```

### Rate Limiting
```json
{
  "error": "Too many requests",
  "message": "Rate limit exceeded. Try again in 45 seconds."
}
```

Response headers include:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Requests remaining in window
- `X-RateLimit-Reset`: Unix timestamp when limit resets
- `Retry-After`: Seconds to wait before retrying

## Security Best Practices

### Secret Management
- **Never commit secrets to version control**
- **Use environment variables for secret storage**
- **Rotate secrets regularly (quarterly recommended)**
- **Use different secrets for development/staging/production**

### Request Security
- **Always use HTTPS in production**
- **Validate timestamp is within 5-minute window**
- **Implement request timeout handling**
- **Log authentication failures for monitoring**

### Error Handling
- **Don't expose sensitive information in error messages**
- **Implement exponential backoff for retries**
- **Monitor authentication success/failure rates**
- **Set up alerts for unusual authentication patterns**

## Testing Your Integration

### Test Environment
- **Base URL**: `https://staging-api.cashback-deals.com`
- **Test Partner ID**: `test-partner`
- **Test Secret**: `test-secret-minimum-32-characters-long`

### Validation Checklist
- [ ] Signature generation produces expected results
- [ ] Timestamp is current Unix timestamp
- [ ] Headers are properly formatted
- [ ] HTTPS is used for all requests
- [ ] Error responses are handled gracefully
- [ ] Rate limiting is respected

### Test Signature Generation
Use these test vectors to validate your implementation:

```
Method: GET
Path: /api/search
Timestamp: 1705123456
Body: (empty)
Secret: test-secret-minimum-32-characters-long

Expected Message:
GET
/api/search
1705123456
e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855

Expected Signature:
a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
```

## Support

### Technical Support
- **Email**: <EMAIL>
- **Documentation**: https://docs.cashback-deals.com/api
- **Status Page**: https://status.cashback-deals.com

### Partner Onboarding
- **Business Contact**: <EMAIL>
- **Technical Contact**: <EMAIL>

---

**Last Updated:** January 12, 2025  
**API Version:** 1.0  
**Document Version:** 1.0

# Environment Variables for HMAC Authentication

## Required Environment Variables

### Authentication Control
```bash
# Master switch for search endpoint authentication
ENABLE_SEARCH_AUTH=true

# Enable HMAC authentication specifically
ENABLE_HMAC_AUTH=true
```

### JWT Configuration
```bash
# JWT secret for token signing and verification
# Must be at least 32 characters for security
JWT_SECRET=your-secure-jwt-secret-key-minimum-32-chars
```

### HMAC Configuration
```bash
# Timestamp window for replay protection (in seconds)
# Default: 300 (5 minutes)
HMAC_TIMESTAMP_WINDOW=300

# Default partner secret (fallback for all partners)
# Must be at least 32 characters for security
PARTNER_SECRET_DEFAULT=your-default-partner-secret-32-chars-min
```

### Partner-Specific Secrets
```bash
# Partner-specific secrets (recommended for production)
# Format: PARTNER_SECRET_{PARTNER_ID_UPPERCASE}
PARTNER_SECRET_ACME=acme-corp-secret-key-32-chars-minimum
PARTNER_SECRET_PARTNER1=partner1-secret-key-32-chars-minimum
PARTNER_SECRET_PARTNER2=partner2-secret-key-32-chars-minimum
```

## Environment-Specific Configuration

### Development
```bash
# Development environment
NODE_ENV=development
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
JWT_SECRET=dev-jwt-secret-key-minimum-32-characters
HMAC_TIMESTAMP_WINDOW=600  # 10 minutes for development
PARTNER_SECRET_DEFAULT=dev-hmac-secret-min-32-chars-required-for-security
```

### Staging
```bash
# Staging environment
NODE_ENV=staging
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
JWT_SECRET=staging-jwt-secret-key-minimum-32-characters
HMAC_TIMESTAMP_WINDOW=300  # 5 minutes
PARTNER_SECRET_DEFAULT=staging-partner-secret-32-chars-minimum
```

### Production
```bash
# Production environment
NODE_ENV=production
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
JWT_SECRET=production-jwt-secret-key-minimum-32-characters
HMAC_TIMESTAMP_WINDOW=300  # 5 minutes
PARTNER_SECRET_DEFAULT=production-partner-secret-32-chars-minimum
```

## Security Requirements

### Secret Length
- All secrets must be at least 32 characters long
- Use cryptographically secure random strings
- Avoid predictable patterns or dictionary words

### Secret Generation
```bash
# Generate secure random secrets
openssl rand -hex 32  # 64-character hex string
openssl rand -base64 32  # 44-character base64 string
```

### Secret Rotation
- Rotate secrets regularly (quarterly recommended)
- Maintain old secrets during transition period
- Update all partner integrations before removing old secrets

## AWS Amplify Configuration

### Setting Environment Variables
1. Navigate to AWS Amplify Console
2. Select your application
3. Go to "Environment variables" section
4. Add the required variables for each environment

### Environment Secrets
Store sensitive values as environment secrets:
- `JWT_SECRET`
- `PARTNER_SECRET_DEFAULT`
- `PARTNER_SECRET_*` (for each partner)

## Validation

### Startup Validation
The application validates configuration at startup:
- Checks for required environment variables
- Validates secret lengths
- Logs configuration errors

### Runtime Validation
- Partner secrets are validated on each request
- Invalid configurations result in authentication failures
- Errors are logged for debugging

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   ```
   Error: HMAC_SECRET environment variable is required in production
   ```
   Solution: Set the required environment variables

2. **Secret Too Short**
   ```
   Error: Partner secret too short (minimum 32 characters required)
   ```
   Solution: Generate longer secrets using secure methods

3. **Authentication Disabled**
   ```
   Warning: HMAC authentication will be disabled due to configuration errors
   ```
   Solution: Check all environment variables are properly set

### Debug Commands
```bash
# Check environment variables (be careful not to expose secrets)
echo $ENABLE_SEARCH_AUTH
echo $ENABLE_HMAC_AUTH
echo ${#JWT_SECRET}  # Shows length without exposing value
echo ${#PARTNER_SECRET_DEFAULT}  # Shows length without exposing value
```

## Migration Guide

### From No Authentication
1. Set `ENABLE_SEARCH_AUTH=false` initially
2. Deploy with authentication code
3. Test with `ENABLE_SEARCH_AUTH=true` in staging
4. Enable authentication in production

### Adding New Partners
1. Generate new partner secret
2. Add `PARTNER_SECRET_{PARTNER_ID}` environment variable
3. Provide integration documentation to partner
4. Test authentication before going live

## Best Practices

### Security
- Never commit secrets to version control
- Use different secrets for each environment
- Rotate secrets regularly
- Monitor authentication logs for suspicious activity

### Operations
- Document all partner configurations
- Set up monitoring for authentication failures
- Have rollback procedures ready
- Test secret rotation procedures

### Development
- Use consistent naming conventions
- Document partner onboarding process
- Maintain integration examples
- Keep environment documentation updated

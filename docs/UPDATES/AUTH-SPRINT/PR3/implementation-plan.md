# PR3: Sentry Cleanup & IP Allowlist - Implementation Plan

**Date:** July 13, 2025  
**Sprint:** Auth-Sprint Phase 1  
**Total Estimated Time:** ≤ 4 hours  
**Risk Level:** Low  

## 🎯 Implementation Overview

Two-phase approach to remove development endpoints and implement reusable IP allowlist middleware for secure internal tool access.

## 📋 Phase 1: Sentry Cleanup (2 hours)

### 1.1 Remove Development Endpoints (45 minutes)

#### Remove API Debug Route
- **File**: `src/app/api/sentry-example-api/route.ts`
- **Action**: Delete entire directory and file
- **Rationale**: Intentionally throws errors, no production value

#### Remove Frontend Debug Page  
- **File**: `src/app/sentry-example-page/page.tsx`
- **Action**: Delete entire directory and file
- **Rationale**: Public testing interface with external Sentry links

#### Update Next.js Configuration
- **File**: `next.config.js`
- **Action**: Remove or conditionally include based on `NODE_ENV`
- **Change**: Wrap Sentry config with environment check

```typescript
// Example change pattern
const nextConfig = {
  // ... existing config
};

// Only enable Sentry in production and staging
if (process.env.NODE_ENV !== 'development' || process.env.ENABLE_SENTRY_LOCAL === 'true') {
  module.exports = withSentryConfig(nextConfig, sentryWebpackOptions);
} else {
  module.exports = nextConfig;
}
```

### 1.2 Environment-Based Sentry Configuration (45 minutes)

#### Update Server Configuration
- **File**: `sentry.server.config.ts`
- **Changes**:
  - Move DSN to `process.env.SENTRY_DSN`
  - Environment-based trace sampling rate
  - Conditional initialization

```typescript
import * as Sentry from "@sentry/nextjs";

const isProduction = process.env.NODE_ENV === 'production';
const isSentryEnabled = process.env.ENABLE_SENTRY === 'true' || 
                       (process.env.NODE_ENV === 'development' && process.env.ENABLE_SENTRY_LOCAL === 'true');

if (isSentryEnabled && process.env.SENTRY_DSN) {
  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    tracesSampleRate: isProduction ? 0.01 : 1.0, // 1% in prod, 100% in dev
    environment: process.env.NODE_ENV,
  });
}
```

#### Update Client Configuration
- **File**: `src/instrumentation-client.ts`
- **Changes**: Mirror server configuration pattern

#### Update Edge Configuration
- **File**: `sentry.edge.config.ts`
- **Changes**: Mirror server configuration pattern

### 1.3 Environment Variables Setup (30 minutes)

#### Required Environment Variables
```env
# Sentry Configuration
SENTRY_DSN=https://<EMAIL>/4509639010680912
ENABLE_SENTRY=true
ENABLE_SENTRY_LOCAL=false  # Only set to true for local Sentry testing

# IP Allowlist Configuration (for Phase 2)
ENABLE_IP_ALLOWLIST=false
IP_ALLOWLIST_CIDRS=10.0.0.0/8,**********/12,***********/16,127.0.0.1/32
```

#### Documentation Update
- **File**: `docs/UPDATES/AUTH-SPRINT/ENVIRONMENT-VARIABLES.md`
- **Action**: Add PR3-specific environment variables with descriptions

## 📋 Phase 2: IP Allowlist Middleware (2 hours)

### 2.1 Core IP Allowlist Implementation (90 minutes)

#### Create IP Allowlist Utility
- **File**: `src/lib/security/ip-allowlist.ts`
- **Features**:
  - CIDR notation support (`10.0.0.0/8`, `**********/12`, `***********/16`)
  - **IPv4 and IPv6 support** (immediate implementation)
  - **Build-time CIDR validation** (fail-fast on invalid configuration)
  - **Self-lockout prevention** (mandatory office IP ranges)
  - Environment-based configuration
  - Logging and monitoring integration

```typescript
// Core interface (detailed in technical specifications)
export interface IPAllowlistConfig {
  enabled: boolean;
  allowedCIDRs: string[];
  logViolations: boolean;
  blockByDefault: boolean;
}

export function isIPAllowed(clientIP: string, config: IPAllowlistConfig): boolean;
export function createIPAllowlistMiddleware(config: IPAllowlistConfig): NextResponse | null;
```

#### Features Include:
- **CIDR Validation**: Support for standard private network ranges (IPv4 and IPv6)
- **IPv6 Support**: Immediate implementation for modern SaaS VPN compatibility
- **Build-time Validation**: Fail-fast CIDR validation to prevent deployment issues
- **Self-lockout Prevention**: Mandatory office IP ranges in default configuration
- **Logging**: Structured logging for security monitoring
- **Performance**: Optimized IP checking with pre-compiled CIDR masks
- **Error Handling**: Graceful degradation if configuration is invalid

### 2.2 Integration Points (30 minutes)

#### Middleware Integration Pattern
```typescript
// Example usage in API routes
import { createIPAllowlistMiddleware, getIPAllowlistConfig } from '@/lib/security/ip-allowlist';

export async function GET(request: NextRequest) {
  // Apply IP allowlist if enabled
  const allowlistResponse = createIPAllowlistMiddleware(getIPAllowlistConfig());
  if (allowlistResponse) {
    return allowlistResponse; // Returns 403 Forbidden
  }
  
  // Continue with normal API logic
  return NextResponse.json({ message: "Internal tool accessed" });
}
```

#### Future Integration Points
- Admin dashboard routes
- Internal debugging endpoints
- Partner configuration endpoints
- System health check endpoints

## 🔧 Technical Implementation Details

### File Structure Changes
```
src/
├── lib/
│   └── security/
│       ├── ip-allowlist.ts          (NEW - Core IP allowlist logic)
│       ├── hmac.ts                  (EXISTS - From PR2)
│       ├── jwt.ts                   (EXISTS - From PR1)
│       └── auth-middleware.ts       (EXISTS - From PR2)
├── app/
│   ├── api/
│   │   └── sentry-example-api/      (DELETE - Remove entire directory)
│   └── sentry-example-page/         (DELETE - Remove entire directory)
├── sentry.server.config.ts          (MODIFY - Environment-based config)
├── sentry.edge.config.ts            (MODIFY - Environment-based config)
└── instrumentation-client.ts        (MODIFY - Environment-based config)
```

### Configuration Changes
- **Environment Variables**: 5 new variables for Sentry and IP allowlist control
- **Feature Flags**: `ENABLE_SENTRY`, `ENABLE_SENTRY_LOCAL`, `ENABLE_IP_ALLOWLIST`
- **Next.js Config**: Conditional Sentry integration based on environment

### Security Considerations
- **IP Spoofing**: Check multiple headers (`X-Forwarded-For`, `X-Real-IP`, `CF-Connecting-IP`)
- **Default Deny**: Block by default if allowlist is enabled but misconfigured
- **Build-time Safety**: Fail deployment on invalid CIDR configuration
- **Self-lockout Prevention**: Mandatory office IP ranges in default configuration
- **Logging**: Security events for monitoring and incident response
- **Performance**: Minimal latency impact (< 1ms per request)

## ✅ Success Criteria

### Phase 1 Success Criteria
- [ ] **Debug Endpoints Removed**: `/api/sentry-example-api` returns 404 in production
- [ ] **Frontend Cleanup**: `/sentry-example-page` returns 404 in production
- [ ] **Sentry Functionality**: Error reporting still works normally in production
- [ ] **Environment Control**: Sentry disabled in local development unless flag set
- [ ] **Performance**: No degradation in application response times

### Phase 2 Success Criteria
- [ ] **IP Allowlist Function**: Correctly validates IPs against CIDR ranges
- [ ] **Middleware Integration**: Easy to apply to any API route
- [ ] **Configuration**: Environment-based configuration works correctly
- [ ] **Logging**: IP violations logged with appropriate detail
- [ ] **Feature Flag**: Can enable/disable without code changes

### Integration Success Criteria
- [ ] **Build Success**: Application builds without TypeScript errors
- [ ] **Test Coverage**: All new functionality covered by unit tests
- [ ] **Documentation**: Clear usage examples and configuration guide
- [ ] **Monitoring**: IP allowlist violations appear in logs
- [ ] **Performance**: IP validation adds < 1ms to request processing

## 🧪 Testing Strategy Integration

### Unit Testing (Covered in testing-strategy.md)
- IP validation logic with various CIDR ranges
- Environment configuration parsing
- Middleware response handling

### Integration Testing
- API routes with IP allowlist enabled/disabled
- Sentry error reporting in different environments
- Feature flag toggling behavior

### End-to-End Testing
- Access internal tools from allowed/blocked IPs
- Sentry error capture in production environment
- Configuration changes without deployment

## 🚀 Deployment Integration

### Deployment Order
1. **Deploy with feature flags disabled** (`ENABLE_IP_ALLOWLIST=false`)
2. **Verify Sentry cleanup**: Debug endpoints return 404
3. **Test IP allowlist**: Enable feature flag and validate blocking
4. **Monitor**: Check logs for any unexpected IP violations
5. **Enable gradually**: Start with loose CIDR ranges, tighten as needed

### Rollback Strategy
- **Quick Rollback**: Set `ENABLE_IP_ALLOWLIST=false` (< 1 minute)
- **Full Rollback**: Revert deployment to previous version
- **Monitoring**: Automated alerts for configuration issues

## 📊 Risk Mitigation

### Development Risks
- **Sentry Loss**: Mitigated by environment-based configuration and feature flags
- **IP Lockout**: Mitigated by feature flags and broad default CIDR ranges
- **Performance**: Mitigated by optimized IP checking algorithms

### Operational Risks
- **Configuration Errors**: Mitigated by validation and default safe configurations
- **Network Changes**: Mitigated by broad CIDR ranges and feature flag rollback
- **Monitoring Gaps**: Mitigated by comprehensive logging and alerting

## 🔄 Future Enhancements

### Post-PR3 Improvements
- **Redis Integration**: Cache IP allowlist for improved performance
- **Admin Interface**: Web UI for managing IP allowlists
- **Geolocation**: Country-based access controls
- **Rate Limiting Integration**: Combine with IP allowlist for advanced protection

### Long-term Strategy
- **Zero Trust**: Move toward identity-based access controls
- **API Gateway**: Centralized IP allowlist management
- **Monitoring**: Advanced threat detection and response
- **Compliance**: SOC2 and GDPR considerations for IP logging

---

**Implementation Ready**: This plan provides clear, step-by-step instructions for completing PR3 within the 4-hour time budget while maintaining security and reliability standards.
# PR3: Sentry Cleanup & IP Allowlist - Testing Strategy

**Date:** July 13, 2025  
**Sprint:** Auth-Sprint Phase 1  
**Testing Framework:** Jest + Playwright  
**Coverage Target:** 95%+ for security-critical code  

## 🎯 Testing Overview

Comprehensive testing strategy for IP allowlist middleware and Sentry cleanup, with **critical focus on preventing self-lockout scenarios** and ensuring production security.

## 🔒 Critical Security Tests

### 1. Self-Lockout Prevention Tests (Priority 1)

**Purpose:** Prevent accidentally blocking company/office IPs during deployment

```typescript
// src/__tests__/security/ip-allowlist-lockout.test.ts
describe('Self-Lockout Prevention', () => {
  it('should allow office IP ranges by default', () => {
    const config = getIPAllowlistConfig();
    
    // Test known office IPs (replace with actual office ranges)
    const officeIPs = [
      '**********',      // Office network
      '***********',     // VPN range
      '************',    // Local office
      '127.0.0.1'        // Localhost
    ];
    
    officeIPs.forEach(ip => {
      const result = validateIPAllowlist(ip, config);
      expect(result.allowed).toBe(true);
      expect(result.reason).toBe('allowed_by_rule');
    });
  });

  it('should fail build if no default CIDR ranges configured', () => {
    process.env.IP_ALLOWLIST_CIDRS = '';
    
    expect(() => {
      validateIPAllowlistConfig(getIPAllowlistConfig());
    }).toThrow('No CIDR ranges configured - risk of complete lockout');
  });
});
```

### 2. IPv4 and IPv6 Validation Tests

```typescript
describe('IP Address Validation', () => {
  describe('IPv4 Support', () => {
    it('should validate IPv4 addresses correctly', () => {
      const validIPs = ['***********', '********', '**********'];
      const invalidIPs = ['256.1.1.1', '192.168.1', '***********.1'];
      
      validIPs.forEach(ip => {
        expect(isValidIPv4(ip)).toBe(true);
      });
      
      invalidIPs.forEach(ip => {
        expect(isValidIPv4(ip)).toBe(false);
      });
    });
  });

  describe('IPv6 Support', () => {
    it('should validate IPv6 addresses correctly', () => {
      const validIPs = [
        '2001:db8::1',                    // Zero compression
        '::1',                           // Loopback
        '2001:db8:85a3::8a2e:370:7334', // Mixed compression
        '2001:db8:85a3:0:0:8a2e:370:7334' // Full form
      ];

      validIPs.forEach(ip => {
        expect(isValidIPv6(ip)).toBe(true);
      });
    });

    it('should validate IPv6 CIDR ranges correctly', () => {
      const validCIDRs = [
        '2001:db8::/32',
        '::1/128',
        '2001:db8:85a3::8a2e:370:7334/64'
      ];

      validCIDRs.forEach(cidr => {
        expect(isValidIPv6CIDR(cidr)).toBe(true);
      });
    });
  });
});
```

### 3. CIDR Range Validation Tests

```typescript
describe('CIDR Range Validation', () => {
  it('should parse and validate CIDR ranges correctly', () => {
    const validCIDRs = [
      '10.0.0.0/8',
      '**********/12', 
      '***********/16',
      '127.0.0.1/32',
      '2001:db8::/32'  // IPv6
    ];
    
    validCIDRs.forEach(cidr => {
      expect(() => parseCIDR(cidr)).not.toThrow();
    });
  });

  it('should fail build on invalid CIDR configuration', () => {
    const invalidCIDRs = [
      '256.0.0.0/8',    // Invalid IP
      '10.0.0.0/33',    // Invalid prefix
      '10.0.0.0',       // Missing prefix
      'invalid-cidr'    // Completely invalid
    ];
    
    invalidCIDRs.forEach(cidr => {
      expect(() => {
        validateCIDRRange(cidr);
      }).toThrow();
    });
  });
});
```

## 🧪 Unit Tests

### 1. IP Allowlist Core Functions

```typescript
// src/__tests__/security/ip-allowlist.test.ts
describe('IP Allowlist Core Functions', () => {
  describe('isIPInCIDR', () => {
    it('should correctly match IPs in CIDR ranges', () => {
      expect(isIPInCIDR('**********', '10.0.0.0/8')).toBe(true);
      expect(isIPInCIDR('***********', '**********/12')).toBe(true);
      expect(isIPInCIDR('***********', '10.0.0.0/8')).toBe(false);
    });
  });

  describe('getClientIP', () => {
    it('should extract IP from request headers in correct priority', () => {
      const request = new NextRequest('http://localhost/test', {
        headers: {
          'X-Forwarded-For': '***********, ********',
          'X-Real-IP': '***********',
          'CF-Connecting-IP': '**********'
        }
      });
      
      expect(getClientIP(request)).toBe('***********');
    });
  });
});
```

### 2. Configuration Validation Tests

```typescript
describe('Configuration Validation', () => {
  it('should validate environment configuration at startup', () => {
    const validConfig = {
      enabled: true,
      allowedCIDRs: ['10.0.0.0/8', '**********/12'],
      logViolations: true,
      blockByDefault: true
    };
    
    expect(validateIPAllowlistConfig(validConfig)).toEqual([]);
  });

  it('should detect configuration errors', () => {
    const invalidConfig = {
      enabled: true,
      allowedCIDRs: ['invalid-cidr'],
      logViolations: true,
      blockByDefault: true
    };
    
    const errors = validateIPAllowlistConfig(invalidConfig);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0]).toContain('Invalid CIDR');
  });
});
```

## 🔗 Integration Tests

### 1. API Route Integration

```typescript
// src/__tests__/integration/ip-allowlist-middleware.test.ts
describe('IP Allowlist Middleware Integration', () => {
  it('should protect API routes when enabled', async () => {
    process.env.ENABLE_IP_ALLOWLIST = 'true';
    
    const request = new NextRequest('http://localhost/api/admin/config', {
      headers: { 'X-Forwarded-For': '***********' } // External IP
    });
    
    const response = await applyIPAllowlist(request);
    expect(response?.status).toBe(403);
  });

  it('should allow access from whitelisted IPs', async () => {
    process.env.ENABLE_IP_ALLOWLIST = 'true';
    
    const request = new NextRequest('http://localhost/api/admin/config', {
      headers: { 'X-Forwarded-For': '**********' } // Office IP
    });
    
    const response = await applyIPAllowlist(request);
    expect(response).toBeNull(); // Allowed
  });
});
```

### 2. Feature Flag Integration

```typescript
describe('Feature Flag Integration', () => {
  it('should disable IP allowlist when feature flag is off', async () => {
    process.env.ENABLE_IP_ALLOWLIST = 'false';
    
    const request = new NextRequest('http://localhost/api/admin/config', {
      headers: { 'X-Forwarded-For': '***********' }
    });
    
    const response = await applyIPAllowlist(request);
    expect(response).toBeNull(); // Allowed when disabled
  });
});
```

## 🎭 End-to-End Tests (Playwright)

### 1. Sentry Cleanup Verification

```typescript
// tests/e2e/sentry-cleanup.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Sentry Cleanup', () => {
  test('debug endpoints should return 404 in production', async ({ page }) => {
    // Test API debug endpoint
    const apiResponse = await page.request.get('/api/sentry-example-api');
    expect(apiResponse.status()).toBe(404);
    
    // Test frontend debug page
    const pageResponse = await page.goto('/sentry-example-page');
    expect(pageResponse?.status()).toBe(404);
  });

  test('Sentry should still capture errors normally', async ({ page }) => {
    // Navigate to a page that might have errors
    await page.goto('/');
    
    // Verify Sentry is loaded (check for Sentry script)
    const sentryScript = await page.locator('script[src*="sentry"]').count();
    expect(sentryScript).toBeGreaterThan(0);
  });
});
```

### 2. IP Allowlist E2E Tests

```typescript
// tests/e2e/ip-allowlist.spec.ts
test.describe('IP Allowlist Protection', () => {
  test('should block access to admin routes from external IPs', async ({ request }) => {
    // Simulate external IP request
    const response = await request.get('/api/admin/config', {
      headers: { 'X-Forwarded-For': '***********' }
    });

    expect(response.status()).toBe(403);

    const body = await response.json();
    expect(body.error).toBe('IP_NOT_ALLOWED');
  });

  // 2025 Best Practice: IPv6 Happy Path Testing
  test('should allow IPv6 addresses from allowlist', async ({ request }) => {
    const response = await request.get('/api/admin/config', {
      headers: { 'X-Forwarded-For': '2001:db8::1' }
    });

    // Should pass if 2001:db8::/32 is in allowlist
    expect(response.status()).not.toBe(403);
  });

  // 2025 Best Practice: Cloudflare Header Chain Testing
  test('should handle Cloudflare header precedence correctly', async ({ request }) => {
    // Simulate Cloudflare proxy chain: X-Forwarded-For + CF-Connecting-IP
    const response = await request.get('/api/admin/config', {
      headers: {
        'X-Forwarded-For': '**********, **********', // Office IP first
        'CF-Connecting-IP': '***********' // External IP from Cloudflare
      }
    });

    // Should use X-Forwarded-For first IP (**********) and allow
    expect(response.status()).not.toBe(403);
  });

  test('should fall back to CF-Connecting-IP when X-Forwarded-For missing', async ({ request }) => {
    const response = await request.get('/api/admin/config', {
      headers: {
        'CF-Connecting-IP': '**********' // Office IP via Cloudflare
      }
    });

    expect(response.status()).not.toBe(403);
  });
});
```

## ⚡ Performance Tests

### 1. IP Validation Performance

```typescript
describe('Performance Tests', () => {
  it('should validate IPs within 2ms average', () => {
    const config = getIPAllowlistConfig();
    const testIP = '**********';

    const start = performance.now();
    for (let i = 0; i < 1000; i++) {
      validateIPAllowlist(testIP, config);
    }
    const end = performance.now();

    const avgTime = (end - start) / 1000;
    expect(avgTime).toBeLessThan(2); // < 2ms per validation (realistic target)
  });
});
```

## 📊 Test Coverage Requirements

### Coverage Targets
- **IP Allowlist Functions:** 100% line coverage
- **Configuration Validation:** 100% line coverage  
- **Middleware Integration:** 95% line coverage
- **Error Handling:** 100% branch coverage

### Test Commands

```bash
# Run all PR3 tests
npm test -- --testPathPattern="ip-allowlist|sentry"

# Run security tests only
npm run test:security

# Run with coverage
npm run test:coverage -- --testPathPattern="ip-allowlist"

# Run self-lockout prevention tests
npm test -- --testNamePattern="Self-Lockout Prevention"

# Run performance tests
npm test -- --testNamePattern="Performance"
```

## 🚨 Pre-Deployment Test Checklist

<!-- Add Tasks -->

### Critical Tests (Must Pass)
- [ ] **Self-lockout prevention tests pass**
- [ ] **Office IP ranges are whitelisted**
- [ ] **Invalid CIDR configuration fails build**
- [ ] **Feature flags work correctly**
- [ ] **Sentry debug endpoints return 404**
- [ ] **Sentry error reporting still works**

### Performance Tests
- [ ] **IP validation < 0.2ms P95 with ipaddr.js**
- [ ] **Memory usage stable under load**
- [ ] **No memory leaks in CIDR cache**

### Security Tests
- [ ] **External IPs blocked correctly**
- [ ] **IPv6 addresses handled properly**
- [ ] **Header spoofing attempts fail**
- [ ] **Configuration tampering detected**

### 2025 Best Practice Tests
- [ ] **IPv6 happy path testing (2001:db8::1)**
- [ ] **Cloudflare header chain precedence**
- [ ] **GDPR-compliant IP masking in logs**
- [ ] **Trust proxy depth configuration**

<!-- Add Tasks -->

---

**Testing Strategy Complete**: This comprehensive testing approach ensures IP allowlist security while preventing operational lockouts and maintaining system performance.

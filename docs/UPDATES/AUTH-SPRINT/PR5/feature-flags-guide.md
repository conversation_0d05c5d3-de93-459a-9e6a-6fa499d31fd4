# PR5: CORS Tightening + Automated Security Tests - Feature Flags Guide

**Date:** July 13, 2025  
**Version:** v14.9.0  
**Status:** Production Ready  

## 🚩 Feature Flag Overview

### **Primary Feature Flag**
**`ENABLE_CORS_STRICT`** - Master control for CORS enforcement across the platform

- **Type:** <PERSON><PERSON>an (true/false)
- **Default:** `false` (disabled for safe deployment)
- **Scope:** Global CORS policy enforcement
- **Rollback Time:** < 1 minute via environment variable change

### **Supporting Configuration Flags**
Additional environment variables that work in conjunction with the primary feature flag:

1. **`CORS_PROTECTED_ROUTES`** - Define which API routes require CORS protection
2. **`CORS_ALLOWED_AMPLIFY_DOMAINS`** - Specific domains to allow (replaces wildcards)

## 🔧 Configuration Details

### **Environment Variable Specifications**

#### **ENABLE_CORS_STRICT**
```bash
# Feature flag to enable/disable CORS enforcement
ENABLE_CORS_STRICT=false  # Safe default for initial deployment
```

**Values:**
- `true` - Full CORS enforcement active
- `false` - CORS headers applied but no blocking (monitoring mode)
- Unset/empty - Defaults to `false`

**Behavior when enabled:**
- Blocks unauthorized cross-origin requests with 403 responses
- Applies strict origin validation against allowlist
- Enforces preflight OPTIONS requirements
- Logs security violations for monitoring

**Behavior when disabled:**
- Applies CORS headers for browser compatibility
- No origin blocking (requests proceed normally)
- Security event logging continues (for monitoring)
- All functionality remains accessible

#### **CORS_PROTECTED_ROUTES**
```bash
# Comma-separated list of API routes to protect
CORS_PROTECTED_ROUTES=/api/catalog,/api/search/more,/api/products,/api/brands,/api/retailers
```

**Format:** Comma-separated route paths
**Default Routes:**
- `/api/catalog` - Comprehensive product catalog (AUTH REQUIRED)
- `/api/search/more` - Search pagination (AUTH REQUIRED)
- `/api/products` - Product endpoints (PUBLIC)
- `/api/brands` - Brand endpoints (PUBLIC)
- `/api/retailers` - Retailer endpoints (PUBLIC)

**Dynamic Route Support:**
```bash
# Supports dynamic routes with [id] parameters
/api/products/[id]  # Matches /api/products/123, /api/products/abc, etc.
/api/brands/[id]    # Matches /api/brands/samsung, /api/brands/apple, etc.
```

#### **CORS_ALLOWED_AMPLIFY_DOMAINS**
```bash
# Specific Amplify domains (replaces dangerous wildcards)
CORS_ALLOWED_AMPLIFY_DOMAINS=https://4-2.d3q274urye85k3.amplifyapp.com,https://main.d3q274urye85k3.amplifyapp.com
```

**Security Improvement:**
- **Before:** `*.amplifyapp.com` (ANY subdomain allowed - security risk)
- **After:** Specific domains only (controlled allowlist)

**Default Domains:**
- `https://4-2.d3q274urye85k3.amplifyapp.com` - Current production domain
- `https://main.d3q274urye85k3.amplifyapp.com` - Main branch preview
- `https://staging.d3q274urye85k3.amplifyapp.com` - Staging environment

## 📊 Deployment Strategies

### **Strategy 1: Phased Rollout (Recommended)**

#### **Phase 0: Monitoring Mode** 🟡
```bash
ENABLE_CORS_STRICT=false
CORS_PROTECTED_ROUTES=/api/catalog,/api/search/more,/api/products,/api/brands,/api/retailers
```

**Objective:** Deploy code with CORS disabled to validate infrastructure
**Duration:** 24 hours
**Validation:**
- All endpoints respond normally
- CORS headers present but no blocking
- Security logging active
- Feature flag toggle working

#### **Phase 1: Catalog Protection** 🟠
```bash
ENABLE_CORS_STRICT=true
CORS_PROTECTED_ROUTES=/api/catalog  # Only catalog initially
```

**Objective:** Enable CORS for most sensitive endpoint only
**Duration:** 24-48 hours
**Validation:**
- Catalog endpoint blocks unauthorized origins
- Search and public endpoints unaffected
- No legitimate user impact
- Monitoring shows expected violations

#### **Phase 2: Full Enforcement** 🔴
```bash
ENABLE_CORS_STRICT=true
CORS_PROTECTED_ROUTES=/api/catalog,/api/search/more,/api/products,/api/brands,/api/retailers
```

**Objective:** Complete CORS protection across all planned endpoints
**Duration:** Ongoing production
**Validation:**
- All protected routes enforce CORS
- Performance impact < 2ms
- Zero false positive blocks
- Complete security coverage

### **Strategy 2: Immediate Full Deployment** (Higher Risk)

```bash
# All-at-once deployment (not recommended for production)
ENABLE_CORS_STRICT=true
CORS_PROTECTED_ROUTES=/api/catalog,/api/search/more,/api/products,/api/brands,/api/retailers
CORS_ALLOWED_AMPLIFY_DOMAINS=https://4-2.d3q274urye85k3.amplifyapp.com,https://main.d3q274urye85k3.amplifyapp.com
```

**Use Cases:**
- Development/staging environments
- Emergency security response
- Well-tested integration scenarios

## 🚨 Emergency Procedures

### **Level 1: Instant Disable** (< 1 minute)
```bash
# AWS Amplify Console → Environment Variables
ENABLE_CORS_STRICT=false
```

**When to use:**
- Legitimate users being blocked
- Unexpected integration failures
- Emergency access required

**Impact:**
- CORS enforcement immediately disabled
- All requests proceed normally
- Headers still applied for compatibility
- Security logging continues

### **Level 2: Selective Route Disable** (< 5 minutes)
```bash
# Remove problematic routes from protection
CORS_PROTECTED_ROUTES=/api/catalog  # Remove other routes
```

**When to use:**
- Specific endpoint causing issues
- Partner integration problems
- Gradual re-enablement needed

**Impact:**
- Removes specific routes from CORS enforcement
- Other routes remain protected
- Targeted problem resolution

### **Level 3: Domain Expansion** (< 2 minutes)
```bash
# Temporarily expand allowed domains
CORS_ALLOWED_AMPLIFY_DOMAINS=https://4-2.d3q274urye85k3.amplifyapp.com,https://main.d3q274urye85k3.amplifyapp.com,https://emergency-domain.amplifyapp.com
```

**When to use:**
- New preview deployment needs access
- Authorized partner domain not in allowlist
- Temporary access requirements

## 🔄 Feature Flag Management

### **AWS Amplify Console Configuration**

#### **Step 1: Access Environment Variables**
1. Navigate to AWS Amplify Console
2. Select your app (cashback-deals-v2)
3. Go to App Settings → Environment Variables
4. Click "Manage Variables"

#### **Step 2: Set Feature Flags**
```
Variable Name: ENABLE_CORS_STRICT
Value: false
Environment: All Environments
```

```
Variable Name: CORS_PROTECTED_ROUTES  
Value: /api/catalog,/api/search/more,/api/products,/api/brands,/api/retailers
Environment: All Environments
```

```
Variable Name: CORS_ALLOWED_AMPLIFY_DOMAINS
Value: https://4-2.d3q274urye85k3.amplifyapp.com,https://main.d3q274urye85k3.amplifyapp.com
Environment: All Environments
```

#### **Step 3: Trigger Deployment**
- Changes to environment variables automatically trigger app rebuild
- Deployment takes ~2-3 minutes
- Feature flags active immediately after deployment

### **Environment-Specific Configuration**

#### **Development Environment**
```bash
# .env.local
ENABLE_CORS_STRICT=false
CORS_PROTECTED_ROUTES=/api/catalog,/api/search/more
CORS_ALLOWED_AMPLIFY_DOMAINS=http://localhost:3000
NODE_ENV=development
```

**Behavior:**
- CORS enforcement disabled by default
- Localhost origins automatically allowed
- Full development functionality maintained

#### **Staging Environment**
```bash
# Staging environment variables
ENABLE_CORS_STRICT=true
CORS_PROTECTED_ROUTES=/api/catalog,/api/search/more,/api/products,/api/brands,/api/retailers
CORS_ALLOWED_AMPLIFY_DOMAINS=https://staging.d3q274urye85k3.amplifyapp.com,https://main.d3q274urye85k3.amplifyapp.com
```

**Behavior:**
- Full CORS enforcement for production testing
- Staging-specific domains allowed
- Production-equivalent security posture

#### **Production Environment**
```bash
# Production environment variables
ENABLE_CORS_STRICT=true  # Enable after successful rollout
CORS_PROTECTED_ROUTES=/api/catalog,/api/search/more,/api/products,/api/brands,/api/retailers
CORS_ALLOWED_AMPLIFY_DOMAINS=https://4-2.d3q274urye85k3.amplifyapp.com,https://main.d3q274urye85k3.amplifyapp.com
```

## 📊 Monitoring & Verification

### **Feature Flag Status Validation**

#### **Health Check Endpoint**
```bash
# Check current CORS configuration
curl https://your-app.amplifyapp.com/api/health/cors

# Expected response:
{
  "corsEnabled": true,
  "protectedRoutes": ["/api/catalog", "/api/search/more", "/api/products", "/api/brands", "/api/retailers"],
  "allowedDomains": ["https://cashback-deals.com", "https://4-2.d3q274urye85k3.amplifyapp.com"],
  "timestamp": "2025-07-13T23:00:00.000Z"
}
```

#### **Live Testing Commands**
```bash
# Test CORS enforcement is working
curl -H "Origin: https://malicious-site.com" \
     https://your-app.amplifyapp.com/api/catalog
# Expected: 403 if CORS enabled, 200/401 if disabled

# Test allowed origin works
curl -H "Origin: https://cashback-deals.com" \
     https://your-app.amplifyapp.com/api/catalog  
# Expected: 200/401 (depends on auth, but not CORS blocked)

# Test server-to-server access
curl https://your-app.amplifyapp.com/api/catalog
# Expected: Always works (no origin = server-to-server)
```

### **CloudWatch Log Monitoring**
```bash
# Monitor CORS violations
aws logs filter-log-events --log-group-name /aws/amplify/your-app \
  --filter-pattern "CORS_VIOLATION" \
  --start-time $(date -d '1 hour ago' +%s)000

# Monitor feature flag changes
aws logs filter-log-events --log-group-name /aws/amplify/your-app \
  --filter-pattern "CORS_CONFIG_CHANGE" \
  --start-time $(date -d '1 hour ago' +%s)000
```

## 🛠️ Troubleshooting Guide

### **Common Issues & Solutions**

#### **Issue 1: Feature Flag Not Taking Effect**
**Symptoms:** CORS behavior unchanged after environment variable update
**Diagnosis:**
```bash
# Check if app rebuild triggered
aws amplify list-jobs --app-id your-app-id --branch-name main

# Verify environment variables set
aws amplify get-app --app-id your-app-id
```
**Solution:** Trigger manual rebuild if automatic deployment didn't occur

#### **Issue 2: Legitimate Users Being Blocked**
**Symptoms:** 403 CORS errors for authorized users
**Diagnosis:**
```bash
# Check exact origin being sent
curl -v -H "Origin: https://suspect-domain.com" https://your-app.amplifyapp.com/api/catalog
```
**Solutions:**
1. Add missing domain to `CORS_ALLOWED_AMPLIFY_DOMAINS`
2. Temporarily disable with `ENABLE_CORS_STRICT=false`
3. Remove problematic route from `CORS_PROTECTED_ROUTES`

#### **Issue 3: Server Tools Being Blocked**
**Symptoms:** curl, Postman, etc. getting 403 errors
**Diagnosis:** Check if User-Agent header is being detected properly
**Solution:** Verify server-to-server detection logic is working

#### **Issue 4: OPTIONS Preflight Failures**
**Symptoms:** Browser CORS errors on actual requests after preflight
**Diagnosis:** Verify OPTIONS handlers are returning correct headers
**Solution:** Ensure all protected routes have OPTIONS method handlers

## 🔒 Security Considerations

### **Feature Flag Security**
- Environment variables stored securely in AWS Amplify
- No sensitive data in feature flag values
- Changes logged for audit trail
- Emergency disable capability always available

### **Gradual Rollout Benefits**
- Minimizes blast radius of potential issues
- Allows real-world validation before full deployment
- Provides opportunity to adjust configuration based on monitoring data
- Enables confident production deployment

### **Monitoring Requirements**
- All feature flag changes logged with timestamps
- CORS violations tracked for security analytics
- Performance impact measured continuously
- Rollback procedures tested and documented

This feature flag system provides comprehensive control over CORS deployment while maintaining security and operational safety throughout the rollout process.
DISCLAIMER: All code snippets are boilerplate guidance only. Review, adapt, and security-harden before using in production.

# PR-5 — CORS Tightening + Automated Security & Flood-Protection Tests

## 🎯 Objective  
Lock down cross-origin access on four API routes and prove it with an automated Jest + Supertest suite that also covers auth failures and flood-control.

ensure use of global logging and functions to keep our code clean and maintainable.

---

## 1  Implementation Tasks

| Task | Detail |
|------|--------|
| **CORS allow-list** | Use `@fastify/cors` (or Express `cors`) **per-route** with a strict origin list:<br>`https://www.example.com`, `https://staging.example.com`.<br>Options:<br>```ts\norigin: (origin, cb) => {\n  if (!origin || ALLOWED_ORIGINS.includes(origin)) return cb(null, true);\n  cb(new Error('Not allowed by CORS'), false);\n},\nmethods: ['GET','POST'],\nallowedHeaders: ['Content-Type','Authorization','X-Partner-Key'],\ncredentials: true,\nstrictPreflight: true, // fastify only\n``` |
| **Protected routes** | Apply the CORS middleware to:<br>• `/api/catalog`  • `/api/search/more`  • `/api/products/:id`  • `/api/brands/:id` |
| **Rate-limiter** | Register `fastify-rate-limit` (or `express-rate-limit`) on `/api/catalog`:<br>`max: 10 reqs / 1 sec` → reply `429` + `Retry-After` |
| **Auth checks** | Ensure existing JWT (PR-1) & HMAC (PR-2) middleware still fire **after** CORS / rate-limit. |
| **Config flags** | `ENABLE_CORS_STRICT=true`, `ENABLE_RATE_LIMIT=true` for easy rollback. |

---

## 2  Automated Test Suite (`__tests__/cors-and-flood.spec.ts`)

> **Boilerplate only – adapt to your app factory & route names.**

```ts
/**
 * Tests for PR-5 — DO NOT copy-paste blindly; wire to your server builder.
 */
import request from 'supertest';
import { buildApp } from '../../src/app';

let app;
beforeAll(async () => {
  app = await buildApp();
  await app.ready?.();
});
afterAll(async () => app.close?.());

const OK_ORIGIN = 'https://www.example.com';
const BAD_ORIGIN = 'https://evil.com';
const jwt = 'Bearer VALID_JWT_TOKEN';

const routes = [
  '/api/catalog',
  '/api/search/more?brand=samsung-uk&page=2',
  '/api/products/123',
  '/api/brands/abc',
] as const;

/* 403 on disallowed origin */
test.each(routes)('CORS 403 if bad origin — %s', async (r) => {
  const res = await request(app.server || app).get(r).set('Origin', BAD_ORIGIN);
  expect(res.status).toBe(403);
  expect(res.headers['access-control-allow-origin']).toBeUndefined();
});

/* 401/403 when auth missing (good origin) */
test.each(routes)('401 when no JWT/HMAC — %s', async (r) => {
  const res = await request(app.server || app).get(r).set('Origin', OK_ORIGIN);
  expect([401, 403]).toContain(res.status);
});

/* 200 when JWT present */
test.each(routes)('200 when JWT + good origin — %s', async (r) => {
  const res = await request(app.server || app)
    .get(r)
    .set('Origin', OK_ORIGIN)
    .set('Authorization', jwt);
  expect(res.status).toBe(200);
});

/* 429 flood check */
test('429 after >10 req/s on /api/catalog', async () => {
  const agent = request.agent(app.server || app).set('Origin', OK_ORIGIN);
  for (let i = 0; i < 11; i++) await agent.get('/api/catalog');
  const res = await agent.get('/api/catalog');
  expect(res.status).toBe(429);
  expect(res.headers['retry-after']).toBeDefined();
});

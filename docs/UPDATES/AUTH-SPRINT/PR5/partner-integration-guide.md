# PR5: CORS Tightening + Automated Security Tests - Partner Integration Guide

**Date:** July 13, 2025  
**Version:** v14.9.0  
**Audience:** Business Development, Technical Partnerships, External Developers  

## 🤝 Partnership Integration Overview

### **New Security Architecture Impact**
With the implementation of strict CORS controls in PR5, partner integrations now operate within a secure, enterprise-grade access control framework. This enhances security while maintaining flexibility for authorized integrations.

### **Key Changes for Partners**
1. **Origin-based Access Control** - Partner domains must be explicitly authorized
2. **Authentication Requirements** - Enhanced JWT/HMAC validation on sensitive endpoints
3. **Rate Limiting** - Tiered access limits based on endpoint sensitivity
4. **Monitoring & Analytics** - Comprehensive access tracking and security event logging

## 🔐 Access Control Framework

### **Partner Domain Authorization**

#### **Current Authorized Domains**
```
Production Domains:
- https://cashback-deals.com (primary)
- https://www.cashback-deals.com (www subdomain)

Amplify Preview Domains:
- https://4-2.d3q274urye85k3.amplifyapp.com (production deployment)
- https://main.d3q274urye85k3.amplifyapp.com (main branch)
```

#### **Adding New Partner Domains**
**For Business Development Team:**
1. Partner domain must be provided in exact format: `https://partner-domain.com`
2. Submit domain authorization request through internal security review process
3. Domain will be added to `CORS_ALLOWED_AMPLIFY_DOMAINS` environment variable
4. Changes require deployment (2-3 minute update cycle)

**Security Requirements for Partner Domains:**
- Must use HTTPS (HTTP domains not permitted in production)
- Must be legitimate business domains (no temporary or suspicious domains)
- Must pass basic security validation (SSL certificate, domain ownership)

### **API Endpoint Classification**

#### **Tier 1: High-Security Endpoints** 🔴
**Authentication Required + CORS Protected**
- `/api/catalog` - Complete product catalog access
- `/api/search/more` - Search pagination and advanced filtering

**Access Requirements:**
- Valid JWT or HMAC authentication
- Authorized origin domain
- Enhanced rate limiting (10 requests/second)
- Comprehensive access logging

**Partner Integration:**
- Requires formal API partnership agreement
- Authentication credentials provided via secure channel
- Rate limit adjustments available for enterprise partners

#### **Tier 2: Protected Public Endpoints** 🟡  
**Public Access + CORS Protected**
- `/api/products/[id]` - Individual product details
- `/api/brands/[id]` - Brand information and metadata  
- `/api/retailers/[id]` - Retailer profiles and offers

**Access Requirements:**
- Authorized origin domain (no authentication required)
- Standard rate limiting (100 requests/minute)
- Basic access logging

**Partner Integration:**
- Suitable for widget integrations and public displays
- Domain authorization required but no API keys needed
- Ideal for white-label implementations

#### **Tier 3: Server-to-Server Access** 🟢
**Tool/API Access**
- All endpoints accessible via curl, Postman, server libraries
- No origin header required (detected automatically)
- Authentication still required for protected endpoints

**Access Requirements:**
- Server/tool User-Agent (automatically detected)
- Valid authentication for protected endpoints
- No domain pre-authorization needed

**Partner Integration:**
- Perfect for backend integrations and data synchronization
- API testing and development workflows
- Automated systems and batch processing

## 🛠️ Integration Patterns

### **Pattern 1: White-Label Widget Integration**

**Use Case:** Partner wants to embed cashback deals widget on their website

**Implementation:**
```javascript
// Partner website: https://partner-site.com
// (Domain must be added to CORS_ALLOWED_AMPLIFY_DOMAINS)

// Frontend JavaScript integration
const fetchDeals = async () => {
  try {
    // Public endpoint - no authentication required
    const response = await fetch('https://cashback-deals.com/api/products/electronics', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'omit' // No cookies needed for public endpoints
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const deals = await response.json()
    return deals
  } catch (error) {
    console.error('Failed to fetch deals:', error)
    // Fallback UI or error handling
  }
}

// Widget rendering
const renderDealsWidget = (deals) => {
  const container = document.getElementById('deals-widget')
  container.innerHTML = deals.map(deal => `
    <div class="deal-card">
      <h3>${deal.title}</h3>
      <p>${deal.cashback_rate}% cashback</p>
      <a href="${deal.affiliate_url}" target="_blank">Shop Now</a>
    </div>
  `).join('')
}
```

**Security Considerations:**
- Partner domain must be HTTPS
- No sensitive data exposed in public endpoints
- Rate limiting prevents abuse
- Origin validation ensures authorized access only

### **Pattern 2: Backend API Integration**

**Use Case:** Partner wants to sync product catalog with their backend system

**Implementation:**
```python
# Partner backend system
import requests
import json
from datetime import datetime

class CashbackDealsAPI:
    def __init__(self, api_key, partner_secret):
        self.base_url = 'https://cashback-deals.com'
        self.api_key = api_key
        self.partner_secret = partner_secret
        
    def get_catalog(self):
        """Fetch complete product catalog with authentication"""
        url = f'{self.base_url}/api/catalog'
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'X-Partner-ID': 'partner-identifier',
            'X-Timestamp': str(int(datetime.now().timestamp())),
            'User-Agent': 'PartnerAPI/1.0'  # Server-to-server detected
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f'API request failed: {e}')
            return None
    
    def search_products(self, query, page=1):
        """Search products with pagination"""
        url = f'{self.base_url}/api/search/more'
        
        params = {
            'q': query,
            'page': page,
            'limit': 50
        }
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'User-Agent': 'PartnerAPI/1.0'
        }
        
        response = requests.get(url, params=params, headers=headers)
        return response.json() if response.status_code == 200 else None

# Usage
api = CashbackDealsAPI('partner-jwt-token', 'partner-secret')
catalog = api.get_catalog()
search_results = api.search_products('electronics', page=1)
```

**Security Benefits:**
- Server-to-server automatically detected (no CORS restrictions)
- Full authentication and authorization
- Comprehensive access logging
- Rate limiting protection

### **Pattern 3: Mobile App Integration**

**Use Case:** Partner mobile app wants to display cashback offers

**Implementation:**
```javascript
// React Native / mobile app integration
class CashbackDealsService {
  constructor(apiKey) {
    this.baseURL = 'https://cashback-deals.com'
    this.apiKey = apiKey
  }

  async fetchBrandOffers(brandId) {
    try {
      // Public endpoint - works from mobile apps
      const response = await fetch(`${this.baseURL}/api/brands/${brandId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PartnerMobileApp/1.0'
        }
      })

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to fetch brand offers:', error)
      return null
    }
  }

  async fetchRetailerDeals(retailerId) {
    const response = await fetch(`${this.baseURL}/api/retailers/${retailerId}`)
    return response.ok ? await response.json() : null
  }
}

// Usage in React Native component
const [deals, setDeals] = useState([])
const dealsService = new CashbackDealsService('mobile-app-key')

useEffect(() => {
  const loadDeals = async () => {
    const brandDeals = await dealsService.fetchBrandOffers('samsung')
    setDeals(brandDeals?.offers || [])
  }
  
  loadDeals()
}, [])
```

**Mobile-Specific Considerations:**
- Mobile apps detected as server-to-server (no origin header)
- No CORS restrictions for native mobile apps
- Standard rate limiting applies
- Public endpoints work without authentication

## 📊 Rate Limiting & Performance

### **Tier-Based Rate Limits**

#### **Tier 1: Catalog/Search Endpoints**
- **Limit:** 10 requests/second per IP/partner
- **Burst:** 50 requests (5-second window)
- **Justification:** High-value data requires protection
- **Upgrade Path:** Enterprise partners can request higher limits

#### **Tier 2: Public Product Endpoints**  
- **Limit:** 100 requests/minute per IP/partner
- **Burst:** 200 requests (2-minute window)
- **Justification:** Balances accessibility with abuse prevention
- **Upgrade Path:** Volume partners can request custom limits

#### **Rate Limit Headers**
All API responses include rate limiting information:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 87
X-RateLimit-Reset: 1625097600
X-RateLimit-Window: 60
```

### **Performance Expectations**

#### **Response Time SLAs**
- **Catalog Endpoint:** < 500ms (95th percentile)
- **Search Endpoint:** < 300ms (95th percentile)  
- **Product Endpoints:** < 200ms (95th percentile)
- **CORS Overhead:** < 2ms additional latency

#### **Availability Targets**
- **Uptime:** 99.9% monthly availability
- **Error Rate:** < 0.1% for authenticated requests
- **Maintenance Windows:** Announced 24 hours in advance

## 🚨 Error Handling & Troubleshooting

### **Common CORS Errors & Solutions**

#### **Error 1: CORS Origin Not Allowed**
```json
{
  "error": "Forbidden",
  "message": "Cross-Origin Request Blocked: The same-origin policy disallows reading from the remote resource.",
  "code": "CORS_BLOCKED",
  "timestamp": "2025-07-13T23:00:00.000Z"
}
```

**Cause:** Partner domain not in authorized list
**Solution:** Contact technical partnerships team to add domain to allowlist
**Timeline:** 24-48 hours for domain authorization

#### **Error 2: Authentication Required**
```json
{
  "error": "Unauthorized", 
  "message": "Authentication required for this endpoint",
  "code": "AUTH_REQUIRED",
  "traceId": "abc123def456"
}
```

**Cause:** Missing or invalid authentication for protected endpoint
**Solution:** Ensure valid JWT token in Authorization header
**Endpoint:** Contact partnerships team for API credentials

#### **Error 3: Rate Limit Exceeded**
```json
{
  "error": "Too Many Requests",
  "message": "Rate limit exceeded. Please try again later.",
  "code": "RATE_LIMITED",
  "retryAfter": 60
}
```

**Cause:** Too many requests in time window
**Solution:** Implement exponential backoff and respect rate limit headers
**Upgrade:** Contact team for higher rate limits if justified

### **Troubleshooting Checklist**

#### **For Frontend Integrations**
1. ✅ Confirm domain uses HTTPS
2. ✅ Verify domain is in authorized list
3. ✅ Check browser console for CORS errors
4. ✅ Ensure no custom headers that require preflight
5. ✅ Test OPTIONS preflight requests manually

#### **For Backend Integrations**
1. ✅ Include proper User-Agent header
2. ✅ Validate authentication credentials
3. ✅ Check rate limiting headers
4. ✅ Implement proper error handling
5. ✅ Test with curl/Postman first

#### **For Mobile Apps**
1. ✅ Verify User-Agent identifies as mobile app
2. ✅ Test public endpoints before protected ones
3. ✅ Implement proper retry logic
4. ✅ Handle network connectivity issues
5. ✅ Cache responses appropriately

## 🔄 Partner Onboarding Process

### **Phase 1: Requirements Gathering** (1-2 days)
1. **Integration Type:** Frontend widget, backend API, mobile app
2. **Domain Information:** All domains requiring access
3. **Usage Estimates:** Expected request volumes and patterns
4. **Data Requirements:** Which endpoints and data types needed
5. **Security Review:** Partner security posture assessment

### **Phase 2: Technical Setup** (2-3 days)
1. **Domain Authorization:** Add domains to CORS allowlist
2. **Credential Provisioning:** Generate API keys for protected endpoints
3. **Rate Limit Configuration:** Set appropriate limits based on usage
4. **Testing Environment:** Provide staging access for integration testing
5. **Documentation:** Custom integration guide and examples

### **Phase 3: Integration Testing** (3-5 days)
1. **Functional Testing:** Verify all required endpoints work correctly
2. **Security Testing:** Confirm CORS and authentication functioning
3. **Performance Testing:** Validate response times and rate limits
4. **Error Handling:** Test various failure scenarios
5. **Monitoring Setup:** Configure alerts and access tracking

### **Phase 4: Production Launch** (1 day)
1. **Production Credentials:** Migrate to production API keys
2. **Monitoring Activation:** Enable production access tracking
3. **Launch Verification:** Confirm live integration functioning
4. **Documentation Delivery:** Final integration documentation
5. **Support Channel:** Establish ongoing technical support

## 📞 Support & Contact Information

### **Technical Integration Support**
- **Email:** <EMAIL>
- **Response Time:** 24 hours for technical issues
- **Escalation:** 4 hours for production-impacting problems
- **Documentation:** Complete integration examples and troubleshooting guides

### **Business Development**
- **Email:** <EMAIL>  
- **Purpose:** New partnership discussions and commercial terms
- **Process:** Initial screening → technical feasibility → pilot program

### **Emergency Contact**
- **Email:** <EMAIL>
- **Purpose:** Production outages or security incidents affecting partners
- **Response:** 1 hour acknowledgment, 4 hours initial resolution

This partner integration guide ensures secure, reliable, and scalable access to the Cashback Deals platform while maintaining the highest security standards for all stakeholders.
This initiative is to address a critical security gap identified during our pre-launch audit. We must implement, verify, and enforce server-side validation and sanitization for all user-supplied input. This is a mandatory prerequisite for the MVP beta release to protect our application and user data from common web vulnerabilities like SQL injection and XSS.

Your assigment is to validate the below factors are covered and they are covered, docunent what has been implemented and refer to our actual codebase to show tracability. If an item on our checklist is not covered, then make a plan on how to cover it, but do not implement the solution. If you do not know an answer, then make a plan on how to find out and give guidance to the CTO on what we need to be doing to implent it. 

Once your audit as been created create a list of jira user stories and tasks that need to be completed/implemented or put into action to cover the gap. Ordered the jira user stories and tasks with the highest  priority first.

Below is the checklist. You can use any of the MCPs already installed to support any tasks on the Chromium web browser.

MCPs:

MCP Servers Available:
  - TaskMaster AI: Task management and project planning (needs setup)
  - Brave Search: Web search capabilities
  - Context7: Library documentation and code examples
  - Supabase: Database operations and management
  - Browser Tools: Web automation (requires Chrome extension, use Chromium)
  - Sentry: Error monitoring and debugging
  - Playwright: Browser automation and testing
  - Sequential Thinking: Structured reasoning and problem-solving

Comprehensive Web Application & Infrastructure Security Audit
Client: [Client Name]
Project: [Project Name]
Date: [Date of Audit]

I. Foundational Security, Policy & Architecture
Security Governance:

[ ] Is there a formal, written security policy or a set of secure coding guidelines that all developers follow?

[ ] Who has ownership of security within the organization, and is there a dedicated budget or resource allocation for security initiatives?

Architecture & Design:

[ ] Was a threat modeling exercise conducted during the application's design phase?

[ ] Do you have a single, centralized module or library for handling critical security functions like input validation and output encoding?

[ ] Can you walk us through the application's architecture, highlighting trust boundaries between different services?

II. Input & Data Validation
Server-Side Validation:

[ ] For all user-submitted input (from search, forms, API calls), what is the server-side process for validation and sanitization?

[ ] How are you protected against common injection attacks, including SQL, NoSQL, OS Command, and Log injection? Can you show a code example?

[ ] How does the application defend against Cross-Site Scripting (XSS), both reflected and stored?

File Uploads & Business Logic:

[ ] If the application allows file uploads, what security controls are in place (e.g., strict file type/size validation, malware scanning, serving files from a separate domain)?

[ ] How does the application's business logic prevent manipulation (e.g., submitting a negative quantity, exploiting a race condition)?

Spam & Bot Prevention:

[ ] What measures are in place to prevent automated spam submissions on public forms (e.g., CAPTCHA, reCAPTCHA, honeypots)?

[ ] Are there controls to prevent content scraping or other forms of automated abuse?

III. API & Routing Security
Endpoint Hardening:

[ ] For dynamic routes using identifiers (e.g., /products/{productId}), what validation is performed to prevent enumeration or IDOR-style attacks?

[ ] Are Cross-Site Request Forgery (CSRF) protections in place for any endpoints that perform actions?

Traffic & Access Management:

[ ] What is the Cross-Origin Resource Sharing (CORS) policy? Is it configured with a strict whitelist of allowed origins?

[ ] Is there granular API rate limiting and throttling to prevent abuse, based on IP address or other identifiers?

[ ] Are all API responses sanitized to prevent leaking internal system information (e.g., internal IP addresses, verbose error messages, software versions)?

IV. Data Security & Privacy
Data Encryption:

[ ] Is all data encrypted in transit using up-to-date TLS configurations (e.g., TLS 1.2/1.3)?

[ ] Is sensitive data encrypted at rest in the database and in backups? What encryption standards are used?

Data Governance:

[ ] How is data classified (e.g., public, confidential)?

[ ] What are the data retention and disposal policies? Is there an automated process for deleting data that is no longer needed?

[ ] How does the application ensure compliance with relevant data privacy regulations (e.g., GDPR, CCPA), particularly concerning data collected from public forms?

V. Application Access Control
Endpoint Protection:

[ ] Are there administrative, diagnostic, or internal-only API endpoints? How are these protected from public access (e.g., IP whitelisting, bearer tokens, placement on a private network)?

Content Segregation:

[ ] If the application has 'draft' or 'unpublished' content, what mechanisms prevent the public from accessing it via direct URL guessing or API manipulation?

VI. Secure Development Lifecycle (DevSecOps)
Code & Dependency Security:

[ ] What is the code review process? Is a security-focused review a mandatory step?

[ ] Do you use Static Application Security Testing (SAST) tools to scan source code for vulnerabilities?

[ ] What is your process for managing third-party dependencies (Software Composition Analysis - SCA)? Do you have a program to automatically scan for and remediate known vulnerabilities (e.g., via Snyk, Dependabot, npm audit)?

Secrets Management:

[ ] How are secrets (API keys, credentials) managed? Are they stored securely in a dedicated service (e.g., AWS Secrets Manager, HashiCorp Vault)?

[ ] How are secrets injected into the application at runtime, and how do you prevent them from being exposed in logs or source code?

CI/CD Pipeline Security:

[ ] How is the CI/CD pipeline itself secured against unauthorized access?

[ ] Do you scan infrastructure-as-code (IaC) templates for security misconfigurations?

[ ] If you use containers, are container images scanned for vulnerabilities before being deployed?

VII. Infrastructure & Network Security
Network Configuration:

[ ] Is the application protected by a Web Application Firewall (WAF)? What kind of rules are configured?

[ ] What is your strategy for mitigating Distributed Denial-of-Service (DDoS) attacks?

[ ] Is the network segmented (e.g., using VPCs and subnets) to isolate critical components from public-facing ones?

Server & Container Hardening:

[ ] What is the process for hardening the underlying servers or base container images (e.g., disabling unnecessary services, applying security patches)?

[ ] How is operating system and application patching managed to ensure timely updates?

VIII. Logging, Monitoring & Incident Response
Logging & Monitoring:

[ ] What security-relevant events are logged, and where are these logs aggregated (e.g., a SIEM solution)?

[ ] Are alerts configured to trigger on suspicious events (e.g., high rates of failed requests, potential injection attacks detected by a WAF)?

[ ] Is all logging sanitized to prevent log injection attacks?

Incident Response & Resilience:

[ ] Is there a formal Incident Response (IR) plan in case of a security breach? Who are the key contacts?

[ ] What is the application's backup and restore procedure? How often are backups taken, and how frequently is the restore process tested?

[ ] Is there a Disaster Recovery (DR) plan to ensure business continuity in the event of a major outage?
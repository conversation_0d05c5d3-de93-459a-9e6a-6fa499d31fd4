# Universal Filter Utility - Testing Strategy

## Testing Overview

This document outlines the comprehensive testing strategy for the Universal Filter Utility, ensuring reliability, performance, and user experience across all supported devices and scenarios.

## Testing Pyramid

```
    /\
   /  \    E2E Tests (10%)
  /____\   - Critical user journeys
 /      \  - Cross-browser compatibility
/__________\
Integration Tests (30%)
- API integration
- Component integration
- Filter state management

Unit Tests (60%)
- Individual components
- Hooks and utilities
- Data transformations
```

## 1. Unit Testing Strategy

### 1.1 Component Testing

**Test Files Location**: `/src/__tests__/components/filters/`

#### PriceRangeFilter.test.tsx
```typescript
describe('PriceRangeFilter', () => {
  const defaultProps = {
    min: 0,
    max: 1000,
    step: 10,
    currency: 'GBP' as const,
    onChange: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render with correct initial values', () => {
      render(<PriceRangeFilter {...defaultProps} />);
      
      expect(screen.getByDisplayValue('£0')).toBeInTheDocument();
      expect(screen.getByDisplayValue('£1000')).toBeInTheDocument();
    });

    it('should display currency symbol correctly', () => {
      render(<PriceRangeFilter {...defaultProps} currency="USD" />);
      
      expect(screen.getByText('$')).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    it('should call onChange when slider values change', () => {
      render(<PriceRangeFilter {...defaultProps} />);
      
      const minSlider = screen.getByRole('slider', { name: /minimum price/i });
      fireEvent.change(minSlider, { target: { value: '50' } });
      
      expect(defaultProps.onChange).toHaveBeenCalledWith({ min: 50, max: 1000 });
    });

    it('should validate input values', () => {
      render(<PriceRangeFilter {...defaultProps} />);
      
      const minInput = screen.getByLabelText(/minimum price/i);
      fireEvent.change(minInput, { target: { value: '-10' } });
      
      expect(screen.getByText(/minimum price cannot be negative/i)).toBeInTheDocument();
    });

    it('should prevent min > max scenarios', () => {
      render(<PriceRangeFilter {...defaultProps} />);
      
      const minInput = screen.getByLabelText(/minimum price/i);
      fireEvent.change(minInput, { target: { value: '1500' } });
      
      expect(screen.getByText(/minimum cannot exceed maximum/i)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<PriceRangeFilter {...defaultProps} />);
      
      expect(screen.getByRole('slider', { name: /minimum price/i })).toBeInTheDocument();
      expect(screen.getByRole('slider', { name: /maximum price/i })).toBeInTheDocument();
    });

    it('should support keyboard navigation', () => {
      render(<PriceRangeFilter {...defaultProps} />);
      
      const minSlider = screen.getByRole('slider', { name: /minimum price/i });
      minSlider.focus();
      fireEvent.keyDown(minSlider, { key: 'ArrowRight' });
      
      expect(defaultProps.onChange).toHaveBeenCalledWith({ min: 10, max: 1000 });
    });
  });
});
```

#### RatingFilter.test.tsx
```typescript
describe('RatingFilter', () => {
  const defaultProps = {
    minRating: 1,
    showReviewCount: true,
    onChange: jest.fn()
  };

  describe('Star Selection', () => {
    it('should highlight stars up to selected rating', () => {
      render(<RatingFilter {...defaultProps} />);
      
      const fourStarButton = screen.getByRole('button', { name: /4 stars or more/i });
      fireEvent.click(fourStarButton);
      
      expect(defaultProps.onChange).toHaveBeenCalledWith({ min: 4 });
      
      const stars = screen.getAllByTestId('star-icon');
      expect(stars[0]).toHaveClass('filled');
      expect(stars[3]).toHaveClass('filled');
      expect(stars[4]).toHaveClass('empty');
    });

    it('should show review count when enabled', () => {
      render(<RatingFilter {...defaultProps} showReviewCount={true} />);
      
      expect(screen.getByText(/minimum reviews/i)).toBeInTheDocument();
    });
  });
});
```

### 1.2 Hook Testing

**Test File**: `/src/__tests__/hooks/useUniversalFilter.test.ts`

```typescript
describe('useUniversalFilter', () => {
  const mockRouter = {
    push: jest.fn(),
    pathname: '/products',
    query: {}
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  describe('Filter State Management', () => {
    it('should initialize with empty filter state', () => {
      const { result } = renderHook(() => useUniversalFilter({
        pageType: 'products'
      }));

      expect(result.current.filterState).toEqual({
        appliedFilters: [],
        resultCount: 0,
        isLoading: false,
        hasError: false
      });
    });

    it('should update filter state correctly', async () => {
      const { result } = renderHook(() => useUniversalFilter({
        pageType: 'products'
      }));

      await act(async () => {
        await result.current.updateFilter('priceRange', { min: 10, max: 100 });
      });

      expect(result.current.filterState.priceRange).toEqual({ min: 10, max: 100 });
      expect(result.current.filterState.appliedFilters).toHaveLength(1);
    });

    it('should debounce rapid filter updates', async () => {
      const { result } = renderHook(() => useUniversalFilter({
        pageType: 'products'
      }));

      // Rapid updates
      await act(async () => {
        result.current.updateFilter('priceRange', { min: 10, max: 100 });
        result.current.updateFilter('priceRange', { min: 20, max: 200 });
        result.current.updateFilter('priceRange', { min: 30, max: 300 });
      });

      // Should only make one API call after debounce
      expect(fetchMock).toHaveBeenCalledTimes(1);
    });
  });

  describe('URL Synchronization', () => {
    it('should sync filter state to URL', async () => {
      const { result } = renderHook(() => useUniversalFilter({
        pageType: 'products'
      }));

      await act(async () => {
        await result.current.updateFilter('priceRange', { min: 10, max: 100 });
      });

      expect(mockRouter.push).toHaveBeenCalledWith('/products?price_min=10&price_max=100');
    });

    it('should parse filters from URL on initialization', () => {
      (useSearchParams as jest.Mock).mockReturnValue(
        new URLSearchParams('price_min=50&price_max=500&merchants=amazon,ebay')
      );

      const { result } = renderHook(() => useUniversalFilter({
        pageType: 'products'
      }));

      expect(result.current.filterState.priceRange).toEqual({ min: 50, max: 500 });
      expect(result.current.filterState.merchants).toEqual(['amazon', 'ebay']);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      fetchMock.mockRejectOnce(new Error('API Error'));

      const { result } = renderHook(() => useUniversalFilter({
        pageType: 'products'
      }));

      await act(async () => {
        await result.current.updateFilter('priceRange', { min: 10, max: 100 });
      });

      expect(result.current.filterState.hasError).toBe(true);
      expect(result.current.filterState.isLoading).toBe(false);
    });
  });
});
```

### 1.3 Utility Function Testing

**Test File**: `/src/__tests__/lib/utils/filterUtils.test.ts`

```typescript
describe('filterUtils', () => {
  describe('parseFilterParams', () => {
    it('should parse URL search params correctly', () => {
      const searchParams = new URLSearchParams('price_min=10&price_max=100&merchants=amazon,ebay');
      
      const result = parseFilterParams(searchParams);
      
      expect(result).toEqual({
        priceRange: { min: 10, max: 100 },
        merchants: ['amazon', 'ebay']
      });
    });

    it('should handle invalid parameters gracefully', () => {
      const searchParams = new URLSearchParams('price_min=invalid&merchants=');
      
      const result = parseFilterParams(searchParams);
      
      expect(result.priceRange).toBeUndefined();
      expect(result.merchants).toEqual([]);
    });
  });

  describe('generateCacheKey', () => {
    it('should generate consistent cache keys', () => {
      const filters = {
        priceRange: { min: 10, max: 100 },
        merchants: ['amazon', 'ebay']
      };

      const key1 = generateCacheKey(filters, 'products');
      const key2 = generateCacheKey(filters, 'products');

      expect(key1).toBe(key2);
    });

    it('should generate different keys for different filters', () => {
      const filters1 = { priceRange: { min: 10, max: 100 } };
      const filters2 = { priceRange: { min: 20, max: 200 } };

      const key1 = generateCacheKey(filters1, 'products');
      const key2 = generateCacheKey(filters2, 'products');

      expect(key1).not.toBe(key2);
    });
  });
});
```

## 2. Integration Testing Strategy

### 2.1 API Integration Tests

**Test File**: `/src/__tests__/api/filters.integration.test.ts`

```typescript
describe('Filter API Integration', () => {
  let supabase: any;

  beforeAll(async () => {
    supabase = createTestSupabaseClient();
    await seedTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  describe('GET /api/products with filters', () => {
    it('should filter products by price range', async () => {
      const response = await request(app)
        .get('/api/products')
        .query({ price_min: 10, price_max: 100 })
        .expect(200);

      const { products } = response.body;
      
      expect(products).toBeDefined();
      expect(products.length).toBeGreaterThan(0);
      products.forEach((product: any) => {
        expect(product.price).toBeGreaterThanOrEqual(10);
        expect(product.price).toBeLessThanOrEqual(100);
      });
    });

    it('should filter products by multiple merchants', async () => {
      const response = await request(app)
        .get('/api/products')
        .query({ merchants: 'amazon,ebay' })
        .expect(200);

      const { products } = response.body;
      
      products.forEach((product: any) => {
        expect(['amazon', 'ebay']).toContain(product.merchant_id);
      });
    });

    it('should return facet counts when requested', async () => {
      const response = await request(app)
        .get('/api/products')
        .query({ include_facet_counts: true })
        .expect(200);

      const { facetCounts } = response.body;
      
      expect(facetCounts).toBeDefined();
      expect(facetCounts.merchants).toBeDefined();
      expect(facetCounts.priceRanges).toBeDefined();
    });

    it('should handle multiple filters combined', async () => {
      const response = await request(app)
        .get('/api/products')
        .query({
          price_min: 50,
          price_max: 500,
          merchants: 'amazon',
          rating_min: 4
        })
        .expect(200);

      const { products } = response.body;
      
      products.forEach((product: any) => {
        expect(product.price).toBeGreaterThanOrEqual(50);
        expect(product.price).toBeLessThanOrEqual(500);
        expect(product.merchant_id).toBe('amazon');
        expect(product.average_rating).toBeGreaterThanOrEqual(4);
      });
    });
  });

  describe('Performance', () => {
    it('should respond within 300ms', async () => {
      const start = Date.now();
      
      await request(app)
        .get('/api/products')
        .query({ price_min: 10, price_max: 100 })
        .expect(200);
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(300);
    });

    it('should utilize caching effectively', async () => {
      const cacheKey = 'test-cache-key';
      
      // First request - cache miss
      const response1 = await request(app)
        .get('/api/products')
        .query({ price_min: 10, price_max: 100 })
        .expect(200);
      
      // Second request - cache hit (should be faster)
      const start = Date.now();
      const response2 = await request(app)
        .get('/api/products')
        .query({ price_min: 10, price_max: 100 })
        .expect(200);
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(50); // Cache hit should be very fast
      expect(response1.body).toEqual(response2.body);
    });
  });
});
```

### 2.2 Component Integration Tests

**Test File**: `/src/__tests__/integration/filterPanel.integration.test.tsx`

```typescript
describe('UniversalFilterPanel Integration', () => {
  beforeEach(() => {
    fetchMock.mockClear();
    mockRouter.push.mockClear();
  });

  it('should update URL and trigger API call when filter changes', async () => {
    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        products: mockProducts,
        totalCount: 100,
        facetCounts: mockFacetCounts
      })
    });

    render(<UniversalFilterPanel config={{ pageType: 'products' }} />);
    
    // Open filter panel
    fireEvent.click(screen.getByText('Filters'));
    
    // Change price filter
    const maxPriceSlider = screen.getByRole('slider', { name: /maximum price/i });
    fireEvent.change(maxPriceSlider, { target: { value: '100' } });
    
    // Wait for debounced API call
    await waitFor(() => {
      expect(fetchMock).toHaveBeenCalledWith(
        expect.stringContaining('/api/products?price_max=100'),
        expect.any(Object)
      );
    });
    
    // Verify URL updated
    expect(mockRouter.push).toHaveBeenCalledWith('/products?price_max=100');
  });

  it('should show applied filters and allow removal', async () => {
    const searchParams = new URLSearchParams('price_min=10&price_max=100&merchants=amazon');
    (useSearchParams as jest.Mock).mockReturnValue(searchParams);

    render(<UniversalFilterPanel config={{ pageType: 'products' }} />);
    
    // Should show applied filter chips
    expect(screen.getByText('Price: £10 - £100')).toBeInTheDocument();
    expect(screen.getByText('Merchant: Amazon')).toBeInTheDocument();
    
    // Remove price filter
    const priceChipRemove = screen.getByLabelText('Remove price filter');
    fireEvent.click(priceChipRemove);
    
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('/products?merchants=amazon');
    });
  });

  it('should prevent zero-result combinations', async () => {
    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        products: [],
        totalCount: 0,
        facetCounts: { merchants: { amazon: 0, ebay: 5 } }
      })
    });

    render(<UniversalFilterPanel config={{ pageType: 'products' }} />);
    
    // Apply filter that results in zero products from Amazon
    fireEvent.click(screen.getByText('Filters'));
    fireEvent.change(screen.getByRole('slider'), { target: { value: '10000' } });
    
    await waitFor(() => {
      const amazonCheckbox = screen.getByLabelText('Amazon');
      expect(amazonCheckbox).toBeDisabled();
      expect(screen.getByText('(0)')).toBeInTheDocument();
    });
  });
});
```

## 3. End-to-End Testing Strategy

### 3.1 Critical User Journeys

**Test File**: `/tests/e2e/filter-journeys.spec.ts`

```typescript
import { test, expect } from '@playwright/test';

test.describe('Universal Filter - Critical Journeys', () => {
  test('Deal Hunter Journey: Find highest cashback within budget', async ({ page }) => {
    await page.goto('/products');
    
    // Open filters
    await page.click('[data-testid="filter-button"]');
    
    // Set price range to £50-150
    await page.fill('[data-testid="price-min-input"]', '50');
    await page.fill('[data-testid="price-max-input"]', '150');
    
    // Sort by highest cashback
    await page.selectOption('[data-testid="sort-select"]', 'cashback-desc');
    
    // Verify results
    await expect(page.locator('[data-testid="product-card"]').first()).toBeVisible();
    
    const firstProduct = page.locator('[data-testid="product-card"]').first();
    const price = await firstProduct.locator('[data-testid="product-price"]').textContent();
    
    expect(price).toMatch(/£(5[0-9]|[6-9][0-9]|1[0-4][0-9]|150)/); // £50-150 range
    
    // Verify URL contains filters
    expect(page.url()).toContain('price_min=50');
    expect(page.url()).toContain('price_max=150');
  });

  test('Trust Seeker Journey: Filter by rating and preferred merchant', async ({ page }) => {
    await page.goto('/products');
    
    // Open filters
    await page.click('[data-testid="filter-button"]');
    
    // Set minimum rating to 4 stars
    await page.click('[data-testid="rating-4-stars"]');
    
    // Select Amazon as preferred merchant
    await page.check('[data-testid="merchant-amazon"]');
    
    // Apply filters (on mobile)
    if (await page.locator('[data-testid="apply-filters-mobile"]').isVisible()) {
      await page.click('[data-testid="apply-filters-mobile"]');
    }
    
    // Verify results
    const products = page.locator('[data-testid="product-card"]');
    await expect(products.first()).toBeVisible();
    
    // Verify all products meet criteria
    const productCount = await products.count();
    for (let i = 0; i < Math.min(productCount, 5); i++) {
      const product = products.nth(i);
      const rating = await product.locator('[data-testid="product-rating"]').getAttribute('data-rating');
      const merchant = await product.locator('[data-testid="product-merchant"]').textContent();
      
      expect(parseFloat(rating || '0')).toBeGreaterThanOrEqual(4);
      expect(merchant).toContain('Amazon');
    }
  });

  test('Browser Journey: Search then filter by category and price', async ({ page }) => {
    await page.goto('/search');
    
    // Search for "laptop"
    await page.fill('[data-testid="search-input"]', 'laptop');
    await page.press('[data-testid="search-input"]', 'Enter');
    
    await page.waitForLoadState('networkidle');
    
    // Open filters
    await page.click('[data-testid="filter-button"]');
    
    // Filter by Electronics category
    await page.check('[data-testid="category-electronics"]');
    
    // Set price range
    await page.fill('[data-testid="price-max-input"]', '1000');
    
    // Verify results update
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
    
    // Verify applied filters are shown
    await expect(page.locator('[data-testid="applied-filter-chip"]')).toHaveCount(2);
    
    // Verify URL contains all parameters
    expect(page.url()).toContain('q=laptop');
    expect(page.url()).toContain('category=electronics');
    expect(page.url()).toContain('price_max=1000');
  });
});

test.describe('Mobile Filter Experience', () => {
  test.use({ viewport: { width: 375, height: 667 } }); // iPhone SE

  test('should show mobile drawer pattern', async ({ page }) => {
    await page.goto('/products');
    
    // Filter button should be visible in mobile header
    const filterButton = page.locator('[data-testid="filter-button-mobile"]');
    await expect(filterButton).toBeVisible();
    
    // Click to open drawer
    await filterButton.click();
    
    // Drawer should slide up from bottom
    const drawer = page.locator('[data-testid="filter-drawer"]');
    await expect(drawer).toBeVisible();
    await expect(drawer).toHaveClass(/drawer-open/);
    
    // Should have sticky "Show X results" button
    const resultsButton = page.locator('[data-testid="show-results-button"]');
    await expect(resultsButton).toBeVisible();
    await expect(resultsButton).toHaveCSS('position', 'sticky');
  });

  test('should handle thumb-friendly interactions', async ({ page }) => {
    await page.goto('/products');
    
    // Open filter drawer
    await page.click('[data-testid="filter-button-mobile"]');
    
    // Filter options should be large enough for thumb interaction
    const checkboxes = page.locator('[data-testid^="merchant-checkbox"]');
    const firstCheckbox = checkboxes.first();
    
    const boundingBox = await firstCheckbox.boundingBox();
    expect(boundingBox?.height).toBeGreaterThanOrEqual(44); // iOS minimum touch target
    
    // Should be able to tap checkbox
    await firstCheckbox.click();
    await expect(firstCheckbox).toBeChecked();
  });
});

test.describe('Performance and Accessibility', () => {
  test('should meet performance benchmarks', async ({ page }) => {
    // Start performance monitoring
    await page.goto('/products', { waitUntil: 'networkidle' });
    
    const start = Date.now();
    
    // Apply filter
    await page.click('[data-testid="filter-button"]');
    await page.fill('[data-testid="price-max-input"]', '100');
    
    // Wait for results to update
    await page.waitForResponse('/api/products?*');
    
    const duration = Date.now() - start;
    
    // Should respond within 2 seconds
    expect(duration).toBeLessThan(2000);
  });

  test('should be keyboard accessible', async ({ page }) => {
    await page.goto('/products');
    
    // Navigate to filter button using keyboard
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab'); // Assuming filter button is second focusable element
    
    // Open filter panel with Enter
    await page.keyboard.press('Enter');
    
    // Should be able to navigate through filter options
    await page.keyboard.press('Tab');
    const focusedElement = await page.evaluate(() => document.activeElement?.getAttribute('data-testid'));
    
    expect(focusedElement).toBeTruthy();
    
    // Should be able to close drawer with Escape
    await page.keyboard.press('Escape');
    const drawer = page.locator('[data-testid="filter-drawer"]');
    await expect(drawer).not.toBeVisible();
  });
});
```

## 4. Performance Testing

### 4.1 Load Testing

```typescript
// Performance test with Artillery.js
// artillery-filter-load.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 120
      arrivalRate: 50
      name: "Filter load test"

scenarios:
  - name: "Filter products by price"
    requests:
      - get:
          url: "/api/products"
          qs:
            price_min: 10
            price_max: 1000
            include_facet_counts: true
  - name: "Filter products by multiple criteria"
    requests:
      - get:
          url: "/api/products"
          qs:
            price_min: 50
            price_max: 500
            merchants: "amazon,ebay"
            rating_min: 4
```

### 4.2 Cache Performance Testing

```typescript
describe('Cache Performance', () => {
  it('should show significant performance improvement with cache', async () => {
    const filters = { price_min: 10, price_max: 100 };
    
    // First request (cache miss)
    const start1 = Date.now();
    const response1 = await fetch('/api/products?' + new URLSearchParams(filters));
    const duration1 = Date.now() - start1;
    
    // Second request (cache hit)
    const start2 = Date.now();
    const response2 = await fetch('/api/products?' + new URLSearchParams(filters));
    const duration2 = Date.now() - start2;
    
    // Cache hit should be significantly faster
    expect(duration2).toBeLessThan(duration1 * 0.1); // 90% improvement
    expect(response1.status).toBe(200);
    expect(response2.status).toBe(200);
  });
});
```

## 5. Browser Compatibility Testing

### 5.1 Cross-Browser Test Matrix

| Browser | Version | Desktop | Mobile | Test Coverage |
|---------|---------|---------|---------|---------------|
| Chrome | Latest | ✅ | ✅ | Full E2E |
| Firefox | Latest | ✅ | ✅ | Full E2E |
| Safari | Latest | ✅ | ✅ | Full E2E |
| Edge | Latest | ✅ | ❌ | Core Functions |
| Chrome | -2 versions | ✅ | ✅ | Smoke Tests |

### 5.2 Device Testing

```typescript
// Playwright device testing
const devices = [
  'iPhone 12',
  'iPhone SE',
  'Pixel 5',
  'Samsung Galaxy S21',
  'iPad',
  'Desktop Chrome'
];

devices.forEach(deviceName => {
  test.describe(`Filter Tests on ${deviceName}`, () => {
    test.use({ ...devices[deviceName] });
    
    test('basic filter functionality', async ({ page }) => {
      // Device-specific filter tests
    });
  });
});
```

## 6. Monitoring and Analytics Testing

### 6.1 Analytics Event Testing

```typescript
describe('Filter Analytics', () => {
  let mockAnalytics: jest.SpyInstance;
  
  beforeEach(() => {
    mockAnalytics = jest.spyOn(analytics, 'track');
  });

  it('should track filter application events', async () => {
    render(<UniversalFilterPanel config={{ pageType: 'products' }} />);
    
    // Apply price filter
    fireEvent.change(screen.getByRole('slider'), { target: { value: '100' } });
    
    await waitFor(() => {
      expect(mockAnalytics).toHaveBeenCalledWith('Filter Applied', {
        facet: 'priceRange',
        value: { max: 100 },
        page: '/products',
        sessionId: expect.any(String)
      });
    });
  });

  it('should track zero-result scenarios', async () => {
    // Mock API to return zero results
    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ products: [], totalCount: 0 })
    });

    render(<UniversalFilterPanel config={{ pageType: 'products' }} />);
    
    fireEvent.change(screen.getByRole('slider'), { target: { value: '99999' } });
    
    await waitFor(() => {
      expect(mockAnalytics).toHaveBeenCalledWith('Filter Zero Results', {
        facet: 'priceRange',
        appliedFilters: expect.any(Array)
      });
    });
  });
});
```

## 7. Test Data Management

### 7.1 Test Data Setup

```typescript
// Test data seeding
const seedFilterTestData = async () => {
  const products = [
    {
      id: 'test-product-1',
      name: 'Test Laptop',
      price: 799.99,
      merchant_id: 'amazon',
      average_rating: 4.5,
      review_count: 1250,
      availability_status: 'in-stock'
    },
    // ... more test products
  ];
  
  await supabase.from('products').insert(products);
};

const cleanupFilterTestData = async () => {
  await supabase.from('products').delete().like('id', 'test-product-%');
};
```

### 7.2 Mock Data Factories

```typescript
const createMockProduct = (overrides = {}) => ({
  id: faker.datatype.uuid(),
  name: faker.commerce.productName(),
  price: faker.datatype.number({ min: 10, max: 2000 }),
  merchant_id: faker.helpers.arrayElement(['amazon', 'ebay', 'walmart']),
  average_rating: faker.datatype.number({ min: 1, max: 5, precision: 0.1 }),
  review_count: faker.datatype.number({ min: 0, max: 5000 }),
  availability_status: faker.helpers.arrayElement(['in-stock', 'out-of-stock', 'pre-order']),
  ...overrides
});
```

## 8. Continuous Integration Testing

### 8.1 GitHub Actions Test Pipeline

```yaml
# .github/workflows/filter-tests.yml
name: Filter Tests

on:
  pull_request:
    paths:
      - 'src/components/filters/**'
      - 'src/hooks/useUniversalFilter.ts'
      - 'src/app/api/**'

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:filters
      - run: npm run test:coverage -- --testPathPattern=filters
      
  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - run: npm ci
      - run: npm run test:integration:filters
      
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e:filters
```

## 9. Quality Gates

### 9.1 Test Coverage Requirements

- **Unit Tests**: ≥ 90% line coverage for filter components and hooks
- **Integration Tests**: ≥ 80% coverage for API routes with filter parameters  
- **E2E Tests**: 100% coverage of critical user journeys
- **Performance Tests**: All filter operations must complete within SLA times

### 9.2 Acceptance Criteria

Before deployment, all tests must pass:

1. ✅ All unit tests pass with required coverage
2. ✅ Integration tests pass with real database
3. ✅ E2E tests pass on all supported devices
4. ✅ Performance tests meet SLA requirements
5. ✅ Accessibility tests pass WCAG 2.2 AA
6. ✅ Cross-browser compatibility verified
7. ✅ Load tests demonstrate system stability

---

**Document Version**: 1.0  
**Last Updated**: 10 JUL 2025  
**Review Status**: Ready for QA Review  
**Implementation Phase**: All Sprints
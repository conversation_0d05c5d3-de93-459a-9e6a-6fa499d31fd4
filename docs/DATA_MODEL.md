# Data Model Documentation

*This file is auto-generated documentation for the Cashback Deals database schema and data layer. Last updated: January 2025*

## Overview

The Cashback Deals platform uses a PostgreSQL database hosted on Supabase with a comprehensive schema supporting e-commerce operations, cashback management, and audit logging.

## Database Schema Overview

### Core Entity Relationships

```mermaid
erDiagram
    USERS {
        uuid id PK
        varchar full_name
        varchar email UK
        varchar password_hash
        varchar profile_picture_url
        varchar country_code
        varchar preferred_language
        timestamp created_at
        timestamp updated_at
    }
    
    BRANDS {
        uuid id PK
        varchar name UK
        varchar slug UK
        varchar logo_url
        text description
        boolean featured
        boolean sponsored
        timestamp created_at
        timestamp updated_at
        bigint version
        tsvector search_vector
    }
    
    CATEGORIES {
        uuid id PK
        varchar name
        varchar slug UK
        uuid parent_id FK
        boolean featured
        boolean sponsored
        timestamp created_at
        bigint version
    }
    
    PRODUCTS {
        uuid id PK
        varchar name
        varchar slug UK
        uuid brand_id FK
        uuid category_id FK
        text description
        jsonb specifications
        text[] images
        varchar status
        boolean is_featured
        boolean is_sponsored
        timestamp created_at
        timestamp updated_at
        bigint version
        tsvector search_vector
    }
    
    RETAILERS {
        uuid id PK
        varchar name UK
        varchar slug UK
        varchar logo_url
        varchar status
        boolean featured
        boolean sponsored
        varchar website_url
        varchar claim_period
        varchar api_key_hash
        varchar api_secret_hash
        timestamp created_at
        bigint version
    }
    
    PROMOTIONS {
        uuid id PK
        uuid brand_id FK
        uuid category_id FK
        varchar title
        text description
        decimal max_cashback_amount
        date purchase_start_date
        date purchase_end_date
        int claim_start_offset_days
        int claim_window_days
        varchar terms_url
        text terms_description
        promotion_status_type status
        timestamp last_validated_at
        timestamp created_at
        bigint version
    }
    
    SKUS {
        uuid id PK
        uuid product_id FK
        uuid retailer_id FK
        varchar manufacturer_sku
        varchar retailer_sku
        varchar gtin
        varchar mpn
        varchar color
        varchar size
        varchar variant_name
        boolean is_default
        varchar status
        jsonb metadata
        timestamp created_at
        timestamp updated_at
        bigint version
    }
    
    PRODUCT_RETAILER_OFFERS {
        uuid id PK
        uuid product_id FK
        uuid retailer_id FK
        uuid promotion_id FK
        uuid sku_id FK
        decimal price
        stock_status_type stock_status
        varchar url
        date valid_from
        date valid_until
        timestamp created_at
        timestamp updated_at
        bigint version
    }
    
    USER_PURCHASES {
        uuid id PK
        uuid user_id FK
        uuid product_id FK
        uuid retailer_id FK
        uuid promotion_id FK
        uuid sku_id FK
        timestamp purchase_date
        decimal purchase_price
        varchar affiliate_click_url
        timestamp claim_start_date
        timestamp claim_end_date
        timestamp claim_submitted_date
        varchar claim_status
        timestamp created_at
        bigint version
    }
    
    AUDIT_LOG {
        uuid id PK
        text table_name
        uuid record_id
        text operation
        jsonb old_data
        jsonb new_data
        uuid changed_by FK
        timestamp changed_at
    }
    
    USERS ||--o{ USER_PURCHASES : "makes"
    USERS ||--o{ USER_FAVORITES : "has"
    USERS ||--o{ CASHBACK_REMINDERS : "sets"
    USERS ||--o{ USER_CASHBACK_CLAIMS : "submits"
    
    BRANDS ||--o{ PRODUCTS : "manufactures"
    BRANDS ||--o{ PROMOTIONS : "sponsors"
    
    CATEGORIES ||--o{ PRODUCTS : "contains"
    CATEGORIES ||--o{ PROMOTIONS : "applies_to"
    CATEGORIES ||--o{ CATEGORIES : "parent_of"
    
    PRODUCTS ||--o{ SKUS : "has_variants"
    PRODUCTS ||--o{ PRODUCT_RETAILER_OFFERS : "sold_by"
    PRODUCTS ||--o{ USER_PURCHASES : "purchased_as"
    PRODUCTS ||--o{ USER_FAVORITES : "favorited_as"
    
    RETAILERS ||--o{ SKUS : "stocks"
    RETAILERS ||--o{ PRODUCT_RETAILER_OFFERS : "offers"
    RETAILERS ||--o{ USER_PURCHASES : "processes"
    
    PROMOTIONS ||--o{ PRODUCT_RETAILER_OFFERS : "applies_to"
    PROMOTIONS ||--o{ USER_PURCHASES : "used_in"
    PROMOTIONS ||--o{ CASHBACK_REMINDERS : "reminds_about"
    
    SKUS ||--o{ PRODUCT_RETAILER_OFFERS : "available_as"
    SKUS ||--o{ USER_PURCHASES : "purchased_as"
    
    PRODUCT_RETAILER_OFFERS ||--o{ USER_CASHBACK_CLAIMS : "claimed_from"
```

## Table Definitions

### Core Tables

#### Users (`public.users`)
Stores user profile information and authentication details.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | uuid | PRIMARY KEY | Unique user identifier |
| `full_name` | varchar(100) | | User's full name |
| `email` | varchar(150) | UNIQUE, NOT NULL | User's email address |
| `password_hash` | varchar(255) | | Encrypted password |
| `profile_picture_url` | varchar(255) | | URL to user's profile image |
| `country_code` | varchar(5) | | ISO country code |
| `preferred_language` | varchar(5) | | ISO language code |
| `created_at` | timestamp | DEFAULT current_timestamp | Account creation time |
| `updated_at` | timestamp | DEFAULT current_timestamp | Last update time |

**Indexes:**
- `users_email_idx` - Unique index on email
- `users_country_code_idx` - Index for country-based queries

**RLS Policies:**
- Users can view their own profile
- Users can update their own profile

#### Products (`public.products`)
Main product catalog with full-text search capabilities.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | uuid | PRIMARY KEY | Unique product identifier |
| `name` | varchar(150) | NOT NULL | Product name |
| `slug` | varchar(150) | UNIQUE, NOT NULL | URL-friendly identifier |
| `brand_id` | uuid | FK to brands(id) | Associated brand |
| `category_id` | uuid | FK to categories(id) | Product category |
| `description` | text | | Product description |
| `specifications` | jsonb | | Technical specifications |
| `images` | text[] | | Array of image URLs |
| `status` | varchar(20) | CHECK (active/inactive/discontinued) | Product status |
| `is_featured` | boolean | DEFAULT false | Featured product flag |
| `is_sponsored` | boolean | DEFAULT false | Sponsored product flag |
| `model_number` | varchar(50) | | Manufacturer model number |
| `cashback_amount` | decimal(10,2) | | Current cashback amount |
| `promotion_id` | uuid | FK to promotions(id) | Active promotion |
| `created_at` | timestamp | DEFAULT current_timestamp | Creation time |
| `updated_at` | timestamp | DEFAULT current_timestamp | Last update time |
| `version` | bigint | DEFAULT 1 | Optimistic locking version |
| `search_vector` | tsvector | GENERATED | Full-text search vector |

**Indexes:**
- `idx_products_brand_id` - Brand-based filtering
- `idx_products_category_id` - Category-based filtering
- `idx_products_status` - Status filtering
- `idx_products_search` - Full-text search (GIN)
- `products_search_idx` - Search vector index

**RLS Policies:**
- Products are viewable by everyone (status = 'active')

<details>
<summary>Sample Product Queries</summary>

```sql
-- Get featured products with brand and category
SELECT p.*, b.name as brand_name, c.name as category_name
FROM products p
LEFT JOIN brands b ON p.brand_id = b.id
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.is_featured = true AND p.status = 'active'
ORDER BY p.created_at DESC
LIMIT 10;

-- Full-text search for products
SELECT p.*, ts_rank(p.search_vector, plainto_tsquery('english', 'laptop gaming')) as rank
FROM products p
WHERE p.search_vector @@ plainto_tsquery('english', 'laptop gaming')
  AND p.status = 'active'
ORDER BY rank DESC, p.created_at DESC;

-- Get products by brand with pagination
SELECT p.*, COUNT(*) OVER() as total_count
FROM products p
WHERE p.brand_id = $1 AND p.status = 'active'
ORDER BY p.name
LIMIT $2 OFFSET $3;
```

</details>

#### Brands (`public.brands`)
Product brand information with search capabilities.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | uuid | PRIMARY KEY | Unique brand identifier |
| `name` | varchar(100) | UNIQUE, NOT NULL | Brand name |
| `slug` | varchar(100) | UNIQUE, NOT NULL | URL-friendly identifier |
| `logo_url` | varchar(255) | | Brand logo URL |
| `description` | text | | Brand description |
| `featured` | boolean | DEFAULT false | Featured brand flag |
| `sponsored` | boolean | DEFAULT false | Sponsored brand flag |
| `created_at` | timestamp | DEFAULT current_timestamp | Creation time |
| `updated_at` | timestamp | DEFAULT current_timestamp | Last update time |
| `version` | bigint | DEFAULT 1 | Optimistic locking version |
| `search_vector` | tsvector | GENERATED | Full-text search vector |

**Indexes:**
- `idx_brands_featured` - Featured brand filtering
- `idx_brands_search` - Full-text search (GIN)

#### Retailers (`public.retailers`)
Retailer information and API credentials.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | uuid | PRIMARY KEY | Unique retailer identifier |
| `name` | varchar(150) | UNIQUE, NOT NULL | Retailer name |
| `slug` | varchar(150) | UNIQUE, NOT NULL | URL-friendly identifier |
| `logo_url` | varchar(255) | | Retailer logo URL |
| `status` | varchar(20) | DEFAULT 'active' | Retailer status |
| `featured` | boolean | DEFAULT false | Featured retailer flag |
| `sponsored` | boolean | DEFAULT false | Sponsored retailer flag |
| `website_url` | varchar(255) | | Retailer website URL |
| `claim_period` | varchar(255) | | Cashback claim period |
| `api_key_hash` | varchar(255) | | Encrypted API key |
| `api_secret_hash` | varchar(255) | | Encrypted API secret |
| `created_at` | timestamp | DEFAULT current_timestamp | Creation time |
| `version` | bigint | DEFAULT 1 | Optimistic locking version |

### Promotion & Cashback Tables

#### Promotions (`public.promotions`)
Cashback promotions with time-based validation.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | uuid | PRIMARY KEY | Unique promotion identifier |
| `brand_id` | uuid | FK to brands(id) | Associated brand |
| `category_id` | uuid | FK to categories(id) | Associated category |
| `title` | varchar(150) | NOT NULL | Promotion title |
| `description` | text | | Promotion description |
| `max_cashback_amount` | decimal(10,2) | NOT NULL | Maximum cashback amount |
| `purchase_start_date` | date | NOT NULL | Purchase period start |
| `purchase_end_date` | date | NOT NULL | Purchase period end |
| `claim_start_offset_days` | int | NOT NULL | Days after purchase to claim |
| `claim_window_days` | int | NOT NULL | Claim window duration |
| `terms_url` | varchar(255) | | Terms and conditions URL |
| `terms_description` | text | | Terms description |
| `status` | promotion_status_type | DEFAULT 'draft' | Promotion status |
| `last_validated_at` | timestamp | | Last validation time |
| `is_featured` | boolean | DEFAULT false | Featured promotion flag |
| `created_at` | timestamp | DEFAULT current_timestamp | Creation time |
| `version` | bigint | DEFAULT 1 | Optimistic locking version |

**Constraints:**
- `valid_dates` - purchase_start_date < purchase_end_date
- `valid_claim_window` - claim_window_days > 0
- `valid_offset` - claim_start_offset_days >= 0

**Custom Types:**
```sql
CREATE TYPE promotion_status_type AS ENUM (
    'draft',
    'scheduled', 
    'active',
    'paused',
    'expired',
    'cancelled'
);
```

#### Product Retailer Offers (`public.product_retailer_offers`)
Links products, retailers, and promotions with pricing.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | uuid | PRIMARY KEY | Unique offer identifier |
| `product_id` | uuid | FK to products(id) | Associated product |
| `retailer_id` | uuid | FK to retailers(id) | Associated retailer |
| `promotion_id` | uuid | FK to promotions(id) | Associated promotion |
| `sku_id` | uuid | FK to skus(id) | Associated SKU |
| `price` | decimal(10,2) | NOT NULL | Product price |
| `stock_status` | stock_status_type | DEFAULT 'in_stock' | Stock status |
| `url` | varchar(255) | | Product page URL |
| `valid_from` | date | NOT NULL | Offer valid from date |
| `valid_until` | date | NOT NULL | Offer valid until date |
| `created_at` | timestamp | DEFAULT current_timestamp | Creation time |
| `updated_at` | timestamp | DEFAULT current_timestamp | Last update time |
| `version` | bigint | DEFAULT 1 | Optimistic locking version |

**Constraints:**
- `unique_product_retailer_promotion` - Unique combination per valid_from
- `valid_dates` - valid_from < valid_until
- `positive_price` - price > 0

**Custom Types:**
```sql
CREATE TYPE stock_status_type AS ENUM (
    'in_stock',
    'low_stock',
    'out_of_stock',
    'discontinued',
    'pre_order'
);
```

### Advanced Tables

#### SKUs (`public.skus`)
Product SKUs and variants with metadata validation.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | uuid | PRIMARY KEY | Unique SKU identifier |
| `product_id` | uuid | FK to products(id) | Associated product |
| `retailer_id` | uuid | FK to retailers(id) | Associated retailer |
| `manufacturer_sku` | varchar(50) | NOT NULL | Manufacturer SKU |
| `retailer_sku` | varchar(50) | | Retailer-specific SKU |
| `gtin` | varchar(14) | | Global Trade Item Number |
| `mpn` | varchar(50) | | Manufacturer Part Number |
| `color` | varchar(50) | | Product color |
| `size` | varchar(50) | | Product size |
| `variant_name` | varchar(100) | | Variant description |
| `is_default` | boolean | DEFAULT false | Default SKU flag |
| `status` | varchar(20) | DEFAULT 'active' | SKU status |
| `metadata` | jsonb | NOT NULL | Additional metadata |
| `created_at` | timestamp | DEFAULT current_timestamp | Creation time |
| `updated_at` | timestamp | DEFAULT current_timestamp | Last update time |
| `version` | bigint | DEFAULT 1 | Optimistic locking version |

**Constraints:**
- `valid_manufacturer_sku` - Length between 3-50 characters
- `valid_retailer_sku` - Length between 3-50 characters (if not null)
- `valid_gtin` - 13-14 digit numeric format
- `valid_status` - active/inactive/discontinued

**Unique Indexes:**
- `idx_unique_default_sku_per_product` - Only one default SKU per product

<details>
<summary>SKU Metadata Validation</summary>

```sql
-- Trigger function to validate SKU metadata
CREATE OR REPLACE FUNCTION validate_sku_metadata()
RETURNS trigger AS $$
DECLARE
    required_fields TEXT[] := ARRAY['specifications', 'dimensions', 'features'];
    field TEXT;
BEGIN
    -- Check metadata has required fields
    FOREACH field IN ARRAY required_fields
    LOOP
        IF NOT (NEW.metadata ? field) THEN
            RAISE EXCEPTION 'Required metadata field % is missing', field;
        END IF;
    END LOOP;
    
    -- Check metadata doesn't exceed 20 keys
    IF jsonb_array_length(jsonb_object_keys(NEW.metadata)::jsonb) > 20 THEN
        RAISE EXCEPTION 'Metadata exceeds maximum allowed properties (20)';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

</details>

### Audit & Logging Tables

#### Audit Log (`public.audit_log`)
Centralized audit log for tracking changes.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | uuid | PRIMARY KEY | Unique log entry identifier |
| `table_name` | text | NOT NULL | Table that was modified |
| `record_id` | uuid | NOT NULL | ID of the modified record |
| `operation` | text | NOT NULL | Operation type (INSERT/UPDATE/DELETE) |
| `old_data` | jsonb | | Previous record data |
| `new_data` | jsonb | | New record data |
| `changed_by` | uuid | FK to auth.users(id) | User who made the change |
| `changed_at` | timestamp | DEFAULT current_timestamp | When the change occurred |

#### Cache Invalidation Log (`public.cache_invalidation_log`)
Tracks cache refresh events for debugging.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | uuid | PRIMARY KEY | Unique log entry identifier |
| `cache_key` | text | NOT NULL | Cache key that was invalidated |
| `invalidated_at` | timestamp | DEFAULT current_timestamp | When cache was invalidated |
| `reason` | text | | Reason for invalidation |
| `triggered_by` | uuid | FK to auth.users(id) | User who triggered invalidation |

## Caching Strategy Matrix

### Cache Layers and TTL

| Data Type | Cache Key Pattern | TTL | Invalidation Triggers | Used By |
|-----------|-------------------|-----|----------------------|---------|
| **Products** | `products:${filters}:${page}` | 5 min | Product CRUD, Stock updates | Product listings |
| **Product Detail** | `product:${id}` | 30 min | Product updates, Price changes | Product pages |
| **Featured Products** | `products:featured:${limit}` | 1 hour | Featured flag changes | Homepage |
| **Brands** | `brands:${page}:${limit}` | 1 hour | Brand CRUD operations | Brand listings |
| **Brand Detail** | `brand:${id}` | 1 hour | Brand updates, Product additions | Brand pages |
| **Search Results** | `search:${query}:${filters}` | 5 min | Product updates, New products | Search pages |
| **Search Suggestions** | `search:suggestions:${query}` | 30 min | Product/Brand additions | Search autocomplete |
| **Promotions** | `promotions:active` | 30 min | Promotion status changes | Multiple pages |
| **Retailers** | `retailers:${filters}` | 1 hour | Retailer updates | Retailer listings |
| **Categories** | `categories:tree` | 24 hours | Category structure changes | Navigation |
| **Sitemap** | `sitemap:all` | 6 hours | Product/Brand additions | SEO |

### Cache Invalidation Patterns

```typescript
// Example cache invalidation on product update
export async function updateProduct(id: string, data: ProductUpdateData) {
  // Update database
  const product = await supabase
    .from('products')
    .update(data)
    .eq('id', id)
    .select()
    .single()
  
  // Invalidate related caches
  await invalidateCacheTags([
    CACHE_TAGS.PRODUCT,
    CACHE_TAGS.PRODUCTS,
    CACHE_TAGS.SEARCH,
    CACHE_TAGS.FEATURED
  ])
  
  return product
}
```

## Database Functions & Triggers

### Key Database Functions

<details>
<summary>Calculate Claim Dates Function</summary>

```sql
CREATE OR REPLACE FUNCTION calculate_claim_dates()
RETURNS trigger AS $$
BEGIN
  -- Calculate claim dates based on promotion settings
  SELECT 
    NEW.purchase_date + (p.claim_start_offset_days * interval '1 day'),
    NEW.purchase_date + (p.claim_start_offset_days * interval '1 day') + (p.claim_window_days * interval '1 day')
  INTO NEW.claim_start_date, NEW.claim_end_date
  FROM promotions p
  WHERE p.id = NEW.promotion_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically calculate claim dates
CREATE TRIGGER set_claim_dates
  BEFORE INSERT ON user_purchases
  FOR EACH ROW
  EXECUTE FUNCTION calculate_claim_dates();
```

</details>

<details>
<summary>Promotion Status Update Function</summary>

```sql
CREATE OR REPLACE FUNCTION update_promotion_status()
RETURNS trigger AS $$
BEGIN
    -- Automatically update promotion status based on dates
    IF NEW.purchase_end_date < current_date THEN
        NEW.status = 'expired';
    ELSIF NEW.purchase_start_date > current_date THEN
        NEW.status = 'scheduled';
    ELSIF NEW.status = 'expired' THEN
        NEW.status = 'expired';
    ELSE
        NEW.status = 'active';
    END IF;
    
    NEW.last_validated_at = current_timestamp;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

</details>

### Materialized Views

#### Product Summary View
Cached product information for performance.

```sql
CREATE MATERIALIZED VIEW mv_product_summary AS
SELECT 
    p.id as product_id,
    MIN(pro.price) as min_price,
    MAX(pro.price) as max_price,
    MAX(prom.max_cashback_amount) as max_cashback,
    COUNT(DISTINCT pro.retailer_id) as retailer_count,
    ARRAY_AGG(DISTINCT s.manufacturer_sku) as manufacturer_skus
FROM products p
JOIN product_retailer_offers pro ON p.id = pro.product_id
LEFT JOIN promotions prom ON pro.promotion_id = prom.id
JOIN skus s ON pro.sku_id = s.id
WHERE pro.valid_until > current_date
GROUP BY p.id;

-- Refresh function with logging
CREATE OR REPLACE FUNCTION refresh_product_summary_with_logging()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_product_summary;
    
    INSERT INTO cache_invalidation_log (cache_key, reason, triggered_by)
    VALUES ('mv_product_summary', 'Manual refresh triggered', auth.uid());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Sample Data Operations

### Read Operations

<details>
<summary>Complex Product Query with Relationships</summary>

```sql
-- Get products with brand, category, and active promotions
SELECT 
    p.id,
    p.name,
    p.slug,
    p.description,
    p.images,
    p.is_featured,
    b.name as brand_name,
    b.logo_url as brand_logo,
    c.name as category_name,
    prom.title as promotion_title,
    prom.max_cashback_amount,
    prom.purchase_end_date,
    MIN(pro.price) as min_price,
    COUNT(DISTINCT pro.retailer_id) as retailer_count
FROM products p
LEFT JOIN brands b ON p.brand_id = b.id
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN product_retailer_offers pro ON p.id = pro.product_id
LEFT JOIN promotions prom ON pro.promotion_id = prom.id
    AND prom.status = 'active'
    AND prom.purchase_end_date >= current_date
WHERE p.status = 'active'
    AND pro.valid_until > current_date
GROUP BY p.id, b.name, b.logo_url, c.name, prom.title, prom.max_cashback_amount, prom.purchase_end_date
ORDER BY p.is_featured DESC, p.created_at DESC
LIMIT 20;
```

</details>

### Write Operations

<details>
<summary>Create Product with SKU and Offer</summary>

```sql
-- Transaction to create product with associated data
BEGIN;

-- Insert product
INSERT INTO products (name, slug, brand_id, category_id, description, specifications, images, status)
VALUES ($1, $2, $3, $4, $5, $6, $7, 'active')
RETURNING id INTO @product_id;

-- Insert default SKU
INSERT INTO skus (product_id, manufacturer_sku, is_default, status, metadata)
VALUES (@product_id, $8, true, 'active', $9)
RETURNING id INTO @sku_id;

-- Insert retailer offer
INSERT INTO product_retailer_offers (product_id, retailer_id, sku_id, price, stock_status, valid_from, valid_until)
VALUES (@product_id, $10, @sku_id, $11, 'in_stock', current_date, current_date + interval '1 year');

COMMIT;
```

</details>

## Row Level Security (RLS) Policies

### User Data Policies

```sql
-- Users can only view their own data
CREATE POLICY "Users can view own profile"
    ON users FOR SELECT
    TO authenticated
    USING (auth.uid() = id);

-- Users can only update their own data
CREATE POLICY "Users can update own profile"
    ON users FOR UPDATE
    TO authenticated
    USING (auth.uid() = id);
```

### Public Data Policies

```sql
-- Products are viewable by everyone (only active ones)
CREATE POLICY "Products viewable by everyone"
    ON products FOR SELECT
    TO anon
    USING (status = 'active');

-- Brands are viewable by everyone
CREATE POLICY "Brands viewable by everyone"
    ON brands FOR SELECT
    TO anon
    USING (true);
```

### Restricted Data Policies

```sql
-- SKU metadata is restricted based on retailer association
CREATE POLICY "Limited SKU info viewable by everyone"
    ON skus FOR SELECT
    TO anon
    USING (status = 'active' AND retailer_id IS NULL);

-- Authenticated users can see more SKU details
CREATE POLICY "Authenticated users can view active SKUs"
    ON skus FOR SELECT
    TO authenticated
    USING (status = 'active');
```

## Performance Optimization

### Critical Indexes

```sql
-- Product search performance
CREATE INDEX idx_products_search ON products USING gin (search_vector);
CREATE INDEX idx_products_brand_category ON products (brand_id, category_id, status);
CREATE INDEX idx_products_featured_status ON products (is_featured, status, created_at);

-- Offer and pricing queries
CREATE INDEX idx_offers_product_valid ON product_retailer_offers (product_id, valid_until);
CREATE INDEX idx_offers_retailer_valid ON product_retailer_offers (retailer_id, valid_until);
CREATE INDEX idx_offers_promotion_valid ON product_retailer_offers (promotion_id, valid_until);

-- Promotion date queries
CREATE INDEX idx_promotions_dates ON promotions (purchase_start_date, purchase_end_date, status);
CREATE INDEX idx_promotions_brand_active ON promotions (brand_id, status, purchase_end_date);

-- SKU lookup performance
CREATE INDEX idx_skus_product_status ON skus (product_id, status);
CREATE INDEX idx_skus_manufacturer_sku_lower ON skus (lower(manufacturer_sku));
CREATE INDEX idx_skus_gtin ON skus (gtin);
```

### Query Optimization Patterns

1. **Use specific field selection** instead of SELECT *
2. **Join minimization** - Only join necessary tables
3. **Index-friendly WHERE clauses** - Use indexed columns
4. **Limit result sets** - Always use LIMIT for listings
5. **Avoid N+1 queries** - Use JOINs or batch queries

## Next Steps / TODO

- [ ] Implement database connection pooling optimization
- [ ] Add database query performance monitoring
- [ ] Implement database backup and recovery procedures
- [ ] Add database migration rollback strategies
- [ ] Implement database read replicas for scaling
- [ ] Add database query caching at the PostgreSQL level
- [ ] Implement database partitioning for large tables
- [ ] Add database monitoring and alerting
- [ ] Implement database security hardening
- [ ] Add database query optimization tools
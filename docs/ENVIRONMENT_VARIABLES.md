# Environment Variable Configuration

This document describes how to set up and manage environment variables for the Cashback Deals project across different environments: local, staging, and production.

## Environment Variable Files

- `.env.local` - Local development environment variables (ignored by git)
- `.env.staging` - Staging environment variables
- `.env.production` - Production environment variables
- `.env.example` - Example environment variables file for reference

## Required Environment Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| NEXT_PUBLIC_SITE_URL         | Base URL of the site                          | https://www.example.com               |
| NEXT_PUBLIC_SUPABASE_URL     | Supabase project URL                          | https://xyzcompany.supabase.co       |
| NEXT_PUBLIC_SUPABASE_ANON_KEY| Supabase anonymous API key                    | public-anon-key                      |
| SUPABASE_SERVICE_ROLE_KEY    | Supabase service role key (server-side only) | service-role-key                     |
| EMAIL_SERVER                | SMTP server for sending emails                | smtp.gmail.com                       |
| EMAIL_PORT                  | SMTP server port                               | 587                                 |
| EMAIL_SECURE                | Use secure connection for SMTP (true/false)  | false                               |
| EMAIL_USER                  | SMTP username                                  | <EMAIL>                    |
| EMAIL_PASSWORD              | SMTP password                                  | password                           |
| EMAIL_FROM                  | Default "from" email address                   | "Cashback Deals" <<EMAIL>> |
| NEXT_PUBLIC_DEBUG_ENABLED   | Enable debug mode on frontend (true/false)    | true                               |
| NEXT_PUBLIC_DEBUG_LEVEL     | Debug level (e.g., error, warn, info, debug)  | error                              |
| DEBUG                      | Enable debug mode on backend (true/false)     | false                              |
| NODE_ENV                   | Node environment (development, production)    | development                       |
| NEXT_PUBLIC_TURNSTILE_SITE_KEY | Cloudflare Turnstile site key                  | 1x00000000000000000000AA (test key) |

## PR3: Sentry & IP Allowlist Configuration

### Sentry Environment Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| SENTRY_DSN                  | Sentry Data Source Name for error tracking   | https://<EMAIL>/project-id      |
| ENABLE_SENTRY               | Enable Sentry in production (true/false)     | true                                  |
| ENABLE_SENTRY_LOCAL         | Enable Sentry in development (true/false)    | false                                 |
| SENTRY_TRACES_SAMPLE_RATE   | Sentry trace sampling rate (0.0-1.0)         | 0.01 (1% in production)               |
| SENTRY_ENVIRONMENT          | Sentry environment name                       | production                            |

### IP Allowlist Environment Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| ENABLE_IP_ALLOWLIST         | Enable IP allowlist protection (true/false)  | false (default: disabled)             |
| IP_ALLOWLIST_CIDRS          | Comma-separated list of allowed CIDR ranges  | 10.0.0.0/8,**********/12,127.0.0.1/32 |
| IP_ALLOWLIST_LOG_VIOLATIONS | Log blocked IP attempts (true/false)         | true                                  |
| IP_ALLOWLIST_BLOCK_BY_DEFAULT | Block IPs not in allowlist (true/false)    | true                                  |
| IP_ALLOWLIST_INCLUDE_DEBUG  | Include debug info in error responses        | false                                 |

### Security Notes

- **Sentry DSN**: Keep this secret in production. Use environment-specific DSNs.
- **IP Allowlist**: Start with `ENABLE_IP_ALLOWLIST=false` for initial deployment.
- **CIDR Ranges**: Include office/VPN networks to prevent self-lockout.
- **AWS Amplify**: Sensitive variables are server-only unless prefixed with `AMPLIFY_`.

## Usage

1. Copy `.env.example` to `.env.local` for local development and fill in the appropriate values.
2. Create `.env.staging` and `.env.production` files with environment-specific values.
3. The project uses `src/env.mjs` to export environment variables with fallback defaults.
4. Access environment variables in the code via the `env` object imported from `src/env.mjs`.

## Notes

- Do not commit `.env.local`, `.env.staging`, or `.env.production` files to version control as they contain sensitive information.
- Use `.env.example` as a reference for required variables.
- Restart the development server after changing environment variables.

## Example

```bash
cp .env.example .env.local
# Edit .env.local with your local environment values
```

## Further Reading

- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
- [Supabase Environment Setup](https://supabase.com/docs/guides/with-nextjs)

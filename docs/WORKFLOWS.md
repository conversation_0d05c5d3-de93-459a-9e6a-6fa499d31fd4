# Development Workflows

*This file is auto-generated documentation for development workflows and local setup. Last updated: January 2025*

## Day-1 Developer Setup

### Prerequisites
- **Node.js 18+** - Use nvm for version management
- **npm 8+** - Package manager (comes with Node.js)
- **Git** - Version control
- **VS Code** - Recommended IDE
- **PostgreSQL** (optional) - For local Supabase development

### Step-by-Step Setup

#### 1. Repository Setup
```bash
# Clone the repository
git clone https://github.com/your-org/cashback-deals-v2.git
cd cashback-deals-v2

# Install dependencies
npm ci

# Verify installation
npm run build
```

#### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env.local

# Edit with your values
code .env.local
```

**Required Environment Variables:**
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Email Configuration (optional for local dev)
EMAIL_SERVER=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM="Cashback Deals" <<EMAIL>>

# Debug Configuration
NEXT_PUBLIC_DEBUG_ENABLED=true
NEXT_PUBLIC_DEBUG_LEVEL=info
DEBUG=false

# Security Configuration
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA
```

#### 3. Database Setup
```bash
# Install Supabase CLI (if not installed)
npm install -g @supabase/cli

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref your-project-ref

# Run migrations (if needed)
supabase db reset
```

#### 4. Start Development Server
```bash
# Start the development server
npm run dev

# Open in browser
open http://localhost:3000
```

#### 5. Verify Setup
- ✅ Homepage loads without errors
- ✅ Products page displays data
- ✅ Search functionality works
- ✅ No console errors in browser
- ✅ Database connection successful

## NPM Scripts Reference

### Core Development Scripts

| Script | Command | Description | Expected Output | Common Errors |
|--------|---------|-------------|----------------|---------------|
| `dev` | `next dev` | Start development server | Server starts on port 3000 | Port 3000 in use |
| `build` | `next build` | Build for production | Build artifacts in .next/ | TypeScript errors, missing env vars |
| `start` | `next start` | Start production server | Production server on port 3000 | Must run `build` first |
| `clean` | `rm -rf .next` | Remove build artifacts | .next directory deleted | Permission errors |
| `clean:build` | `npm run clean && npm run build` | Clean and rebuild | Fresh build artifacts | Same as `build` |
| `preview` | `npm run build && npm run start` | Preview production build | Production preview server | Build errors |

### Testing Scripts

| Script | Command | Description | Expected Output | Common Errors |
|--------|---------|-------------|----------------|---------------|
| `test` | `jest` | Run all tests | Test results summary | Test failures, missing dependencies |
| `test:watch` | `jest --watch` | Run tests in watch mode | Interactive test runner | File watchers limit |
| `test:coverage` | `jest --coverage` | Generate coverage report | Coverage HTML report | Low coverage warnings |
| `test:camelcase` | `jest --testPathPatterns=camelcase` | Test camelCase transformations | Transformation test results | Data transformation errors |
| `test:api` | `jest --testPathPatterns=api` | Test API routes | API test results | Supabase connection errors |
| `test:metadata` | `jest --testPathPatterns=metadata` | Test metadata utilities | Metadata test results | SEO validation errors |

### Quality Assurance Scripts

| Script | Command | Description | Expected Output | Common Errors |
|--------|---------|-------------|----------------|---------------|
| `lint` | `next lint` | Run ESLint | Linting results | ESLint rule violations |
| `lint:fix` | `next lint --fix` | Fix linting errors | Auto-fixed issues | Unfixable violations |
| `typecheck` | `tsc --noEmit` | Type checking | Type error summary | TypeScript errors |

### SEO & Performance Scripts

| Script | Command | Description | Expected Output | Common Errors |
|--------|---------|-------------|----------------|---------------|
| `seo:test` | `node scripts/seo-test.js` | Run SEO tests | SEO audit results | Missing meta tags |
| `seo:monitor` | `curl -s http://localhost:3000/api/seo/monitor` | Monitor SEO metrics | SEO metrics JSON | Server not running |
| `audit:seo` | `lighthouse --only=seo --output=json --quiet` | SEO audit | Lighthouse SEO score | Port not available |
| `audit:performance` | `lighthouse --only=performance --output=json --quiet` | Performance audit | Performance metrics | Server not running |
| `performance:check` | `curl -s http://localhost:3000/api/analytics/web-vitals` | Check Web Vitals | Web Vitals metrics | API not available |

### Build Analysis Scripts

| Script | Command | Description | Expected Output | Common Errors |
|--------|---------|-------------|----------------|---------------|
| `build:analyze` | `ANALYZE=true npm run build` | Analyze bundle size | Bundle analyzer report | Build failures |
| `validate:schema` | `node scripts/validate-structured-data.js` | Validate structured data | Schema validation results | Invalid schema |
| `generate:sitemap` | `node scripts/generate-sitemap.js` | Generate sitemap | Sitemap XML file | Database connection errors |

## Git Workflow

### Branch Naming Convention

| Branch Type | Pattern | Example | Description |
|-------------|---------|---------|-------------|
| **Feature** | `feature/description` | `feature/product-search` | New features |
| **Bug Fix** | `fix/description` | `fix/pagination-bug` | Bug fixes |
| **Hotfix** | `hotfix/description` | `hotfix/security-patch` | Production fixes |
| **Refactor** | `refactor/description` | `refactor/data-layer` | Code improvements |
| **Docs** | `docs/description` | `docs/api-documentation` | Documentation updates |
| **Security** | `security/description` | `security/xss-protection` | Security improvements |

### Commit Message Format

```
<type>(<scope>): <subject>

<body>

<footer>
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test additions/changes
- `chore`: Build/tooling changes
- `security`: Security improvements

**Examples:**
```bash
git commit -m "feat(search): add advanced search filters with price range"
git commit -m "fix(pagination): resolve URL state management issue"
git commit -m "security(api): implement rate limiting for search endpoints"
```

### Pull Request Process

#### 1. Before Creating PR
```bash
# Update your branch
git checkout main
git pull origin main
git checkout your-branch
git rebase main

# Run quality checks
npm run lint
npm run test
npm run build
```

#### 2. PR Checklist
- [ ] Code follows existing patterns
- [ ] Tests pass (`npm test`)
- [ ] Build succeeds (`npm run build`)
- [ ] ESLint passes (`npm run lint`)
- [ ] TypeScript compiles without errors
- [ ] No console errors in browser
- [ ] Security considerations addressed
- [ ] Documentation updated (if needed)

#### 3. PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests pass
- [ ] Manual testing completed

## Security
- [ ] Input validation implemented
- [ ] No sensitive data exposed
- [ ] Rate limiting considered
```

### Changelog Management

#### When to Update CHANGELOG.md
- New features
- Bug fixes
- Breaking changes
- Security fixes
- Performance improvements

#### Changelog Format
```markdown
## [Version] - YYYY-MM-DD

### Added
- New features

### Changed
- Changes to existing features

### Deprecated
- Soon-to-be removed features

### Removed
- Removed features

### Fixed
- Bug fixes

### Security
- Security improvements
```

## IDE Setup & Configuration

### VS Code Setup

#### Recommended Extensions
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.eslint",
    "ms-vscode.vscode-json",
    "ms-playwright.playwright",
    "supabase.supabase",
    "bierner.markdown-mermaid"
  ]
}
```

#### VS Code Settings
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "tailwindCSS.experimental.classRegex": [
    "cn\\(([^)]*)\\)"
  ]
}
```

#### Workspace Settings
```json
{
  "folders": [
    {
      "path": "."
    }
  ],
  "settings": {
    "typescript.preferences.includePackageJsonAutoImports": "on",
    "search.exclude": {
      "**/.next": true,
      "**/node_modules": true,
      "**/coverage": true
    }
  }
}
```

### ESLint Configuration

The project uses Next.js ESLint configuration with TypeScript support:

```javascript
// eslint.config.mjs
import { FlatCompat } from "@eslint/eslintrc";

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
];

export default eslintConfig;
```

### Prettier Configuration

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "tabWidth": 2,
  "useTabs": false,
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "always"
}
```

## Debugging & Development Tips

### Debug Configuration

#### Environment Variables for Debugging
```bash
# Frontend debugging
NEXT_PUBLIC_DEBUG_ENABLED=true
NEXT_PUBLIC_DEBUG_LEVEL=info

# Backend debugging
DEBUG=true
NODE_ENV=development
```

#### Debug Levels
- `error`: Only errors
- `warn`: Errors and warnings
- `info`: General information
- `debug`: Detailed debugging info

### Common Development Patterns

#### 1. Server vs Client Components
```typescript
// Server Component (default)
export default async function ProductsPage() {
  const products = await getProducts()
  return <ProductsContent products={products} />
}

// Client Component
'use client'
export function ProductsContent({ products }) {
  const [selected, setSelected] = useState(null)
  return <div>Interactive content</div>
}
```

#### 2. Data Fetching Pattern
```typescript
// Always use data layer functions
import { getProducts } from '@/lib/data'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'

const supabase = createServerSupabaseReadOnlyClient()
const products = await getProducts(supabase, filters, page, limit)
```

#### 3. URL State Management
```typescript
// Always use pagination hooks
const { currentPage, goToPage, updateFilters } = useProductsPagination()

// Update filters (resets to page 1)
updateFilters({ brand: 'samsung' })

// Navigate to specific page
goToPage(3)
```

### Performance Debugging

#### 1. Bundle Analysis
```bash
# Analyze bundle size
npm run build:analyze
```

#### 2. Performance Monitoring
```bash
# Check Web Vitals
npm run performance:check

# Run Lighthouse audit
npm run audit:performance
```

#### 3. Cache Debugging
```typescript
// Enable cache debugging
console.log('Cache hit for key:', cacheKey)

// Monitor cache performance
cacheMonitoring.logCacheStats(key, isHit)
```

### Database Debugging

#### 1. Query Performance
```sql
-- Enable query logging in development
SET log_statement = 'all';
SET log_min_duration_statement = 0;

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM products WHERE brand_id = $1;
```

#### 2. Connection Debugging
```bash
# Test Supabase connection
npm run test:api

# Check environment variables
node -e "console.log(process.env.NEXT_PUBLIC_SUPABASE_URL)"
```

## Common Issues & Solutions

### Build Issues

#### TypeScript Errors
```bash
# Check for type errors
npm run typecheck

# Common fixes
npm install @types/node @types/react @types/react-dom
```

#### Next.js Build Errors
```bash
# Clear cache and rebuild
npm run clean:build

# Check for missing dependencies
npm install
```

### Runtime Issues

#### Supabase Connection Errors
```bash
# Verify environment variables
echo $NEXT_PUBLIC_SUPABASE_URL
echo $SUPABASE_SERVICE_ROLE_KEY

# Test connection
npm run test:api
```

#### Port Conflicts
```bash
# Kill process on port 3000
lsof -ti:3000 | xargs kill -9

# Or use different port
npm run dev -- --port 3001
```

### Development Shortcuts

#### Quick Commands
```bash
# Full development cycle
npm run clean && npm install && npm run dev

# Quality check before commit
npm run lint && npm run test && npm run build

# Reset everything
rm -rf node_modules .next && npm install
```

#### Useful Aliases
```bash
# Add to ~/.bashrc or ~/.zshrc
alias nrd="npm run dev"
alias nrb="npm run build"
alias nrt="npm run test"
alias nrl="npm run lint"
```

## Next Steps / TODO

- [ ] Add database seeding scripts for local development
- [ ] Implement hot reloading for database schema changes
- [ ] Add development environment health checks
- [ ] Create automated development environment setup script
- [ ] Add development performance monitoring
- [ ] Implement development-specific error boundaries
- [ ] Add development logging enhancements
- [ ] Create debugging utilities for complex state management
- [ ] Add development-specific testing utilities
- [ ] Implement development database backup/restore tools
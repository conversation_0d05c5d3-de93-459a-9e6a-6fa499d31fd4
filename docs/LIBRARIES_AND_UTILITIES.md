# Libraries and Utilities Reference

*This file is auto-generated documentation for third-party packages and internal utilities. Last updated: January 2025*

## Overview

This document provides a comprehensive reference for all external dependencies and internal utilities used in the Cashback Deals platform, including usage patterns, configuration, and upgrade guidelines.

## External Dependencies

### Core Framework Dependencies

#### React & Next.js
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `next` | 15.1.4 | Web framework | `import { useRouter } from 'next/navigation'` | App Router only, no Pages Router |
| `react` | 19.0.0 | UI library | `import { useState } from 'react'` | Latest version with concurrent features |
| `react-dom` | 19.0.0 | React renderer | `import { createRoot } from 'react-dom/client'` | Server components by default |

**Configuration:**
```typescript
// next.config.js - Key settings
module.exports = {
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
  },
}
```

#### TypeScript
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `typescript` | 5.7.3 | Type system | `interface Product { id: string }` | Strict mode enabled |
| `@types/node` | 20.17.13 | Node.js types | `import { readFileSync } from 'fs'` | Required for server-side code |
| `@types/react` | 19.0.7 | React types | `React.FC<Props>` | Version must match React version |
| `@types/react-dom` | 19.0.3 | React DOM types | `ReactDOM.render()` | Required for SSR |

### Database & Backend

#### Supabase
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `@supabase/supabase-js` | 2.47.13 | Database client | `const { data, error } = await supabase.from('products').select()` | Use server-side client for security |
| `@supabase/ssr` | 0.5.2 | SSR support | `createServerClient(url, key, { cookies })` | Required for server components |

**Configuration:**
```typescript
// src/lib/supabase/server.ts
export function createServerSupabaseReadOnlyClient() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        get: () => undefined,
        set: () => {},
        remove: () => {},
      },
    }
  )
}
```

### State Management

#### TanStack Query (React Query)
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `@tanstack/react-query` | 5.81.5 | Server state | `const { data, isLoading } = useQuery({ queryKey: ['products'], queryFn: fetchProducts })` | Client-side only |
| `@tanstack/react-query-devtools` | 5.64.1 | Development tools | `<ReactQueryDevtools initialIsOpen={false} />` | Development only |

**Configuration:**
```typescript
// src/app/providers.tsx
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 60 * 1000, // 30 minutes
      gcTime: 60 * 60 * 1000,    // 60 minutes
      refetchOnWindowFocus: false,
      retry: 3,
    },
  },
})
```

### UI & Styling

#### Tailwind CSS
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `tailwindcss` | 3.4.1 | CSS framework | `className="flex items-center justify-center"` | JIT mode enabled |
| `tailwindcss-animate` | 1.0.7 | Animations | `className="animate-fade-in"` | Requires tailwind-merge |
| `tailwind-merge` | 2.6.0 | Class merging | `cn('base-class', conditionalClass)` | Essential for conditional styling |

**Configuration:**
```javascript
// tailwind.config.js
module.exports = {
  content: ["./src/**/*.{ts,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: "#3D5A80",
        secondary: "#98C1D9",
        accent: "#EE6C4D",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
```

#### Radix UI
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `@radix-ui/react-accordion` | 1.2.3 | Accordion component | `<Accordion type="single" collapsible>` | Unstyled, requires CSS |
| `@radix-ui/react-dialog` | 1.1.4 | Modal dialogs | `<Dialog><DialogTrigger>` | Portal-based |
| `@radix-ui/react-dropdown-menu` | 2.1.4 | Dropdown menus | `<DropdownMenu><DropdownMenuTrigger>` | Complex nesting |
| `@radix-ui/react-navigation-menu` | 1.2.3 | Navigation | `<NavigationMenu>` | Keyboard navigation |
| `@radix-ui/react-select` | 2.1.4 | Select inputs | `<Select><SelectTrigger>` | Custom styling needed |

#### shadcn/ui
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `@shadcn/ui` | 0.0.4 | Pre-built components | `import { Button } from "@/components/ui/button"` | Copy-paste components |
| `class-variance-authority` | 0.7.1 | Variant management | `const buttonVariants = cva("base", { variants: {} })` | Used by shadcn/ui |

### Icons & Graphics

#### Lucide React
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `lucide-react` | 0.471.2 | Icon library | `import { Search, ShoppingCart } from 'lucide-react'` | Tree-shaking enabled |

**Tree-shaking Configuration:**
```typescript
// Import specific icons only
import { Search } from 'lucide-react'

// ❌ Don't import entire library
import * as Icons from 'lucide-react'
```

#### Framer Motion
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `framer-motion` | 11.18.0 | Animation library | `<motion.div animate={{ x: 100 }}>` | Large bundle size |

**Optimization:**
```typescript
// Use dynamic imports for heavy animations
const AnimatedComponent = dynamic(() => import('./AnimatedComponent'), {
  loading: () => <div>Loading...</div>,
  ssr: false,
})
```

### Form Handling

#### React Hook Form
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `react-hook-form` | 7.54.2 | Form management | `const { register, handleSubmit, formState: { errors } } = useForm()` | Uncontrolled components |
| `@hookform/resolvers` | 3.10.0 | Form validation | `resolver: zodResolver(schema)` | Requires validation library |

### Validation & Security

#### Zod
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `zod` | 4.0.0-beta.20250505T195954 | Schema validation | `const schema = z.object({ name: z.string() })` | Beta version in use |

**Usage Pattern:**
```typescript
// src/lib/validation/schemas.ts
export const searchApiSchema = z.object({
  q: z.string().min(1).max(200),
  page: z.coerce.number().int().min(1).max(1000).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
})

// Usage in API routes
const validation = validateInput(searchApiSchema, params)
if (!validation.success) {
  return NextResponse.json({ error: 'Invalid input' }, { status: 400 })
}
```

#### Security Libraries
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `isomorphic-dompurify` | 2.26.0 | XSS prevention | `DOMPurify.sanitize(userInput)` | Works in Node.js and browser |
| `@marsidev/react-turnstile` | 1.1.0 | CAPTCHA | `<Turnstile siteKey={key} onVerify={handleVerify} />` | Cloudflare integration |

### Utilities

#### Date & Time
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `date-fns` | Not installed | Date manipulation | `import { format } from 'date-fns/format'` | Use specific imports |

#### Data Processing
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `papaparse` | 5.5.2 | CSV parsing | `Papa.parse(csvString, { header: true })` | Browser and Node.js |
| `clsx` | 2.1.1 | Conditional classes | `clsx('base', { active: isActive })` | Lightweight alternative to classnames |

### Development Tools

#### Testing
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `jest` | 30.0.4 | Test runner | `describe('Component', () => { test('renders', () => {}) })` | Configured for Next.js |
| `@testing-library/react` | 16.3.0 | React testing | `render(<Component />); expect(screen.getByText('text')).toBeInTheDocument()` | Testing Library philosophy |
| `@testing-library/jest-dom` | 6.6.3 | DOM matchers | `expect(element).toBeInTheDocument()` | Extends Jest matchers |
| `@playwright/test` | 1.53.1 | E2E testing | `test('page loads', async ({ page }) => { await page.goto('/') })` | Browser automation |

#### Build Tools
| Package | Version | Purpose | Usage Pattern | Caveats |
|---------|---------|---------|---------------|---------|
| `autoprefixer` | 10.4.20 | CSS prefixing | Automatic in PostCSS | Required for Tailwind |
| `postcss` | 8.x | CSS processing | PostCSS configuration | Required for Tailwind |

## Internal Utilities

### Data Layer (`src/lib/data/`)

#### Core Data Functions
| Module | Purpose | Key Functions | Usage Pattern |
|--------|---------|---------------|---------------|
| `products.ts` | Product data management | `getProducts`, `getProduct`, `getFeaturedProducts` | `const products = await getProducts(supabase, filters, page, limit)` |
| `brands.ts` | Brand data management | `getBrands`, `getBrand`, `getBrandWithDetails` | `const brand = await getBrand(supabase, id)` |
| `search.ts` | Search functionality | `searchProducts`, `getSearchSuggestions` | `const results = await searchProducts(supabase, filters)` |
| `types.ts` | Type definitions | Interfaces and types | `import { TransformedProduct } from '@/lib/data/types'` |

**Example Implementation:**
```typescript
// src/lib/data/products.ts
export async function getProducts(
  supabase: SupabaseClient,
  filters: ProductFilters = {},
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<TransformedProduct>> {
  const offset = (page - 1) * limit
  
  let query = supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url),
      category:category_id (id, name, slug)
    `, { count: 'exact' })
    .eq('status', 'active')
    .range(offset, offset + limit - 1)
  
  if (filters.brandId) {
    query = query.eq('brand_id', filters.brandId)
  }
  
  const { data, error, count } = await query
  
  if (error) throw new Error(`Failed to fetch products: ${error.message}`)
  
  return {
    data: data.map(transformProduct),
    pagination: {
      page,
      pageSize: limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit),
      hasNext: page < Math.ceil((count || 0) / limit),
      hasPrev: page > 1,
    },
  }
}
```

### Custom React Hooks (`src/hooks/`)

#### Pagination Hook
| Hook | Purpose | Returns | Usage Pattern |
|------|---------|---------|---------------|
| `usePagination` | URL-based pagination | `{ currentPage, goToPage, updateFilters }` | `const { goToPage } = usePagination()` |
| `useProductsPagination` | Products-specific pagination | Same as above | `const { currentPage, goToPage } = useProductsPagination()` |

**Implementation:**
```typescript
// src/hooks/usePagination.ts
export function usePagination({
  defaultPage = 1,
  pageSize = 20,
  basePath = ''
}: UsePaginationOptions = {}): UsePaginationReturn {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const currentPage = parseInt(searchParams?.get('page') || defaultPage.toString(), 10)
  
  const goToPage = useCallback((page: number) => {
    const params = new URLSearchParams(searchParams?.toString() || '')
    
    if (page === defaultPage) {
      params.delete('page')
    } else {
      params.set('page', page.toString())
    }
    
    const url = basePath + (params.toString() ? `?${params.toString()}` : '')
    router.push(url, { scroll: false })
  }, [router, searchParams, defaultPage, basePath])
  
  return { currentPage, pageSize, goToPage }
}
```

#### Performance Hooks
| Hook | Purpose | Returns | Usage Pattern |
|------|---------|---------|---------------|
| `useDebounce` | Debounce value changes | `debouncedValue` | `const debouncedQuery = useDebounce(query, 300)` |
| `usePerformanceOptimization` | Performance monitoring | `{ metrics, trackEvent }` | `const { trackEvent } = usePerformanceOptimization()` |

### Utility Functions (`src/lib/utils.ts`)

#### Core Utilities
| Function | Purpose | Usage Pattern | Notes |
|----------|---------|---------------|-------|
| `cn` | Merge CSS classes | `cn('base', { active: isActive })` | Uses tailwind-merge |
| `formatPrice` | Format currency | `formatPrice(999) // "£999"` | Handles null values |
| `formatDate` | Format dates | `formatDate(new Date())` | Consistent date formatting |

**Implementation:**
```typescript
// src/lib/utils.ts
import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: number | null | undefined): string {
  if (price === null || price === undefined) {
    return 'Price not available'
  }
  
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP',
  }).format(price)
}

export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  return new Intl.DateTimeFormat('en-GB', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(dateObj)
}
```

### Security Utilities (`src/lib/security/`)

#### Input Validation
| Function | Purpose | Usage Pattern | Notes |
|----------|---------|---------------|-------|
| `validateInput` | Zod schema validation | `const result = validateInput(schema, data)` | Returns success/error |
| `sanitizeHtml` | HTML sanitization | `const clean = sanitizeHtml(userInput)` | Uses DOMPurify |

#### Rate Limiting
| Function | Purpose | Usage Pattern | Notes |
|----------|---------|---------------|-------|
| `applyRateLimit` | Rate limit requests | `const response = applyRateLimit(request, config)` | Returns null if allowed |
| `cleanupRateLimits` | Clean expired entries | `cleanupRateLimits()` | Automatic cleanup |

### Cache Utilities (`src/lib/cache.ts`)

#### Caching Functions
| Function | Purpose | Usage Pattern | Notes |
|----------|---------|---------------|-------|
| `createCachedFunction` | Create cached version | `const cachedFn = createCachedFunction(fn, config)` | Uses Next.js cache |
| `cacheKeys` | Generate cache keys | `const key = cacheKeys.product(id)` | Consistent naming |

**Cache Configuration:**
```typescript
// src/lib/cache.ts
export const CACHE_DURATIONS = {
  SHORT: 300,    // 5 minutes
  MEDIUM: 1800,  // 30 minutes
  LONG: 3600,    // 1 hour
  EXTENDED: 86400 // 24 hours
} as const

export const CACHE_TAGS = {
  PRODUCTS: 'products',
  BRANDS: 'brands',
  SEARCH: 'search',
  FEATURED: 'featured',
} as const
```

### Image Utilities (`src/lib/imageUtils.ts`)

#### Image Processing
| Function | Purpose | Usage Pattern | Notes |
|----------|---------|---------------|-------|
| `optimizeImage` | Image optimization | `const optimized = optimizeImage(url, options)` | Next.js integration |
| `generateBlurDataURL` | Blur placeholder | `const blur = generateBlurDataURL(image)` | Performance optimization |

## Component Library (`src/components/`)

### UI Components (`src/components/ui/`)

#### Base Components
| Component | Purpose | Usage Pattern | Props |
|-----------|---------|---------------|-------|
| `Button` | Interactive buttons | `<Button variant="primary">Click me</Button>` | variant, size, disabled |
| `Pagination` | Page navigation | `<Pagination currentPage={1} totalPages={10} onPageChange={handleChange} />` | currentPage, totalPages, onPageChange |
| `OptimizedImage` | Optimized images | `<OptimizedImage src="/image.jpg" alt="Description" width={300} height={200} />` | src, alt, width, height, priority |

#### Form Components
| Component | Purpose | Usage Pattern | Props |
|-----------|---------|---------------|-------|
| `Input` | Text input | `<Input placeholder="Search..." value={value} onChange={handleChange} />` | value, onChange, placeholder |
| `Select` | Dropdown selection | `<Select value={selected} onValueChange={setSelected}>` | value, onValueChange, children |

### Layout Components (`src/components/layout/`)

#### Core Layout
| Component | Purpose | Usage Pattern | Props |
|-----------|---------|---------------|-------|
| `Header` | Site header | `<Header />` | None |
| `Footer` | Site footer | `<Footer />` | None |
| `SEO` | SEO metadata | `<SEO title="Page Title" description="Page description" />` | title, description, canonical |

### Page Components (`src/components/pages/`)

#### Client Components
| Component | Purpose | Usage Pattern | Props |
|-----------|---------|---------------|-------|
| `ProductsContent` | Products listing | `<ProductsContent products={products} />` | products, pagination |
| `SearchPageClient` | Search interface | `<SearchPageClient query={query} />` | query, results |

## Package Management

### Dependency Categories

#### Production Dependencies (35 packages)
```json
{
  "framework": ["next", "react", "react-dom"],
  "database": ["@supabase/supabase-js", "@supabase/ssr"],
  "ui": ["@radix-ui/*", "lucide-react", "framer-motion"],
  "styling": ["tailwindcss", "tailwind-merge", "clsx"],
  "forms": ["react-hook-form", "@hookform/resolvers"],
  "validation": ["zod"],
  "security": ["isomorphic-dompurify", "@marsidev/react-turnstile"],
  "state": ["@tanstack/react-query"],
  "utils": ["class-variance-authority", "papaparse"]
}
```

#### Development Dependencies (32 packages)
```json
{
  "build": ["typescript", "autoprefixer", "postcss"],
  "testing": ["jest", "@testing-library/react", "@playwright/test"],
  "types": ["@types/node", "@types/react", "@types/react-dom"],
  "tools": ["eslint", "babel-jest", "ts-jest"],
  "deployment": ["@cloudflare/next-on-pages", "supabase"]
}
```

### Unused Dependencies Check

**Potentially Unused:**
- `next-auth` (4.24.11) - Not currently used for authentication
- `next-i18next` (15.4.1) - Internationalization not implemented
- `next-seo` (6.6.0) - Using custom metadata utils instead
- `nodemailer` (6.10.0) - Email functionality not implemented
- `graphql` (16.10.0) - Not using GraphQL
- `critters` (0.0.23) - CSS inlining not configured

**Recommendation:** Remove unused dependencies to reduce bundle size.

### Upgrade Guidelines

#### Critical Dependencies (Check Monthly)
- `next` - Follow Next.js release schedule
- `react` - Major version updates require testing
- `@supabase/supabase-js` - Database client updates
- `typescript` - Language updates

#### UI Dependencies (Check Quarterly)
- `@radix-ui/*` - UI primitive updates
- `tailwindcss` - CSS framework updates
- `framer-motion` - Animation library updates

#### Development Dependencies (Check Bi-annually)
- Testing libraries (`jest`, `@testing-library/*`)
- Build tools (`postcss`, `autoprefixer`)
- Type definitions (`@types/*`)

### Upgrade Process

#### Pre-upgrade Checklist
1. **Check breaking changes** in release notes
2. **Update TypeScript** if required
3. **Run tests** before upgrade
4. **Check bundle impact** with `npm run build:analyze`
5. **Update documentation** if APIs change

#### Upgrade Commands
```bash
# Check for updates
npm outdated

# Update specific package
npm update package-name

# Update all dependencies (with caution)
npm update

# Major version upgrades
npm install package-name@latest

# Verify after upgrade
npm run build
npm run test
npm run lint
```

#### Post-upgrade Verification
```bash
# Build check
npm run build

# Test suite
npm run test

# Type checking
npm run typecheck

# Bundle analysis
npm run build:analyze

# Performance check
npm run audit:performance
```

## Security & License Compliance

### License Audit
| License Type | Packages | Compliance |
|-------------|----------|------------|
| MIT | 45 packages | ✅ Safe for commercial use |
| Apache-2.0 | 8 packages | ✅ Safe for commercial use |
| ISC | 12 packages | ✅ Safe for commercial use |
| BSD-3-Clause | 5 packages | ✅ Safe for commercial use |

### Security Scanning
```bash
# Run security audit
npm audit

# Fix vulnerabilities
npm audit fix

# Check for known vulnerabilities
npm audit --audit-level=high
```

### Bundle Impact Analysis

#### Large Dependencies (>100KB)
1. `@supabase/supabase-js` (~400KB) - Database client
2. `framer-motion` (~200KB) - Animation library
3. `@radix-ui/react-*` (~150KB combined) - UI primitives
4. `react-hook-form` (~120KB) - Form management

#### Tree-shaking Opportunities
```typescript
// ✅ Good - Import specific functions
import { Search } from 'lucide-react'
import { format } from 'date-fns/format'

// ❌ Bad - Imports entire library
import * as Icons from 'lucide-react'
import * as DateFns from 'date-fns'
```

## Best Practices

### Adding New Dependencies

#### Evaluation Criteria
1. **Bundle size impact** - Check with `bundlephobia.com`
2. **Maintenance status** - Active development and support
3. **TypeScript support** - Built-in or @types availability
4. **Security** - No known vulnerabilities
5. **License compatibility** - MIT/Apache-2.0 preferred

#### Installation Process
```bash
# Install with exact version
npm install --save-exact package-name

# Update documentation
# Add to this file under appropriate section

# Test thoroughly
npm run test
npm run build
```

### Dependency Hygiene

#### Regular Maintenance
- Monthly: Update critical dependencies
- Quarterly: Review all dependencies
- Bi-annually: Remove unused dependencies
- Annually: Major version upgrades

#### Monitoring
- Enable Dependabot for security updates
- Monitor bundle size changes in CI
- Track performance impact of upgrades
- Document breaking changes

## Next Steps / TODO

- [ ] Implement automated dependency vulnerability scanning
- [ ] Add bundle size monitoring in CI/CD
- [ ] Create dependency update automation
- [ ] Add performance impact monitoring for upgrades
- [ ] Implement license compliance automation
- [ ] Add tree-shaking analysis tools
- [ ] Create dependency migration guides
- [ ] Add deprecation warnings for internal utilities
- [ ] Implement automated documentation updates
- [ ] Add dependency usage analytics
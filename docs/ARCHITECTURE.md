# Architecture Documentation

*This file is auto-generated documentation for the Cashback Deals platform architecture. Last updated: January 2025*

## Overview

The Cashback Deals platform is a modern e-commerce application built with Next.js 15 and React 19, featuring a hybrid rendering strategy, centralized data layer, and comprehensive security implementation.

## Technology Stack

### Core Framework
- **Next.js 15.1.4** - App Router with hybrid SSR/SSG
- **React 19.0.0** - Latest React with server components
- **TypeScript 5.7.3** - Full type safety across the application
- **Node.js 18+** - Runtime environment

### Database & Backend
- **Supabase** - PostgreSQL database with real-time features
- **Supabase Auth** - Authentication and user management
- **Row Level Security (RLS)** - Database-level security policies

### Frontend & UI
- **Tailwind CSS 3.4.1** - Utility-first CSS framework
- **shadcn/ui** - Reusable component library
- **Framer Motion** - Animation library
- **Lucide React** - Icon library

### State Management
- **React Query (TanStack Query)** - Server state management
- **URL State** - Client-side routing state via Next.js router
- **React Context** - Global app state (minimal usage)

### Security & Validation
- **Zod** - Schema validation and type safety
- **isomorphic-dompurify** - XSS protection
- **Rate Limiting** - API protection middleware
- **Cloudflare Turnstile** - Bot protection

## Rendering Strategy

### Hybrid Approach
The application uses a strategic mix of rendering patterns:

```mermaid
graph TB
    A[User Request] --> B{Route Type}
    B -->|Static Pages| C[SSG - Pre-generated]
    B -->|Dynamic Pages| D[SSR - Server Rendered]
    B -->|Interactive Components| E[Client Components]
    
    C --> F[CDN Cache]
    D --> G[Server Cache]
    E --> H[React Query Cache]
    
    F --> I[User]
    G --> I
    H --> I
```

### Rendering Patterns by Route Type

| Route Pattern | Rendering Strategy | Cache Strategy | Reason |
|---------------|-------------------|----------------|---------|
| `/` (Homepage) | SSG + ISR | 1 hour revalidate | Static content, good SEO |
| `/products` | SSR | Server-side cache | Dynamic filtering, pagination |
| `/products/[id]` | SSG + ISR | 30 min revalidate | Product detail pages, SEO critical |
| `/brands` | SSR | Server-side cache | Alphabetical navigation, filters |
| `/brands/[id]` | SSG + ISR | 1 hour revalidate | Brand detail pages |
| `/search` | SSR | Short cache | Dynamic search results |
| `/api/*` | Server Functions | Tiered caching | API endpoints |

## Data Flow Architecture

### High-Level Data Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as React Component
    participant H as usePagination Hook
    participant D as Data Layer
    participant S as Supabase
    participant Cache as Cache Layer
    
    U->>C: User interaction (page change)
    C->>H: Call goToPage(2)
    H->>H: Update URL params
    H->>D: Fetch data with new params
    D->>Cache: Check cache first
    Cache-->>D: Cache miss/hit
    D->>S: Query database (if cache miss)
    S-->>D: Return data
    D->>Cache: Store in cache
    D-->>C: Return transformed data
    C->>U: Re-render with new data
```

### Detailed Component Data Flow

```typescript
// Example: Products page data flow
User clicks "Next Page" 
  → ProductsContent component
  → useProductsPagination hook
  → goToPage(2) function
  → URL updates to /products?page=2
  → Server component re-renders
  → getProducts(supabase, filters, page=2)
  → Check cache with key "products:page=2"
  → If cache miss: Query Supabase
  → Transform data to TransformedProduct[]
  → Return to component
  → Re-render with new data
```

## Key Architectural Patterns

### 1. Centralized Data Layer

The application uses a centralized data layer pattern located in `src/lib/data/`:

```typescript
// src/lib/data/index.ts - Single export point
export {
  getProducts,
  getProduct,
  getFeaturedProducts,
  // ... other data functions
} from './products'

// Usage in server components
import { getProducts } from '@/lib/data'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'

const supabase = createServerSupabaseReadOnlyClient()
const products = await getProducts(supabase, filters, page, limit)
```

**Benefits:**
- Consistent data fetching patterns
- Centralized caching strategy
- Type safety across the application
- Easy testing and mocking

### 2. URL State Management

The `usePagination` hook family serves as the single source of truth for URL state:

```typescript
// src/hooks/usePagination.ts
export function usePagination({
  defaultPage = 1,
  pageSize = 20,
  basePath = ''
}: UsePaginationOptions = {}): UsePaginationReturn {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // URL state management logic
  const goToPage = useCallback((page: number) => {
    const params = new URLSearchParams(searchParams?.toString() || '')
    // Clean URL logic - remove page=1 from URL
    if (page === defaultPage) {
      params.delete('page')
    } else {
      params.set('page', page.toString())
    }
    router.push(`${basePath}?${params.toString()}`, { scroll: false })
  }, [router, searchParams, defaultPage, basePath])
  
  return { currentPage, goToPage, updateFilters, clearFilters }
}
```

**Page-specific implementations:**
- `useProductsPagination()` - Products listing
- `useBrandsPagination()` - Brands listing  
- `useRetailersPagination()` - Retailers listing
- `usePromotionsPagination()` - Promotions listing

### 3. Server vs Client Components Strategy

**Server Components (default):**
- Page components (`page.tsx`)
- Layout components
- Data fetching components
- SEO components

**Client Components (`'use client'`):**
- Interactive components with state
- Components using hooks
- Form components
- Pagination components

```typescript
// Server Component - Products page
export default async function ProductsPage({
  searchParams
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const supabase = createServerSupabaseReadOnlyClient()
  const products = await getProducts(supabase, filters, page, limit)
  
  return (
    <div>
      <ProductsContent products={products} />
    </div>
  )
}

// Client Component - Interactive content
'use client'
export function ProductsContent({ products }: { products: TransformedProduct[] }) {
  const { currentPage, goToPage } = useProductsPagination()
  
  return (
    <div>
      <ProductGrid products={products} />
      <Pagination currentPage={currentPage} onPageChange={goToPage} />
    </div>
  )
}
```

### 4. Feature-Sliced Architecture

The application follows a feature-sliced architecture:

```
src/
├── app/                    # Next.js App Router
│   ├── (pages)/           # Route groups
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # Reusable components
│   ├── ui/               # Base UI components (shadcn/ui)
│   ├── layout/           # Layout components
│   ├── pages/            # Page-specific components
│   └── search/           # Feature-specific components
├── lib/                   # Core utilities
│   ├── data/             # Data layer
│   ├── validation/       # Zod schemas
│   └── security/         # Security utilities
├── hooks/                # Custom React hooks
├── types/                # TypeScript definitions
└── utils/                # Helper functions
```

## Thread Safety & Concurrency

### Supabase Client Management

```typescript
// src/lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'

export function createServerSupabaseReadOnlyClient() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        get: () => undefined,
        set: () => {},
        remove: () => {},
      },
    }
  )
}
```

**Thread Safety Considerations:**
- Each request gets its own Supabase client instance
- Connection pooling handled by Supabase
- No shared state between requests
- Stateless server components

### Caching Strategy

```typescript
// src/lib/cache.ts
export const CACHE_DURATIONS = {
  SHORT: 300,    // 5 minutes - frequently changing data
  MEDIUM: 1800,  // 30 minutes - moderately stable data
  LONG: 3600,    // 1 hour - stable data
  EXTENDED: 86400 // 24 hours - very stable data
}

export function createCachedFunction<T>(
  fn: T,
  config: CacheConfig
): T {
  return unstable_cache(fn, [config.key], {
    revalidate: config.revalidate,
    tags: config.tags,
  }) as T
}
```

## Search + Pagination Flow Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant SC as SearchComponent
    participant PH as usePagination
    participant API as /api/search
    participant DL as Data Layer
    participant DB as Supabase
    participant Cache as Cache Layer
    
    U->>SC: Enter search query "laptop"
    SC->>PH: updateFilters({query: "laptop"})
    PH->>PH: Reset page to 1
    PH->>API: GET /api/search?q=laptop&page=1
    API->>API: Rate limit check
    API->>API: Validate input (Zod)
    API->>DL: searchProducts(supabase, filters, page)
    DL->>Cache: Check cache key "search:laptop:page=1"
    Cache-->>DL: Cache miss
    DL->>DB: Full-text search query
    DB-->>DL: Raw product data
    DL->>DL: Transform to TransformedProduct[]
    DL->>Cache: Store with 5min TTL
    DL-->>API: Return search results
    API-->>SC: JSON response
    SC->>U: Display results
    
    U->>SC: Click "Next Page"
    SC->>PH: goToPage(2)
    PH->>API: GET /api/search?q=laptop&page=2
    API->>DL: searchProducts(supabase, filters, page=2)
    DL->>Cache: Check cache key "search:laptop:page=2"
    Cache-->>DL: Cache hit
    DL-->>API: Return cached results
    API-->>SC: JSON response
    SC->>U: Display page 2 results
```

## Micro-Decisions & Trade-offs

### 1. URL State vs React State

**Decision:** Use URL state for pagination and filters
**Reasoning:** 
- SEO benefits (crawlable pages)
- Shareable URLs
- Browser back/forward support
- Bookmarkable states

### 2. Server vs Client Components

**Decision:** Server components by default, client components for interactivity
**Reasoning:**
- Better performance (less JavaScript)
- Improved SEO
- Reduced bundle size
- Better initial page load

### 3. React Query vs Direct Fetch

**Decision:** React Query for client-side data fetching, direct fetch for server components
**Reasoning:**
- React Query: Caching, error handling, loading states
- Direct fetch: Better SSR performance, no hydration issues

### 4. Supabase RLS vs Application-Level Security

**Decision:** Supabase RLS for data security, application-level for business logic
**Reasoning:**
- Database-level security cannot be bypassed
- Better performance with database-level filtering
- Centralized security policies

## Performance Considerations

### Bundle Optimization
- **Tree shaking**: Enabled for all packages
- **Code splitting**: Automatic route-based splitting
- **Dynamic imports**: For heavy components

### Database Optimization
- **Indexes**: Full-text search, foreign keys, composite indexes
- **Connection pooling**: Handled by Supabase
- **Query optimization**: Specific field selection, joins minimization

### Caching Strategy
- **4-tier cache system**: SHORT/MEDIUM/LONG/EXTENDED
- **Cache invalidation**: Tag-based invalidation
- **Stale-while-revalidate**: Background cache refresh

## Security Architecture

### Defense in Depth
1. **Input Validation** - Zod schemas for all inputs
2. **Rate Limiting** - API endpoint protection
3. **Authentication** - Supabase Auth with JWTs
4. **Authorization** - Row Level Security (RLS)
5. **XSS Protection** - DOMPurify sanitization
6. **CSRF Protection** - SameSite cookies
7. **HTTP Headers** - CSP, HSTS, etc.

## Next Steps / TODO

- [ ] Implement Redis for distributed caching in production
- [ ] Add real-time updates with Supabase subscriptions
- [ ] Implement advanced search with Elasticsearch
- [ ] Add GraphQL layer for complex queries
- [ ] Implement micro-frontends for scalability
- [ ] Add comprehensive error boundary system
- [ ] Implement progressive web app features
- [ ] Add internationalization (i18n) support
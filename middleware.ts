/**
 * Next.js Middleware for Cashback Deals
 * 
 * Implements IP allowlist protection for API routes with feature flags
 * and comprehensive logging as per PR3 specifications.
 * 
 * <AUTHOR> Agent
 * @date 2025-07-13
 */

import { NextRequest, NextResponse } from 'next/server';
import { applyIPAllowlist } from './src/lib/security/ip-allowlist';

/**
 * Middleware configuration - specify which routes to protect
 */
export const config = {
  matcher: [
    // Protect all API routes except health checks and public endpoints
    '/api/((?!health|public).*)',
    
    // Protect admin routes
    '/admin/:path*',
    
    // Protect sensitive pages
    '/dashboard/:path*',
  ],
};

/**
 * Main middleware function
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip IP allowlist for health check endpoints
  if (pathname.startsWith('/api/health')) {
    return NextResponse.next();
  }
  
  // Skip IP allowlist for public API endpoints
  if (pathname.startsWith('/api/public')) {
    return NextResponse.next();
  }
  
  // Apply IP allowlist protection
  const ipAllowlistResponse = applyIPAllowlist(request);
  if (ipAllowlistResponse) {
    // Request was blocked by IP allowlist
    return ipAllowlistResponse;
  }
  
  // Continue to next middleware or route handler
  return NextResponse.next();
}

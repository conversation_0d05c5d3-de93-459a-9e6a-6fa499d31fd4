/** @type {import('next').NextConfig} */

// Environment detection for security headers
const isProduction = process.env.NODE_ENV === 'production';
const isStaging = process.env.VERCEL_ENV === 'preview' || process.env.NODE_ENV === 'staging';
const isDevelopment = process.env.NODE_ENV === 'development';

// Security Headers Configuration
const getSecurityHeaders = () => {
  // Content Security Policy configuration
  const cspDirectives = {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "https://challenges.cloudflare.com",
      // Allow unsafe-eval and unsafe-inline only in development for Next.js dev tools and styles
      ...(isDevelopment ? ["'unsafe-eval'", "'unsafe-inline'"] : [])
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'" // Required for Tailwind CSS and styled-jsx
    ],
    'img-src': [
      "'self'",
      "data:",
      "https://*.supabase.co",
      "https://placehold.co",
      "https://via.placeholder.com",
      "https://dummyimage.com",
      "https://*.amazonaws.com",
      "https://*.cloudfront.net",
      "https://images.samsung.com",
      "https://supabase.com",
      ...(isDevelopment ? ["https://example.com"] : [])
    ],
    'connect-src': [
      "'self'",
      "https://*.supabase.co",
      "wss://*.supabase.co",
      "https://images.samsung.com",
      "https://*.ingest.de.sentry.io",
      "https://o4509639007272960.ingest.de.sentry.io"
    ],
    'font-src': ["'self'", "data:"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-src': ["https://challenges.cloudflare.com"], // <-- ADD THIS LINE - By adding this rule, you are telling the browser that it is safe to embed content from https://challenges.cloudflare.com, which will allow the Turnstile CAPTCHA widget to render correctly and resolve the console error.
    'worker-src': ["'self'", "blob:"], // Allow web workers and blob URLs for Sentry replay
    // 'frame-ancestors': ["'none'"], // Replaces X-Frame-Options
    'upgrade-insecure-requests': []
  };

  // Convert CSP object to string
  const cspString = Object.entries(cspDirectives)
    .map(([directive, sources]) => {
      if (sources.length === 0) return directive;
      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');

  // Base security headers for all environments
  const baseHeaders = [
    {
      key: 'X-Content-Type-Options',
      value: 'nosniff'
    },
    {
      key: 'Referrer-Policy',
      value: 'strict-origin-when-cross-origin'
    },
    {
      key: 'Permissions-Policy',
      value: 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), browsing-topics=()'
    },
    {
      key: 'Content-Security-Policy',
      value: cspString
    },
    {
      key: 'X-DNS-Prefetch-Control',
      value: 'on'
    }
  ];

  // Add HSTS only in production and staging
  if (isProduction || isStaging) {
    baseHeaders.push({
      key: 'Strict-Transport-Security',
      value: 'max-age=63072000; includeSubDomains; preload'
    });
  }

  return baseHeaders;
};

const nextConfig = {
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'placehold.co', // Primary placeholder service
            },
            {
                protocol: 'https',
                hostname: 'via.placeholder.com', // Legacy placeholder service (for compatibility)
            },
            {
                protocol: 'https',
                hostname: 'dummyimage.com', // Alternative placeholder service (for compatibility)
            },
            {
                protocol: 'https',
                hostname: '*.amazonaws.com', // AWS images
            },
            {
                protocol: 'https',
                hostname: '*.cloudfront.net', // CloudFront images
            },
            {
                protocol: 'https',
                hostname: '*.supabase.co', // Supabase images
            },
            {
                protocol: 'https',
                hostname: 'rkjcixumtesncutclmxm.supabase.co', // Specific Supabase instance
            },
            {
                protocol: 'https',
                hostname: 'rkjcixumtesncutclmxm.supabase.co', // Allow images from this specific path
                pathname: '/storage/v1/object/sign/**', // Allow all images from the sign endpoint
            },
            {
                protocol: 'https',
                hostname: 'images.samsung.com', // Samsung images
            },
            {
                protocol: 'https',
                hostname: 'supabase.com', // Supabase logo and assets
            },
            {
                protocol: 'https',
                hostname: 'example.com', // Example/test images (for development/testing)
            }
        ],
        // Enhanced image optimization configuration
        formats: ['image/webp', 'image/avif'], // Modern formats with fallbacks
        deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840], // Responsive breakpoints
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384], // Icon and thumbnail sizes
        minimumCacheTTL: 60 * 60 * 24 * 365, // Cache for 1 year
        dangerouslyAllowSVG: true, // Allow SVG images (for logos)
        contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;", // SVG security
        unoptimized: false, // Enable optimization for better performance
        // Deprecated domains property (use remotePatterns instead)
        domains: []
    },
    eslint: {
        ignoreDuringBuilds: true // Ignore ESLint warnings during builds
    },
    poweredByHeader: false, // Disable the X-Powered-By header
    compress: true, // Enable gzip compression
    productionBrowserSourceMaps: false, // Disable source maps in production

    // Enhanced performance optimizations
    // Note: swcMinify is enabled by default in Next.js 13+

    // Experimental features for better performance
    experimental: {
        optimizeCss: true, // Optimize CSS loading
        optimizePackageImports: ['lucide-react', 'framer-motion'], // Tree shake large packages
    },

    // Turbopack configuration (stable in Next.js 15+)
    turbopack: {
        rules: {
            '*.svg': {
                loaders: ['@svgr/webpack'],
                as: '*.js',
            },
        },
    },

    // Note: API route configuration is handled in individual route files
    // Response limits and body parser settings are configured per route

    // Server configuration
    serverRuntimeConfig: {
        // Server-side timeout configurations
        apiTimeout: 30000, // 30 seconds for API routes
        imageTimeout: 10000, // 10 seconds for image processing
    },

    // Public runtime configuration
    publicRuntimeConfig: {
        // Client-side timeout configurations
        fetchTimeout: 8000, // 8 seconds for client-side requests
        imageLoadTimeout: 10000, // 10 seconds for image loading
    },

    // Webpack optimizations
    webpack: (config, { dev, isServer }) => {
        // Production optimizations
        if (!dev && !isServer) {
            // Enable aggressive splitting for better caching
            config.optimization.splitChunks = {
                chunks: 'all',
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                        priority: 10,
                    },
                    common: {
                        name: 'common',
                        minChunks: 2,
                        chunks: 'all',
                        priority: 5,
                        reuseExistingChunk: true,
                    },
                },
            };
        }

        return config;
    },

    compiler: {
        removeConsole: process.env.NODE_ENV === 'production', // Remove console logs in production
    },

    // Headers for security, caching and SEO
    async headers() {
        const securityHeaders = getSecurityHeaders();

        return [
            // Apply security headers to all routes
            {
                source: '/(.*)',
                headers: securityHeaders,
            },
            // API routes caching
            {
                source: '/api/(.*)',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, s-maxage=1800, stale-while-revalidate=3600'
                    },
                ],
            },
            // Test routes - prevent indexing
            {
                source: '/test-api-routes',
                headers: [
                    {
                        key: 'X-Robots-Tag',
                        value: 'noindex, nofollow'
                    }
                ],
            },
            {
                source: '/test-data-layer',
                headers: [
                    {
                        key: 'X-Robots-Tag',
                        value: 'noindex, nofollow'
                    }
                ],
            },
            {
                source: '/test-featured-debug',
                headers: [
                    {
                        key: 'X-Robots-Tag',
                        value: 'noindex, nofollow'
                    }
                ],
            },
            {
                source: '/test-data-simple',
                headers: [
                    {
                        key: 'X-Robots-Tag',
                        value: 'noindex, nofollow'
                    }
                ],
            }
        ];
    },
}

module.exports = nextConfig;


  // Injected content via Sentry wizard below

  const { withSentryConfig } = require("@sentry/nextjs");

  module.exports = withSentryConfig(
    module.exports,
    {
      // For all available options, see:
      // https://www.npmjs.com/package/@sentry/webpack-plugin#options

      org: "sanjmirch",
      project: "javascript-nextjs",

      // Only print logs for uploading source maps in CI
      silent: !process.env.CI,

      // For all available options, see:
      // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

      // Upload a larger set of source maps for prettier stack traces (increases build time)
      widenClientFileUpload: true,

      // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
      // This can increase your server load as well as your hosting bill.
      // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
      // side errors will fail.
      // tunnelRoute: "/monitoring",

      // Automatically tree-shake Sentry logger statements to reduce bundle size
      disableLogger: true,

      // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
      // See the following for more information:
      // https://docs.sentry.io/product/crons/
      // https://vercel.com/docs/cron-jobs
      automaticVercelMonitors: true,
    }
  );

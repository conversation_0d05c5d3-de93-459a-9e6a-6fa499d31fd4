import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import type { TransformedProduct } from '@/lib/data/types';

interface FeaturedProductCardProps {
  product: TransformedProduct;
}

export function FeaturedProductCard({ product }: FeaturedProductCardProps) {
  const [imageError, setImageError] = useState(false);
  
  const getImageSource = (): string => {
    // Try to get image from specifications first (usually higher quality)
    if (product.specifications?.product_image_1) {
      return product.specifications.product_image_1;
    }
    
    // Get first image from images array (which is string[])
    if (product.images?.[0]) {
      return product.images[0];
    }
    
    // Fallback to a generic placeholder
    return 'https://placehold.co/600x600/f1f5f9/64748b.png';
  };

  return (
    <Link href={`/products/${product.slug || product.id}`} className="block h-full">
      <motion.div
        whileHover={{ y: -2 }}
        className="group relative overflow-hidden rounded-lg border bg-white shadow-sm transition-all hover:shadow-md h-full flex flex-col"
      >
        <div className="relative aspect-[4/3] w-full overflow-hidden bg-gray-50" style={{ position: 'relative' }}>
          <Image
            src={getImageSource()}
            alt={product.name}
            fill
            className="object-contain p-2 transition-transform duration-300 group-hover:scale-105"
            onError={() => setImageError(true)}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
        <div className="p-4 flex-1 flex flex-col">
          <h3 className="text-sm font-semibold text-primary mb-2 line-clamp-2">
            {product.brand?.name || 'Unknown Brand'} - {product.name}
          </h3>
          <div className="mt-auto">
            {/* Temporarily commented out price display - keeping the code but not rendering
            <div className="space-y-2">
              <p className="text-lg font-bold text-primary">
                {product.minPrice !== null ? `£${Number(product.minPrice).toFixed(2)}` : 'Price not available'}
              </p>
            </div>
            */}
            <p className="text-sm text-accent">
              Up to £{(product.cashbackAmount || 0).toFixed(2)} Cashback
            </p>
            {product.retailerOffers && product.retailerOffers.length > 0 && (
              <p className="text-sm text-foreground/70">
                {product.retailerOffers.length} retailer{product.retailerOffers.length !== 1 ? 's' : ''} available
              </p>
            )}
          </div>
        </div>
      </motion.div>
    </Link>
  );
}

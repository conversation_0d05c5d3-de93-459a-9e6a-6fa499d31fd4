// src/components/layout/header.tsx
// This is the header component that will be used in the layout component
// It includes the mobile menu and desktop navigation

'use client'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { Menu, X } from 'lucide-react'
import { useState } from 'react'
import React from 'react'
import { useSearchParams, usePathname } from 'next/navigation'
import { SearchBar } from '@/components/search/SearchBar'
import { getSearchInputDisplayValue } from '@/lib/utils/display-names'

// SEO is now handled at the page/route level, so we don't need props for it here
export const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const searchParams = useSearchParams()
  const pathname = usePathname()
  
  // Get display value - handle product pages specially
  let displayValue = getSearchInputDisplayValue(searchParams)
  
  // For product pages, check if we have a product name in sessionStorage
  if (pathname.startsWith('/products/') && !displayValue) {
    try {
      const productName = sessionStorage.getItem('lastClickedProduct')
      if (productName) {
        displayValue = productName
      }
    } catch (error) {
      // SessionStorage not available or error - ignore
    }
  }

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="sticky top-0 z-50 w-full border-b bg-white/80 backdrop-blur-sm"
    >
      <div className="container flex h-16 items-center justify-between gap-4">
        <div className="text-primary font-bold text-lg md:text-xl flex-shrink-0">
          <Link href="/">RebateRay</Link>
        </div>

        {/* Search Bar */}
        <div className="flex-1 max-w-xl hidden md:block">
          <SearchBar initialValue={displayValue} />
        </div>

        {/* Mobile menu button */}
        <button
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className="p-2 md:hidden"
          aria-label={isMenuOpen ? "Close menu" : "Open menu"}
        >
          {isMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </button>

        {/* Desktop navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          <div>
            <Link href="/brands" className="text-sm font-medium transition-colors hover:text-primary">
              Brands
            </Link>
          </div>
          <div>
            <Link href="/products" className="text-sm font-medium transition-colors hover:text-primary">
              Products
            </Link>
          </div>
        </nav>
      </div>

      {/* Mobile navigation */}
      <div
        className={`md:hidden overflow-hidden bg-white border-t transition-all duration-200 ease-in-out ${isMenuOpen ? 'max-h-96' : 'max-h-0'
          }`}
      >
        <div className="container py-4 space-y-4">
          {/* Mobile Search */}
          <div className="px-4">
            <SearchBar initialValue={displayValue} />
          </div>
          <Link
            href="/brands"
            className="block text-sm font-medium px-4 py-2 hover:bg-primary/5 rounded-lg"
            onClick={() => setIsMenuOpen(false)}
          >
            Brands
          </Link>
          <Link
            href="/products"
            className="block text-sm font-medium px-4 py-2 hover:bg-primary/5 rounded-lg"
            onClick={() => setIsMenuOpen(false)}
          >
            Products
          </Link>
        </div>
      </div>
    </motion.header>
  )
}
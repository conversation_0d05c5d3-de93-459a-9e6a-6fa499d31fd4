/**
 * Utility functions for converting URL slugs to display names
 */

/**
 * Converts a slug to a proper display name
 * @param slug - The URL slug (e.g., "samsung", "home-garden")
 * @returns The display name (e.g., "Samsung", "Home & Garden")
 */
export function slugToDisplayName(slug: string): string {
  if (!slug) return '';
  
  // Handle special cases
  const specialCases: Record<string, string> = {
    'samsung': 'Samsung',
    'samsung-uk': 'Samsung UK',
    'sony': 'Sony',
    'apple': 'Apple',
    'lg': 'LG',
    'electronics': 'Electronics',
    'home-garden': 'Home & Garden',
    'home-appliances': 'Home Appliances',
    'fashion': 'Fashion',
    'sports': 'Sports',
    'books': 'Books',
    'automotive': 'Automotive',
    'health-beauty': 'Health & Beauty',
    'toys-games': 'Toys & Games',
    'pet-supplies': 'Pet Supplies',
  };
  
  // Check for special cases first
  if (specialCases[slug]) {
    return specialCases[slug];
  }
  
  // Convert slug to title case
  return slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

/**
 * Determines what to display in the search input based on URL parameters
 * @param searchParams - URLSearchParams object
 * @returns The text to display in the search input
 */
export function getSearchInputDisplayValue(searchParams: URLSearchParams): string {
  const q = searchParams.get('q');
  const brand = searchParams.get('brand');
  const category = searchParams.get('category');
  
  // Priority order: q > brand > category
  if (q) {
    return q; // Manual search or query-phrase suggestion
  } else if (brand) {
    return slugToDisplayName(brand); // Brand suggestion was clicked
  } else if (category) {
    return slugToDisplayName(category); // Category suggestion was clicked
  }
  
  return ''; // No search parameters
}
/**
 * Server-side Supabase client configuration
 * 
 * This module provides secure server-side access to Supabase.
 * It offers two types of clients:
 * 1. A read-only client that uses the `anon` key and respects RLS policies.
 * 2. An admin client that uses the `service_role` key for administrative tasks.
 */

import { createClient } from '@supabase/supabase-js'
import { Database } from '../../types/supabase'
import { TIMEOUT_CONFIG } from '../timeoutConfig'

type SupabaseClient = ReturnType<typeof createClient<Database>>

/**
 * Creates a server-side Supabase client with service role privileges.
 * This client bypasses RLS and should only be used for essential administrative tasks.
 * 
 * @returns Supabase client with full database access.
 * @throws Error if required environment variables are missing.
 */
export function createServerSupabaseClient(): SupabaseClient {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error(
      'Missing required Supabase environment variables. ' +
      'Ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.'
    )
  }

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false
    },
    global: {
      headers: {
        'User-Agent': 'CashbackDeals/1.0 (ServerAdmin)',
      },
    },
    db: {
      schema: 'public',
    },
    realtime: {
      timeout: TIMEOUT_CONFIG.DATABASE.CONNECTION,
    }
  })
}

/**
 * Creates a server-side Supabase client for read-only operations.
 * This client uses the `anon` key and respects all RLS policies.
 * It is the default client for all public data fetching.
 * 
 * @returns Supabase client with read-only access, respecting RLS.
 * @throws Error if required environment variables are missing.
 */
export function createServerSupabaseReadOnlyClient(): SupabaseClient {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error(
      'Missing required Supabase environment variables. ' +
      'Ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set.'
    )
  }

  return createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false
    },
    global: {
      headers: {
        'User-Agent': 'CashbackDeals/1.0 (ReadOnly)',
      },
    },
    db: {
      schema: 'public',
    },
    realtime: {
      timeout: TIMEOUT_CONFIG.DATABASE.CONNECTION,
    }
  })
}

import { NextResponse } from 'next/server'

// Unified authentication error codes
export enum AUTH_ERROR_CODES {
  // General authentication errors
  MISSING_AUTH = 'MISSING_AUTH',
  INVALID_AUTH = 'INVALID_AUTH',
  
  // JWT specific errors
  JWT_MISSING = 'JWT_MISSING',
  JWT_INVALID = 'JWT_INVALID',
  JWT_EXPIRED = 'JWT_EXPIRED',
  JWT_MALFORMED = 'JWT_MALFORMED',
  
  // HMAC specific errors
  MISSING_SIGNATURE = 'MISSING_SIGNATURE',
  MISSING_TIMESTAMP = 'MISSING_TIMESTAMP',
  MISSING_PARTNER_ID = 'MISSING_PARTNER_ID',
  INVALID_SIGNATURE = 'INVALID_SIGNATURE',
  EXPIRED_TIMESTAMP = 'EXPIRED_TIMESTAMP',
  UNKNOWN_PARTNER = 'UNKNOWN_PARTNER',
  INVALID_FORMAT = 'INVALID_FORMAT',
  REPLAY_DETECTED = 'REPLAY_DETECTED',
  BODY_HASH_MISMATCH = 'BODY_HASH_MISMATCH',
  
  // Configuration errors
  CONFIG_ERROR = 'CONFIG_ERROR'
}

// Error response interface
export interface AuthErrorResponse {
  error: string
  message: string
  code: AUTH_ERROR_CODES
  traceId: string
  serverTime?: string
  supportedMethods?: string[]
  documentation?: string | object
}

// Error definitions with status codes and messages
const ERROR_DEFINITIONS = {
  [AUTH_ERROR_CODES.MISSING_AUTH]: {
    status: 401,
    error: 'Unauthorized',
    message: 'Valid authentication required. Use JWT (browser) or HMAC (API) authentication.',
    supportedMethods: ['JWT', 'HMAC'] as string[]
  },
  [AUTH_ERROR_CODES.INVALID_AUTH]: {
    status: 401,
    error: 'Unauthorized',
    message: 'Invalid authentication provided.',
    supportedMethods: ['JWT', 'HMAC'] as string[]
  },
  
  // JWT errors
  [AUTH_ERROR_CODES.JWT_MISSING]: {
    status: 401,
    error: 'Unauthorized',
    message: 'JWT token missing or invalid.',
    supportedMethods: ['JWT'] as string[]
  },
  [AUTH_ERROR_CODES.JWT_INVALID]: {
    status: 401,
    error: 'Unauthorized',
    message: 'JWT token is invalid.',
    supportedMethods: ['JWT'] as string[]
  },
  [AUTH_ERROR_CODES.JWT_EXPIRED]: {
    status: 401,
    error: 'Unauthorized',
    message: 'JWT token has expired.',
    supportedMethods: ['JWT'] as string[]
  },
  [AUTH_ERROR_CODES.JWT_MALFORMED]: {
    status: 400,
    error: 'Bad Request',
    message: 'JWT token is malformed.',
    supportedMethods: ['JWT'] as string[]
  },
  
  // HMAC errors
  [AUTH_ERROR_CODES.MISSING_SIGNATURE]: {
    status: 401,
    error: 'Unauthorized',
    message: 'Missing X-Signature header.',
    supportedMethods: ['HMAC'] as string[]
  },
  [AUTH_ERROR_CODES.MISSING_TIMESTAMP]: {
    status: 401,
    error: 'Unauthorized',
    message: 'Missing X-Timestamp header.',
    supportedMethods: ['HMAC'] as string[]
  },
  [AUTH_ERROR_CODES.MISSING_PARTNER_ID]: {
    status: 401,
    error: 'Unauthorized',
    message: 'Missing X-Partner-ID header.',
    supportedMethods: ['HMAC'] as string[]
  },
  [AUTH_ERROR_CODES.INVALID_SIGNATURE]: {
    status: 401,
    error: 'Unauthorized',
    message: 'Invalid HMAC signature.',
    supportedMethods: ['HMAC'] as string[]
  },
  [AUTH_ERROR_CODES.EXPIRED_TIMESTAMP]: {
    status: 401,
    error: 'Unauthorized',
    message: 'Request timestamp expired.',
    supportedMethods: ['HMAC'] as string[]
  },
  [AUTH_ERROR_CODES.UNKNOWN_PARTNER]: {
    status: 401,
    error: 'Unauthorized',
    message: 'Unknown partner ID.',
    supportedMethods: ['HMAC'] as string[]
  },
  [AUTH_ERROR_CODES.INVALID_FORMAT]: {
    status: 400,
    error: 'Bad Request',
    message: 'Invalid request format.',
    supportedMethods: ['HMAC'] as string[]
  },
  [AUTH_ERROR_CODES.REPLAY_DETECTED]: {
    status: 401,
    error: 'Unauthorized',
    message: 'Duplicate request detected.',
    supportedMethods: ['HMAC'] as string[]
  },
  [AUTH_ERROR_CODES.BODY_HASH_MISMATCH]: {
    status: 401,
    error: 'Unauthorized',
    message: 'Request body hash mismatch.',
    supportedMethods: ['HMAC'] as string[]
  },
  
  // Configuration errors
  [AUTH_ERROR_CODES.CONFIG_ERROR]: {
    status: 500,
    error: 'Internal Server Error',
    message: 'Authentication configuration error.',
    supportedMethods: ['JWT', 'HMAC'] as string[]
  }
} as const

// Create unified authentication error response
export function createAuthErrorResponse(
  code: AUTH_ERROR_CODES,
  traceId: string,
  options: {
    includeServerTime?: boolean
    includeDocumentation?: boolean
    customMessage?: string
  } = {}
): NextResponse {
  const definition = ERROR_DEFINITIONS[code]
  
  if (!definition) {
    throw new Error(`Unknown error code: ${code}`)
  }
  
  const response: AuthErrorResponse = {
    error: definition.error,
    message: options.customMessage || definition.message,
    code,
    traceId,
    supportedMethods: definition.supportedMethods
  }
  
  // Add server time for clock skew detection (useful for HMAC)
  if (options.includeServerTime) {
    response.serverTime = new Date().toISOString()
  }
  
  // Add documentation links
  if (options.includeDocumentation) {
    if (definition.supportedMethods.includes('HMAC')) {
      response.documentation = {
        jwt: 'For browser users: Complete CAPTCHA to receive JWT token',
        hmac: 'For API partners: Use HMAC-SHA256 signature authentication',
        guide: 'See partner integration guide for HMAC implementation examples'
      }
    } else {
      response.documentation = 'https://docs.cashback-deals.com/api/authentication'
    }
  }
  
  // Create headers
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'X-Trace-ID': traceId
  }
  
  // Add server time header for clock sync
  if (options.includeServerTime) {
    headers['Date'] = new Date().toUTCString()
  }
  
  // Add WWW-Authenticate header for 401 responses
  if (definition.status === 401) {
    if (definition.supportedMethods.includes('HMAC')) {
      headers['WWW-Authenticate'] = 'Bearer realm="Search API", HMAC realm="Partner API"'
    } else {
      headers['WWW-Authenticate'] = 'Bearer realm="Search API"'
    }
  }
  
  return NextResponse.json(response, {
    status: definition.status,
    headers
  })
}

// Convenience functions for common error types
export function createMissingAuthResponse(traceId: string): NextResponse {
  return createAuthErrorResponse(AUTH_ERROR_CODES.MISSING_AUTH, traceId, {
    includeDocumentation: true
  })
}

export function createHMACErrorResponse(code: AUTH_ERROR_CODES, traceId: string): NextResponse {
  return createAuthErrorResponse(code, traceId, {
    includeServerTime: true,
    includeDocumentation: true
  })
}

export function createJWTErrorResponse(code: AUTH_ERROR_CODES, traceId: string): NextResponse {
  return createAuthErrorResponse(code, traceId, {
    includeDocumentation: false
  })
}

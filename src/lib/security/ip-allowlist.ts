/**
 * IP Allowlist Middleware for Cashback Deals
 *
 * Provides CIDR-based IP validation with IPv4/IPv6 support, feature flags,
 * self-lockout prevention, and GDPR-compliant logging as per PR3 technical specifications.
 *
 * 2025 Best Practices:
 * - Uses ipaddr.js for comprehensive IP parsing
 * - GDPR-compliant IP masking for logs
 * - Advanced performance optimization
 *
 * <AUTHOR> Agent
 * @date 2025-07-13
 */

import { NextRequest, NextResponse } from 'next/server';
import * as ipaddr from 'ipaddr.js';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface IPAllowlistConfig {
  enabled: boolean;
  allowedCIDRs: string[];
  logViolations: boolean;
  blockByDefault: boolean;
  includeDebug: boolean;
}

export interface IPValidationResult {
  allowed: boolean;
  ip: string;
  reason: 'allowed_by_rule' | 'blocked_by_default' | 'invalid_ip' | 'disabled';
  matchedRule?: string;
  debugInfo?: {
    headers: Record<string, string>;
    extractedIPs: string[];
  };
}

export interface CIDRRange {
  network: string;
  prefix: number;
  isIPv6: boolean;
}

// ============================================================================
// Environment Configuration
// ============================================================================

/**
 * Load IP allowlist configuration from environment variables
 * Automatically disables in development unless explicitly enabled
 */
export function getIPAllowlistConfig(): IPAllowlistConfig {
  // Disable by default in development environment
  const isProduction = process.env.NODE_ENV === 'production';
  const explicitlyEnabled = process.env.ENABLE_IP_ALLOWLIST === 'true';
  
  // Only enable if explicitly set to true, or in production with default config
  const enabled = explicitlyEnabled;
  
  const cidrsString = process.env.IP_ALLOWLIST_CIDRS || '';
  const logViolations = process.env.IP_ALLOWLIST_LOG_VIOLATIONS !== 'false';
  const blockByDefault = process.env.IP_ALLOWLIST_BLOCK_BY_DEFAULT !== 'false';
  const includeDebug = process.env.IP_ALLOWLIST_INCLUDE_DEBUG === 'true';

  // Parse CIDR ranges from comma-separated string
  let allowedCIDRs = cidrsString
    .split(',')
    .map(cidr => cidr.trim())
    .filter(cidr => cidr.length > 0);

  // If no CIDR ranges provided, use localhost defaults only in development
  if (allowedCIDRs.length === 0 && !isProduction) {
    allowedCIDRs = ['127.0.0.1/32', '::1/128'];
  }

  // In development, always include localhost ranges for safety
  if (!isProduction && enabled && allowedCIDRs.length > 0) {
    const localhostRanges = ['127.0.0.1/32', '::1/128'];
    localhostRanges.forEach(range => {
      if (!allowedCIDRs.includes(range)) {
        allowedCIDRs.push(range);
      }
    });
  }

  return {
    enabled,
    allowedCIDRs,
    logViolations,
    blockByDefault,
    includeDebug,
  };
}

// ============================================================================
// Build-time Validation (Fail-fast approach)
// ============================================================================

/**
 * Validate IP allowlist configuration at build time
 * Throws error if critical validation fails (prevents deployment)
 */
export function validateIPAllowlistConfig(config: IPAllowlistConfig): string[] {
  const errors: string[] = [];

  // Critical: Fail build if no CIDR ranges configured when enabled
  if (config.enabled && config.allowedCIDRs.length === 0) {
    throw new Error('FATAL: No CIDR ranges configured - risk of complete lockout');
  }

  // Validate each CIDR range
  config.allowedCIDRs.forEach(cidr => {
    try {
      parseCIDR(cidr);
    } catch (error) {
      const errorMessage = `FATAL: Invalid CIDR range: ${cidr}`;
      console.error(errorMessage, error);
      throw new Error(errorMessage);
    }
  });

  // Validate office IP ranges are included (self-lockout prevention)
  const requiredOfficeRanges = ['127.0.0.1/32', '10.0.0.0/8'];
  const hasOfficeRange = requiredOfficeRanges.some(required =>
    config.allowedCIDRs.some(cidr => cidr === required || cidr.includes('10.0.0.0/8'))
  );

  if (config.enabled && !hasOfficeRange) {
    console.warn('WARNING: No office IP ranges detected. Consider adding 10.0.0.0/8 or 127.0.0.1/32');
  }

  return errors;
}

// ============================================================================
// IP Address Validation
// ============================================================================

/**
 * Validate IPv4 address format using ipaddr.js
 */
export function isValidIPv4(ip: string): boolean {
  try {
    if (!ipaddr.isValid(ip)) return false;
    const addr = ipaddr.process(ip);
    return addr.kind() === 'ipv4';
  } catch {
    return false;
  }
}

/**
 * Validate IPv6 address format using ipaddr.js (handles all RFC 4291 cases)
 * Supports IPv4-mapped IPv6, zero compression, and reserved ranges
 */
export function isValidIPv6(ip: string): boolean {
  try {
    if (!ipaddr.isValid(ip)) return false;
    const addr = ipaddr.process(ip);
    
    // Accept IPv6 addresses
    if (addr.kind() === 'ipv6') return true;
    
    // Special case: IPv4-mapped IPv6 addresses (::ffff:x.x.x.x)
    // These are processed as IPv4 by ipaddr.js but are syntactically IPv6
    if (ip.startsWith('::ffff:') && addr.kind() === 'ipv4') {
      return true;
    }
    
    return false;
  } catch {
    return false;
  }
}

/**
 * Parse CIDR notation into network and prefix using ipaddr.js
 */
export function parseCIDR(cidr: string): CIDRRange {
  const parts = cidr.split('/');
  if (parts.length !== 2) {
    throw new Error(`Invalid CIDR format: ${cidr}`);
  }

  const [network, prefixStr] = parts;
  const prefix = parseInt(prefixStr, 10);

  try {
    if (!ipaddr.isValid(network)) {
      throw new Error(`Invalid IP address: ${network}`);
    }

    const addr = ipaddr.process(network);
    const isIPv6 = addr.kind() === 'ipv6';

    // Validate prefix length
    const maxPrefix = isIPv6 ? 128 : 32;
    if (prefix < 0 || prefix > maxPrefix) {
      throw new Error(`Invalid ${isIPv6 ? 'IPv6' : 'IPv4'} prefix: ${prefix} (max: ${maxPrefix})`);
    }

    return { network, prefix, isIPv6 };
  } catch (error) {
    throw new Error(`Invalid CIDR: ${cidr} - ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Check if IP address is within CIDR range using ipaddr.js
 * Supports IPv4, IPv6, IPv4-mapped IPv6, and reserved ranges
 */
export function isIPInCIDR(ip: string, cidr: string): boolean {
  try {
    // Parse CIDR range
    const cidrRange = parseCIDR(cidr);
    
    // Validate both IP and network address
    if (!ipaddr.isValid(ip) || !ipaddr.isValid(cidrRange.network)) {
      return false;
    }

    const ipAddr = ipaddr.process(ip);
    const networkAddr = ipaddr.process(cidrRange.network);

    // Special case: IPv4-mapped IPv6 addresses (::ffff:x.x.x.x)
    // These are processed as IPv4 by ipaddr.js, so we need to handle them specially
    if (ip.startsWith('::ffff:') && networkAddr.kind() === 'ipv4') {
      // IPv4-mapped IPv6 against IPv4 CIDR - should match
      return ipAddr.match(networkAddr, cidrRange.prefix);
    }

    // Both addresses must be the same type (IPv4 or IPv6)
    if (ipAddr.kind() !== networkAddr.kind()) {
      return false;
    }

    // Use ipaddr.js built-in CIDR matching
    return ipAddr.match(networkAddr, cidrRange.prefix);
  } catch (error) {
    console.error('Error checking IP in CIDR:', error);
    return false;
  }
}

/**
 * Check if an IP address is a valid format (IPv4 or IPv6)
 * Includes support for IPv4-mapped IPv6 and reserved ranges
 */
export function isValidIP(ip: string): boolean {
  return isValidIPv4(ip) || isValidIPv6(ip);
}

// ============================================================================
// GDPR-Compliant IP Masking (2025 Legal Requirement)
// ============================================================================

/**
 * Mask IP address for GDPR-compliant logging
 * EU courts confirm IP addresses are personal data requiring protection
 * Handles IPv4, IPv6, and IPv4-mapped IPv6 addresses
 */
export function maskIPForLogging(ip: string): string {
  try {
    if (!ipaddr.isValid(ip)) {
      return 'invalid.ip.xxx';
    }

    // Special case for IPv4-mapped IPv6 addresses (::ffff:x.x.x.x)
    if (ip.startsWith('::ffff:')) {
      // Extract the IPv4 part and mask it
      const ipv4Part = ip.substring(7); // Remove '::ffff:' prefix
      const maskedIPv4 = ipv4Part.replace(/\.\d+$/, '.xxx');
      return `::ffff:${maskedIPv4}`;
    }

    const addr = ipaddr.process(ip);
    
    if (addr.kind() === 'ipv4') {
      // IPv4: Mask last octet (192.168.1.xxx)
      return ip.replace(/\.\d+$/, '.xxx');
    } else if (addr.kind() === 'ipv6') {
      // Regular IPv6: Mask last 32 bits (2001:db8::xxxx)
      return ip.replace(/:([0-9a-fA-F]{1,4}:)*[0-9a-fA-F]{1,4}$/, ':xxxx');
    }
    
    return 'invalid.ip.xxx';
  } catch {
    return 'invalid.ip.xxx';
  }
}

// ============================================================================
// Client IP Extraction
// ============================================================================

/**
 * Extract client IP from request headers with proper precedence
 * Priority: X-Forwarded-For → X-Real-IP → CF-Connecting-IP → fallback
 */
export function getClientIP(request: NextRequest): string {
  // 1. X-Forwarded-For (first IP in comma-separated list)
  const xForwardedFor = request.headers.get('X-Forwarded-For');
  if (xForwardedFor) {
    const firstIP = xForwardedFor.split(',')[0].trim();
    if (firstIP) return firstIP;
  }

  // 2. X-Real-IP (single IP)
  const xRealIP = request.headers.get('X-Real-IP');
  if (xRealIP) return xRealIP.trim();

  // 3. CF-Connecting-IP (Cloudflare)
  const cfConnectingIP = request.headers.get('CF-Connecting-IP');
  if (cfConnectingIP) return cfConnectingIP.trim();

  // 4. Fallback for development
  return '127.0.0.1';
}

// ============================================================================
// IP Allowlist Validation
// ============================================================================

/**
 * Validate IP against allowlist configuration
 */
export function validateIPAllowlist(ip: string, config: IPAllowlistConfig): IPValidationResult {
  // If disabled, allow all traffic
  if (!config.enabled) {
    return {
      allowed: true,
      ip,
      reason: 'disabled',
    };
  }

  // Validate IP format (supports IPv4, IPv6, and IPv4-mapped IPv6)
  if (!isValidIP(ip)) {
    return {
      allowed: false,
      ip,
      reason: 'invalid_ip',
    };
  }

  // Check against allowed CIDR ranges
  for (const cidr of config.allowedCIDRs) {
    if (isIPInCIDR(ip, cidr)) {
      return {
        allowed: true,
        ip,
        reason: 'allowed_by_rule',
        matchedRule: cidr,
      };
    }
  }

  // Block by default if no rules match
  return {
    allowed: false,
    ip,
    reason: 'blocked_by_default',
  };
}

// ============================================================================
// Middleware Application
// ============================================================================

/**
 * Apply IP allowlist middleware to request
 * Returns null if allowed, NextResponse with 403 if blocked
 */
export function applyIPAllowlist(request: NextRequest): NextResponse | null {
  const config = getIPAllowlistConfig();
  
  // Skip if disabled
  if (!config.enabled) {
    return null;
  }

  const clientIP = getClientIP(request);
  const result = validateIPAllowlist(clientIP, config);

  // GDPR-compliant logging (2025 Legal Requirement)
  if (config.logViolations && !result.allowed) {
    const maskedIP = maskIPForLogging(clientIP);
    const logData = {
      event: "IP_ALLOWLIST_VIOLATION",
      timestamp: new Date().toISOString(),
      ip: maskedIP, // GDPR-compliant masked IP
      url: request.url,
      method: request.method,
      reason: result.reason,
      userAgent: request.headers.get('User-Agent'),
      retention: "30-days-max", // EU compliance requirement
    };

    console.warn('[IP Allowlist] Blocked request:', JSON.stringify(logData));
  }

  // Allow if validation passed
  if (result.allowed) {
    return null;
  }

  // Block with 403 response
  const errorResponse = {
    error: 'IP_NOT_ALLOWED',
    message: 'Your IP address is not authorized to access this resource',
    timestamp: new Date().toISOString(),
    ...(config.includeDebug && {
      debug: {
        ip: clientIP,
        reason: result.reason,
      },
    }),
  };

  return NextResponse.json(errorResponse, { status: 403 });
}

// ============================================================================
// Health Check
// ============================================================================

/**
 * Health check for IP allowlist functionality
 */
export async function healthCheckIPAllowlist(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: {
    configLoaded: boolean;
    cidrRulesValid: boolean;
    performanceOk: boolean;
    lastError?: string;
  };
}> {
  try {
    const config = getIPAllowlistConfig();
    const errors = validateIPAllowlistConfig(config);
    
    // Performance test
    const start = performance.now();
    validateIPAllowlist('**********', config);
    const duration = performance.now() - start;
    
    return {
      status: errors.length === 0 && duration < 2 ? 'healthy' : 'degraded',
      details: {
        configLoaded: true,
        cidrRulesValid: errors.length === 0,
        performanceOk: duration < 2, // 2ms target
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        configLoaded: false,
        cidrRulesValid: false,
        performanceOk: false,
        lastError: error instanceof Error ? error.message : 'Unknown error',
      },
    };
  }
}

// src/lib/security/cors.ts
// Centralized CORS middleware for protected API routes

import { NextRequest, NextResponse } from 'next/server'

// Feature flag for CORS enforcement
export function isCorsStrictEnabled(): boolean {
  return process.env.ENABLE_CORS_STRICT === 'true'
}

// Get allowed routes from environment or default
export function getCorsProtectedRoutes(): string[] {
  const envRoutes = process.env.CORS_PROTECTED_ROUTES
  if (envRoutes) {
    return envRoutes.split(',').map(route => route.trim())
  }
  
  // Default protected routes
  return [
    '/api/catalog',
    '/api/search/more',
    '/api/products',
    '/api/brands',
    '/api/retailers'
  ]
}

// Get specific Amplify domains from environment or use defaults
function getAllowedAmplifyDomains(): string[] {
  const envDomains = process.env.CORS_ALLOWED_AMPLIFY_DOMAINS
  if (envDomains) {
    return envDomains.split(',').map(domain => domain.trim())
  }
  
  // Default to specific domains we control
  return [
    'https://4-2.d3q274urye85k3.amplifyapp.com', // Current production domain
    'https://main.d3q274urye85k3.amplifyapp.com', // Main branch
    'https://staging.d3q274urye85k3.amplifyapp.com' // Staging branch (if exists)
  ]
}

// CORS configuration for protected routes
const ALLOWED_ORIGINS = [
  'https://cashback-deals.com',
  'https://www.cashback-deals.com',
  ...getAllowedAmplifyDomains(),
  // Add development origins in development mode only
  ...(process.env.NODE_ENV === 'development' ? [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    /^http:\/\/localhost:\d+$/,
    /^http:\/\/127\.0\.0\.1:\d+$/
  ] : [])
]

// Helper function to check if origin is allowed
export function isOriginAllowed(origin: string | null): boolean {
  if (!origin) return false

  return ALLOWED_ORIGINS.some(allowed => {
    if (typeof allowed === 'string') {
      return origin === allowed
    } else {
      return allowed.test(origin)
    }
  })
}

// Get allowed origin for CORS header
export function getAllowedOriginForCors(request: NextRequest): string {
  const origin = request.headers.get('origin')

  if (origin && isOriginAllowed(origin)) {
    return origin
  }

  // Default to first allowed origin for non-browser requests
  return 'https://cashback-deals.com'
}

// Check if route should be protected by CORS
export function shouldApplyCorsProtection(pathname: string): boolean {
  if (!isCorsStrictEnabled()) {
    return false
  }

  const protectedRoutes = getCorsProtectedRoutes()
  return protectedRoutes.some(route => {
    // Handle dynamic routes
    if (route.includes('[id]')) {
      const routePattern = route.replace('[id]', '[^/]+')
      const regex = new RegExp(`^${routePattern}$`)
      return regex.test(pathname)
    }
    
    // Handle exact and prefix matches
    return pathname === route || pathname.startsWith(route + '/')
  })
}

// Apply CORS policy to a response
export function applyCorsHeaders(
  request: NextRequest, 
  response: NextResponse,
  options: {
    methods?: string[]
    headers?: string[]
    credentials?: boolean
  } = {}
): NextResponse {
  const {
    methods = ['GET', 'OPTIONS'],
    headers = ['Content-Type', 'Authorization', 'X-Signature', 'X-Timestamp', 'X-Partner-ID', 'X-Version'],
    credentials = true
  } = options

  const allowedOrigin = getAllowedOriginForCors(request)
  
  response.headers.set('Access-Control-Allow-Origin', allowedOrigin)
  response.headers.set('Access-Control-Allow-Methods', methods.join(', '))
  response.headers.set('Access-Control-Allow-Headers', headers.join(', '))
  
  if (credentials) {
    response.headers.set('Access-Control-Allow-Credentials', 'true')
  }

  return response
}

// Helper function to check if request is from a server/tool (non-browser)
function isServerToServerRequest(request: NextRequest): boolean {
  const userAgent = request.headers.get('user-agent') || ''
  
  // Check for typical server/tool user agents (non-Mozilla browsers)
  const serverAgentPatterns = [
    /curl/i,
    /wget/i,
    /postman/i,
    /insomnia/i,
    /httpie/i,
    /python-requests/i,
    /node-fetch/i,
    /axios/i,
    /^go-http-client/i,
    /^java/i,
    /^apache-httpclient/i,
    /^okhttp/i
  ]
  
  // If no Mozilla in user agent and matches server patterns, it's likely server-to-server
  return !userAgent.includes('Mozilla') && 
         (userAgent === '' || serverAgentPatterns.some(pattern => pattern.test(userAgent)))
}

// Create CORS error response for blocked origins
export function createCorsErrorResponse(origin: string | null): NextResponse {
  const errorResponse = {
    error: 'Forbidden',
    message: 'Cross-Origin Request Blocked: The same-origin policy disallows reading from the remote resource.',
    code: 'CORS_BLOCKED',
    timestamp: new Date().toISOString()
  }

  // Log CORS violation for monitoring
  console.warn('CORS violation detected', {
    event: 'CORS_VIOLATION',
    origin,
    timestamp: new Date().toISOString(),
    blocked: true
  })

  return NextResponse.json(errorResponse, { 
    status: 403,
    headers: {
      'Content-Type': 'application/json'
      // Note: No CORS headers on blocked requests
    }
  })
}

// Main CORS enforcement middleware
export function enforceCorsPolicy(request: NextRequest): NextResponse | null {
  const pathname = new URL(request.url).pathname
  
  // Skip if CORS protection not enabled for this route
  if (!shouldApplyCorsProtection(pathname)) {
    return null
  }

  const origin = request.headers.get('origin')
  
  // Allow same-origin requests (when origin header is not present)
  if (!origin) {
    // Additional check for server-to-server requests
    if (isServerToServerRequest(request)) {
      // Log server-to-server access for monitoring
      console.log(JSON.stringify({
        event: 'SERVER_TO_SERVER_ACCESS',
        timestamp: new Date().toISOString(),
        pathname,
        userAgent: request.headers.get('user-agent') || 'unknown',
        ip: request.headers.get('x-forwarded-for') || 'unknown'
      }))
    }
    return null
  }

  // Block disallowed origins
  if (!isOriginAllowed(origin)) {
    return createCorsErrorResponse(origin)
  }

  // Origin is allowed, continue with request
  return null
}

// Handle OPTIONS preflight requests
export function handleCorsPreflightRequest(request: NextRequest): NextResponse {
  const allowedOrigin = getAllowedOriginForCors(request)
  
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': allowedOrigin,
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Signature, X-Timestamp, X-Partner-ID, X-Version',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Max-Age': '86400' // 24 hours
    }
  })
}

// Export types for use in route handlers
export interface CorsOptions {
  methods?: string[]
  headers?: string[]
  credentials?: boolean
}

// Log CORS enforcement metrics for monitoring
export function logCorsMetrics(pathname: string, origin: string | null, allowed: boolean) {
  console.log(JSON.stringify({
    event: 'CORS_CHECK',
    pathname,
    origin,
    allowed,
    corsEnabled: isCorsStrictEnabled(),
    timestamp: new Date().toISOString()
  }))
}
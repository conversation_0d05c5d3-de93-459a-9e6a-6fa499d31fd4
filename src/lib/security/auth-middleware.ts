// src/lib/security/auth-middleware.ts
// Dual authentication middleware for search endpoints (JWT + HMAC)

import { NextRequest, NextResponse } from 'next/server'
import { verifyRequestJWT, type JWTPayload } from './jwt'
import {
  verifyRequestHMAC,
  shouldEnforceAuthentication,
  isHMACEnabled,
  generateTraceId,
  logSecurityEvent,
  type HMACPayload
} from './hmac'
import { createMissingAuthResponse, AUTH_ERROR_CODES } from './error-responses'

// Get specific Amplify domains from environment or use defaults  
function getAllowedAmplifyDomains(): string[] {
  const envDomains = process.env.CORS_ALLOWED_AMPLIFY_DOMAINS
  if (envDomains) {
    return envDomains.split(',').map(domain => domain.trim())
  }
  
  // Default to specific domains we control
  return [
    'https://4-2.d3q274urye85k3.amplifyapp.com', // Current production domain
    'https://main.d3q274urye85k3.amplifyapp.com', // Main branch
    'https://staging.d3q274urye85k3.amplifyapp.com' // Staging branch (if exists)
  ]
}

// CORS configuration for protected routes
const ALLOWED_ORIGINS = [
  'https://cashback-deals.com',
  'https://www.cashback-deals.com',
  ...getAllowedAmplifyDomains()
]

// Helper function to check if origin is allowed
function isOriginAllowed(origin: string | null): boolean {
  if (!origin) return false

  // All allowed origins are now strings (no regex patterns)
  return ALLOWED_ORIGINS.includes(origin)
}

// Get allowed origin for CORS header
export function getAllowedOrigin(request: NextRequest): string {
  const origin = request.headers.get('origin')

  if (origin && isOriginAllowed(origin)) {
    return origin
  }

  // Default to first allowed origin for non-browser requests
  return 'https://cashback-deals.com'
}

// Authentication result interface
export interface AuthResult {
  success: boolean
  method: 'JWT' | 'HMAC' | null
  payload: JWTPayload | HMACPayload | null
  error?: string
  traceId: string
}

// Get client IP from request
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfIP = request.headers.get('cf-connecting-ip')
  
  return cfIP || realIP || forwarded?.split(',')[0].trim() || 'unknown'
}

// Dual authentication check for search endpoints
export async function authenticateSearchRequest(request: NextRequest): Promise<AuthResult> {
  const traceId = generateTraceId(request.method)
  const endpoint = new URL(request.url).pathname
  const ip = getClientIP(request)
  
  // Check feature flags first
  if (!shouldEnforceAuthentication(endpoint)) {
    return { 
      success: true, 
      method: null, 
      payload: null, 
      traceId 
    }
  }
  
  // Try JWT first (browser users with CAPTCHA)
  try {
    const jwtPayload = await verifyRequestJWT(request)
    if (jwtPayload) {
      logSecurityEvent({
        type: 'JWT_AUTH_SUCCESS',
        endpoint,
        method: request.method,
        ip,
        timestamp: new Date().toISOString(),
        traceId
      })
      
      return {
        success: true,
        method: 'JWT',
        payload: jwtPayload,
        traceId
      }
    }
  } catch (error) {
    console.warn('JWT verification error:', error)
  }
  
  // Try HMAC second (API partners) - only if enabled
  if (isHMACEnabled()) {
    try {
      const hmacPayload = await verifyRequestHMAC(request)
      if (hmacPayload) {
        logSecurityEvent({
          type: 'HMAC_AUTH_SUCCESS',
          endpoint,
          method: request.method,
          partnerId: hmacPayload.partnerId,
          ip,
          timestamp: new Date().toISOString(),
          traceId
        })
        
        return {
          success: true,
          method: 'HMAC',
          payload: hmacPayload,
          traceId
        }
      }
    } catch (error) {
      console.warn('HMAC verification error:', error)
    }
  }
  
  // Log authentication failure
  logSecurityEvent({
    type: 'HMAC_AUTH_FAILURE',
    endpoint,
    method: request.method,
    ip,
    timestamp: new Date().toISOString(),
    traceId,
    error: AUTH_ERROR_CODES.MISSING_AUTH,
    errorMessage: 'No valid authentication found'
  })
  
  return {
    success: false,
    method: null,
    payload: null,
    error: 'No valid authentication found',
    traceId
  }
}

// Create consistent unauthorized response for search endpoints
export function createSearchUnauthorizedResponse(traceId: string): NextResponse {
  return createMissingAuthResponse(traceId)
}

// Middleware wrapper for search endpoints
export async function withSearchAuthentication(
  request: NextRequest,
  handler: (request: NextRequest, authResult: AuthResult) => Promise<NextResponse>
): Promise<NextResponse> {
  // Apply authentication
  const authResult = await authenticateSearchRequest(request)
  
  if (!authResult.success) {
    console.warn(`Search API unauthorized access attempt from ${getClientIP(request)}`)
    return createSearchUnauthorizedResponse(authResult.traceId)
  }
  
  // Log successful authentication
  console.log(`Search API access: ${authResult.method} authentication successful`, {
    endpoint: new URL(request.url).pathname,
    method: request.method,
    traceId: authResult.traceId,
    partnerId: authResult.method === 'HMAC' ? (authResult.payload as HMACPayload)?.partnerId : undefined
  })
  
  // Call the actual handler with authentication result
  return handler(request, authResult)
}

// Helper to check if request has authentication headers
export function hasAuthenticationHeaders(request: NextRequest): boolean {
  // Check for JWT in Authorization header or cookie
  const authHeader = request.headers.get('authorization')
  const authCookie = request.cookies.get('auth-token')

  if (authHeader?.startsWith('Bearer ') || authCookie) {
    return true
  }

  // Check for HMAC headers using constants
  const signature = request.headers.get('x-signature')
  const timestamp = request.headers.get('x-timestamp')
  const partnerId = request.headers.get('x-partner-id')

  return !!(signature && timestamp && partnerId)
}

// Helper to determine authentication method from request
export function getAuthenticationMethod(request: NextRequest): 'JWT' | 'HMAC' | 'NONE' {
  // Check for JWT first
  const authHeader = request.headers.get('authorization')
  const authCookie = request.cookies.get('auth-token')
  
  if (authHeader?.startsWith('Bearer ') || authCookie) {
    return 'JWT'
  }
  
  // Check for HMAC headers
  const signature = request.headers.get('x-signature')
  const timestamp = request.headers.get('x-timestamp')
  const partnerId = request.headers.get('x-partner-id')
  
  if (signature && timestamp && partnerId) {
    return 'HMAC'
  }
  
  return 'NONE'
}

// Export types for use in other modules
export type { JWTPayload, HMACPayload }

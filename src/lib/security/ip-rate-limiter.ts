// src/lib/security/ip-rate-limiter.ts
// Simple in-memory IP-based rate limiter for public endpoints

import { NextRequest, NextResponse } from 'next/server'

interface RateLimitEntry {
  requests: number
  lastReset: number
}

// In-memory storage for rate limiting (LRU-like cleanup)
const ipRateLimits = new Map<string, RateLimitEntry>()
const MAX_ENTRIES = 10000 // Prevent memory exhaustion

// Rate limit configuration
export interface IPRateLimitConfig {
  requestsPerSecond: number
  windowSeconds: number
}

// Default configuration for search suggestions
export const SUGGESTIONS_RATE_LIMIT: IPRateLimitConfig = {
  requestsPerSecond: 10,
  windowSeconds: 60
}

/**
 * Get client IP address from request
 */
function getClientIP(request: NextRequest): string {
  // Check various headers for the real IP
  const forwarded = request.headers.get('x-forwarded-for')
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  const realIP = request.headers.get('x-real-ip')
  if (realIP) {
    return realIP
  }
  
  // Fallback to default for localhost/development
  return 'localhost'
}

/**
 * Clean up old entries to prevent memory leak
 */
function cleanupOldEntries(config: IPRateLimitConfig) {
  if (ipRateLimits.size < MAX_ENTRIES) return
  
  const cutoff = Date.now() - (config.windowSeconds * 1000 * 2) // Keep 2x window for safety
  const entriesToRemove: string[] = []
  
  for (const [ip, entry] of ipRateLimits.entries()) {
    if (entry.lastReset < cutoff) {
      entriesToRemove.push(ip)
    }
  }
  
  entriesToRemove.forEach(ip => ipRateLimits.delete(ip))
  
  console.log(`IP rate limiter: cleaned up ${entriesToRemove.length} old entries`)
}

/**
 * Apply IP-based rate limiting
 * Returns null if request is allowed, or NextResponse with 429 if rate limited
 */
export function applyIPRateLimit(
  request: NextRequest, 
  config: IPRateLimitConfig = SUGGESTIONS_RATE_LIMIT
): NextResponse | null {
  const ip = getClientIP(request)
  const now = Date.now()
  const windowMs = config.windowSeconds * 1000
  
  // Clean up old entries periodically
  if (Math.random() < 0.01) { // 1% chance per request
    cleanupOldEntries(config)
  }
  
  // Get or create rate limit entry for this IP
  let entry = ipRateLimits.get(ip)
  
  if (!entry) {
    entry = { requests: 0, lastReset: now }
    ipRateLimits.set(ip, entry)
  }
  
  // Reset counter if window has passed
  if (now - entry.lastReset > windowMs) {
    entry.requests = 0
    entry.lastReset = now
  }
  
  // Calculate max requests for the current window
  const maxRequests = config.requestsPerSecond * config.windowSeconds
  
  // Check if rate limit exceeded
  if (entry.requests >= maxRequests) {
    console.warn(`IP rate limit exceeded for ${ip}: ${entry.requests}/${maxRequests} requests in ${config.windowSeconds}s`)
    
    return NextResponse.json(
      {
        error: 'Rate limit exceeded',
        message: `Too many requests. Limit: ${config.requestsPerSecond} requests per second.`,
        retryAfter: Math.ceil((entry.lastReset + windowMs - now) / 1000)
      },
      { 
        status: 429,
        headers: {
          'Retry-After': Math.ceil((entry.lastReset + windowMs - now) / 1000).toString(),
          'X-RateLimit-Limit': maxRequests.toString(),
          'X-RateLimit-Remaining': Math.max(0, maxRequests - entry.requests).toString(),
          'X-RateLimit-Reset': new Date(entry.lastReset + windowMs).toISOString()
        }
      }
    )
  }
  
  // Increment request counter
  entry.requests++
  
  return null
}

/**
 * Get current rate limit status for an IP (for debugging)
 */
export function getRateLimitStatus(request: NextRequest, config: IPRateLimitConfig = SUGGESTIONS_RATE_LIMIT) {
  const ip = getClientIP(request)
  const entry = ipRateLimits.get(ip)
  const maxRequests = config.requestsPerSecond * config.windowSeconds
  
  return {
    ip,
    requests: entry?.requests || 0,
    maxRequests,
    remaining: Math.max(0, maxRequests - (entry?.requests || 0)),
    resetTime: entry ? new Date(entry.lastReset + (config.windowSeconds * 1000)) : new Date(),
    windowSeconds: config.windowSeconds
  }
}
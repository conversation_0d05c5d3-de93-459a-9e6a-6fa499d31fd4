/**
 * Security Utility Functions
 * 
 * This file contains reusable security utilities for input sanitization,
 * XSS prevention, and safe data handling across the application.
 * 
 * Security Features:
 * - Input sanitization and validation
 * - XSS prevention utilities
 * - Safe JSON-LD rendering
 * - Content Security Policy helpers
 * - Rate limiting utilities
 */

/**
 * Safe DOMPurify wrapper that handles SSR issues with dynamic imports
 */
const getSafeDOMPurify = async () => {
  // Check if we're in a browser or Node.js environment
  if (typeof window === 'undefined' && typeof global === 'undefined') {
    // Fallback for edge runtime or SSR
    return {
      sanitize: (input: string, options?: any) => {
        // Basic sanitization when DOMPurify fails
        return input
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '');
      }
    };
  }

  try {
    // Only import DOMPurify when needed and in browser/Node environment
    const DOMPurify = (await import('isomorphic-dompurify')).default;
    return DOMPurify;
  } catch (error) {
    // Fallback for other issues
    return {
      sanitize: (input: string, options?: any) => {
        // Basic sanitization when DOMPurify fails
        return input
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '');
      }
    };
  }
};

/**
 * Enhanced string sanitization with XSS prevention
 * Removes dangerous characters and patterns while preserving safe content
 */
export const sanitizeString = (input: string | null | undefined, maxLength = 255): string => {
  if (!input || typeof input !== 'string') return '';

  // First pass: Remove control characters and normalize whitespace
  let sanitized = input
    .replace(/[\x00-\x1f\x7f-\x9f]/g, '') // Remove control characters
    .replace(/\s+/g, ' '); // Normalize whitespace

  // Second pass: Remove potentially dangerous HTML/XML characters
  sanitized = sanitized
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/[&]/g, '&amp;') // Escape ampersands
    .replace(/['"]/g, '') // Remove quotes
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .replace(/on\w+=/gi, ''); // Remove event handlers

  return String(sanitized).substring(0, maxLength);
};

/**
 * Advanced input sanitization using DOMPurify
 * For cases where some HTML formatting might be needed
 */
export const sanitizeHtml = async (
  input: string,
  options: {
    allowedTags?: string[];
    allowedAttributes?: string[];
    maxLength?: number;
  } = {}
): Promise<string> => {
  const {
    allowedTags = ['b', 'i', 'em', 'strong', 'p', 'br'],
    allowedAttributes = [],
    maxLength = 1000
  } = options;

  if (!input || typeof input !== 'string') return '';

  const safeDOMPurify = await getSafeDOMPurify();
  const sanitized = safeDOMPurify.sanitize(input, {
    ALLOWED_TAGS: allowedTags,
    ALLOWED_ATTR: allowedAttributes,
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
  });

  return String(sanitized).substring(0, maxLength);
};

/**
 * Secure JSON-LD rendering for structured data
 * Prevents XSS while maintaining valid JSON-LD format
 */
export const renderSecureJsonLd = (data: any): string => {
  if (!data || typeof data !== 'object') {
    throw new Error('Invalid data for JSON-LD rendering');
  }

  try {
    // Serialize to JSON first
    let jsonString = JSON.stringify(data);

    // Only escape dangerous characters that could break out of JSON-LD context
    // We need to be careful not to break valid JSON structure
    jsonString = jsonString
      .replace(/</g, '\\u003c')
      .replace(/>/g, '\\u003e')
      .replace(/&/g, '\\u0026')
      .replace(/javascript:/gi, 'javascript\\u003a')
      .replace(/vbscript:/gi, 'vbscript\\u003a')
      .replace(/data:text\/html/gi, 'data\\u003atext\\u002fhtml')
      .replace(/onload=/gi, 'onload\\u003d')
      .replace(/onerror=/gi, 'onerror\\u003d')
      .replace(/onclick=/gi, 'onclick\\u003d')
      .replace(/onmouseover=/gi, 'onmouseover\\u003d')
      .replace(/data-\w*=/gi, (match) => match.replace(/=/g, '\\u003d'))
      .replace(/eval\(/gi, 'eval\\u0028')
      .replace(/alert\(/gi, 'alert\\u0028')
      .replace(/fetch\(/gi, 'fetch\\u0028')
      .replace(/document\./gi, 'document\\u002e')
      .replace(/onerror/gi, 'on\\u0065rror'); // Escape all instances of onerror

    return jsonString;
  } catch (error) {
    console.error('Error rendering JSON-LD:', error);
    throw new Error('Failed to render secure JSON-LD');
  }
};

/**
 * Validates if a string is a valid UUID v4
 */
export const isValidUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Validates if a string is a valid slug
 */
export const isValidSlug = (slug: string): boolean => {
  const slugRegex = /^[a-zA-Z0-9_-]+$/;
  return slugRegex.test(slug) && slug.length >= 2 && slug.length <= 150;
};

/**
 * Validates and sanitizes ID parameters (UUID or slug)
 */
export const validateIdParameter = (id: string | null | undefined): {
  isValid: boolean;
  sanitized: string;
  isUUID: boolean;
} => {
  if (!id || typeof id !== 'string') {
    return { isValid: false, sanitized: '', isUUID: false };
  }

  const sanitized = sanitizeString(id, 150);
  const isUUID = isValidUUID(sanitized);
  const isSlug = isValidSlug(sanitized);

  return {
    isValid: isUUID || isSlug,
    sanitized,
    isUUID
  };
};

/**
 * Validates and sanitizes search queries
 */
export const validateSearchQuery = (query: string | null | undefined): {
  isValid: boolean;
  sanitized: string;
} => {
  if (!query || typeof query !== 'string') {
    return { isValid: false, sanitized: '' };
  }

  const sanitized = sanitizeString(query, 200);

  // Check minimum length after sanitization
  if (sanitized.length < 1) {
    return { isValid: false, sanitized: '' };
  }

  // Check for suspicious patterns (aligned with Zod schema)
  const suspiciousPatterns = [
    /script/i,
    /javascript/i,
    /vbscript/i,
    /onload/i,
    /onerror/i,
    /eval\(/i,
    /expression\(/i,
    /<.*>/,
    /alert\(/i,
    /document\./i,
    /window\./i,
    /select\s+.*\s+from/i,
    /union\s+select/i,
    /insert\s+into/i,
    /delete\s+from/i,
    /update\s+.*\s+set/i,
    /drop\s+table/i
  ];

  const hasSuspiciousContent = suspiciousPatterns.some(pattern => pattern.test(sanitized));

  return {
    isValid: !hasSuspiciousContent,
    sanitized
  };
};

/**
 * Validates filter parameters for API endpoints
 */
export const validateFilterParams = (filters: Record<string, string | null>): Record<string, string> => {
  const sanitizedFilters: Record<string, string> = {};

  Object.entries(filters).forEach(([key, value]) => {
    if (value && typeof value === 'string') {
      const sanitized = sanitizeString(value, 100);
      if (sanitized.length > 0) {
        sanitizedFilters[key] = sanitized;
      }
    }
  });

  return sanitizedFilters;
};

/**
 * Validates pagination parameters
 */
export const validatePaginationParams = (
  page: string | null,
  limit: string | null
): { page: number; limit: number } => {
  const parsedPage = Math.max(1, parseInt(page || '1', 10) || 1);
  const parsedLimit = Math.min(100, Math.max(1, parseInt(limit || '20', 10) || 20));

  return {
    page: Math.min(1000, parsedPage), // Cap at 1000 pages
    limit: parsedLimit
  };
};

/**
 * Validates email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 255;
};

/**
 * Validates phone number format
 */
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^[\d\s\-\+\(\)]+$/;
  return phoneRegex.test(phone) && phone.length <= 20;
};

/**
 * Content Security Policy helper
 */
export const getSecurityHeaders = () => {
  return {
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Note: Consider removing unsafe-* in production
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self'",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; '),
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'X-XSS-Protection': '1; mode=block'
  };
};

/**
 * Rate limiting helper
 */
export const createRateLimitKey = (identifier: string, endpoint: string): string => {
  return `rate_limit:${sanitizeString(endpoint, 50)}:${sanitizeString(identifier, 100)}`;
};

/**
 * Safe object property access
 */
export const safeGet = (obj: any, path: string, defaultValue: any = null): any => {
  try {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : defaultValue;
    }, obj);
  } catch {
    return defaultValue;
  }
};

/**
 * Escape special characters for use in regular expressions
 */
export const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

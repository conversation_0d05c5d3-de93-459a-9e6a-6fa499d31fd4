/**
 * Enhanced utility functions for handling product images with resilience features
 */

// Configuration for image handling
const IMAGE_UTILS_CONFIG = {
    // Samsung image server patterns
    SAMSUNG_DOMAINS: ['images.samsung.com'],

    // Fallback image services - standardized on placehold.co for consistency
    FALLBACK_SERVICES: {
        primary: 'https://placehold.co',
        secondary: 'https://placehold.co',
        tertiary: 'https://placehold.co'
    },

    // Image quality settings
    DEFAULT_QUALITY: 85,
    FALLBACK_QUALITY: 75,

    // Common invalid image patterns
    INVALID_PATTERNS: [
        'URL_to_image_1',
        'image_url_1.jpg',
        'example.com',
        'blank.html',
        'placeholder-product.png',
        'no-image',
        'default.jpg',
        'missing.png'
    ]
};

/**
 * Constructs the full URL for a product image with validation
 * @param image - The image path from the database
 * @param supabaseUrl - The Supabase URL from environment variables
 * @returns The complete URL for the image
 */
export function getProductImageUrl(image: string, supabaseUrl: string): string {
    // Validate input
    if (!image || typeof image !== 'string') {
        throw new Error('Invalid image path provided');
    }

    // Check for invalid patterns
    if (isInvalidImagePath(image)) {
        throw new Error(`Invalid image pattern detected: ${image}`);
    }

    // If it's already a full URL, return it as is
    if (image.startsWith('http')) {
        return image;
    }

    // Construct Supabase storage URL
    return `${supabaseUrl}/storage/v1/object/public/${image}`;
}

/**
 * Check if an image path contains invalid patterns
 * @param imagePath - The image path to validate
 * @returns True if the path contains invalid patterns
 */
export function isInvalidImagePath(imagePath: string): boolean {
    if (!imagePath || typeof imagePath !== 'string') {
        return true;
    }

    return IMAGE_UTILS_CONFIG.INVALID_PATTERNS.some(pattern =>
        imagePath.toLowerCase().includes(pattern.toLowerCase())
    );
}

/**
 * Check if an image URL is from Samsung's servers
 * @param url - The image URL to check
 * @returns True if the URL is from Samsung's image servers
 */
export function isSamsungImage(url: string): boolean {
    if (!url || typeof url !== 'string') {
        return false;
    }

    return IMAGE_UTILS_CONFIG.SAMSUNG_DOMAINS.some(domain =>
        url.toLowerCase().includes(domain.toLowerCase())
    );
}

/**
 * Extract product information from Samsung image URLs for better fallback text
 * @param url - Samsung image URL
 * @returns Extracted product information
 */
export function extractSamsungProductInfo(url: string): { model?: string; category?: string } {
    try {
        // Extract model number from Samsung URLs
        const modelMatch = url.match(/\/([a-z0-9-]+)\/gallery\//i);
        const model = modelMatch?.[1]?.replace(/-/g, ' ').toUpperCase();

        // Extract category hints from URL
        let category = 'Product';
        if (url.includes('refrigerat') || url.includes('fridge')) category = 'Refrigerator';
        else if (url.includes('washing') || url.includes('washer')) category = 'Washing Machine';
        else if (url.includes('dishwash')) category = 'Dishwasher';
        else if (url.includes('oven') || url.includes('microwave')) category = 'Oven';
        else if (url.includes('vacuum')) category = 'Vacuum';
        else if (url.includes('tv') || url.includes('display')) category = 'TV';

        return { model, category };
    } catch (error) {
        console.warn('Error extracting Samsung product info:', error);
        return { category: 'Samsung Product' };
    }
}

/**
 * Gets an enhanced fallback image URL with multiple fallback options
 * @param options - Configuration for fallback image generation
 * @returns A fallback image URL with enhanced features
 */
export function getFallbackImageUrl(options: {
    brandLogoUrl?: string;
    productName?: string;
    brandName?: string;
    originalUrl?: string;
    width?: number;
    height?: number;
    quality?: number;
    fallbackLevel?: 'primary' | 'secondary' | 'tertiary';
}): string {
    const {
        brandLogoUrl,
        productName,
        brandName,
        originalUrl,
        width = 600,
        height = 600,
        quality = IMAGE_UTILS_CONFIG.FALLBACK_QUALITY,
        fallbackLevel = 'primary'
    } = options;

    // First try brand logo if available
    if (brandLogoUrl && !isInvalidImagePath(brandLogoUrl)) {
        return brandLogoUrl;
    }

    // Generate enhanced placeholder text
    let placeholderText = 'Product Image';

    if (originalUrl && isSamsungImage(originalUrl)) {
        const samsungInfo = extractSamsungProductInfo(originalUrl);
        if (samsungInfo.model) {
            placeholderText = samsungInfo.model;
        } else if (samsungInfo.category) {
            placeholderText = `Samsung ${samsungInfo.category}`;
        }
    } else if (productName) {
        // Truncate long product names for better display
        placeholderText = productName.length > 30
            ? `${productName.substring(0, 27)}...`
            : productName;
    } else if (brandName) {
        placeholderText = `${brandName} Product`;
    }

    const encodedText = encodeURIComponent(placeholderText);

    // Select fallback service based on level
    const service = IMAGE_UTILS_CONFIG.FALLBACK_SERVICES[fallbackLevel];

    // Generate color scheme based on brand
    let bgColor = 'f1f5f9'; // Default light gray
    let textColor = '64748b'; // Default dark gray

    if (brandName?.toLowerCase().includes('samsung')) {
        bgColor = '1f2937'; // Samsung dark blue
        textColor = 'ffffff'; // White text
    }

    return `${service}/${width}x${height}/${bgColor}/${textColor}.png?text=${encodedText}`;
}

/**
 * Creates an enhanced error handler for image loading with detailed logging
 * @param onError - Optional callback for additional error handling
 * @param debugInfo - Optional debug information to log
 * @returns A function to handle image loading errors
 */
export function createImageErrorHandler(
    onError?: (error: string) => void,
    debugInfo?: Record<string, any>
): (e: React.SyntheticEvent<HTMLImageElement, Event>) => void {
    return (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
        const target = e.target as HTMLImageElement;
        const errorDetails = {
            attemptedSrc: target.src,
            naturalWidth: target.naturalWidth,
            naturalHeight: target.naturalHeight,
            complete: target.complete,
            isSamsungImage: isSamsungImage(target.src),
            timestamp: new Date().toISOString(),
            ...debugInfo
        };

        console.error('Enhanced image load error:', errorDetails);

        // Call the error callback with a descriptive message
        const errorMessage = isSamsungImage(target.src)
            ? 'Samsung image server timeout or unavailable'
            : 'Image failed to load';

        onError?.(errorMessage);
    };
}

/**
 * Validate image URL and provide fallback recommendations
 * @param imageUrl - The image URL to validate
 * @param options - Additional options for validation
 * @returns Validation result with recommendations
 */
export async function validateImageWithFallback(
    imageUrl: string,
    options: {
        productName?: string;
        brandName?: string;
        brandLogoUrl?: string;
        timeoutMs?: number;
    } = {}
): Promise<{
    isValid: boolean;
    originalUrl: string;
    fallbackUrl?: string;
    error?: string;
    recommendations: string[];
}> {
    const { productName, brandName, brandLogoUrl, timeoutMs = 5000 } = options;
    const recommendations: string[] = [];

    // Basic validation
    if (!imageUrl || typeof imageUrl !== 'string') {
        return {
            isValid: false,
            originalUrl: imageUrl,
            fallbackUrl: getFallbackImageUrl({ productName, brandName, brandLogoUrl }),
            error: 'Invalid image URL',
            recommendations: ['Provide a valid image URL']
        };
    }

    // Check for invalid patterns
    if (isInvalidImagePath(imageUrl)) {
        recommendations.push('Remove placeholder or invalid image paths from database');
        return {
            isValid: false,
            originalUrl: imageUrl,
            fallbackUrl: getFallbackImageUrl({ productName, brandName, brandLogoUrl, originalUrl: imageUrl }),
            error: 'Invalid image pattern detected',
            recommendations
        };
    }

    // For Samsung images, add specific recommendations
    if (isSamsungImage(imageUrl)) {
        recommendations.push(
            'Consider caching Samsung images locally to avoid external dependencies',
            'Implement image preloading for critical product images',
            'Use WebP format for better compression'
        );

        // Quick validation for Samsung images (they're known to be slow)
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

            const response = await fetch(imageUrl, {
                method: 'HEAD',
                signal: controller.signal,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (compatible; ImageValidator/1.0)'
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return {
                isValid: true,
                originalUrl: imageUrl,
                recommendations
            };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            recommendations.push('Samsung image server appears to be slow or unavailable');

            return {
                isValid: false,
                originalUrl: imageUrl,
                fallbackUrl: getFallbackImageUrl({
                    productName,
                    brandName,
                    brandLogoUrl,
                    originalUrl: imageUrl,
                    fallbackLevel: 'primary' // Use primary service (placehold.co) for consistency
                }),
                error: errorMessage,
                recommendations
            };
        }
    }

    // For other images, assume they're valid (local Supabase images, etc.)
    return {
        isValid: true,
        originalUrl: imageUrl,
        recommendations: ['Image appears to be from a reliable source']
    };
}

/**
 * Get image optimization recommendations based on URL analysis
 * @param imageUrl - The image URL to analyze
 * @returns Array of optimization recommendations
 */
export function getImageOptimizationRecommendations(imageUrl: string): string[] {
    const recommendations: string[] = [];

    if (isSamsungImage(imageUrl)) {
        recommendations.push(
            'Samsung images are external and may be slow - consider local caching',
            'Implement lazy loading for non-critical Samsung images',
            'Use lower quality settings for Samsung images to improve load times',
            'Implement circuit breaker pattern for Samsung image requests'
        );
    }

    if (imageUrl.includes('?')) {
        recommendations.push('URL contains query parameters - ensure they are necessary');
    }

    if (!imageUrl.includes('webp') && !imageUrl.includes('avif')) {
        recommendations.push('Consider using modern image formats (WebP, AVIF) for better compression');
    }

    return recommendations;
}

/**
 * Server-side promotions data access layer
 * 
 * This module provides server-side functions for fetching and transforming
 * promotion data from Supabase. Optimized for SEO and performance.
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import { createCachedFunction, CACHE_DURATIONS, CACHE_TAGS } from '@/lib/cache';
import type {
  Promotion,
  TransformedPromotion,
  PaginatedResponse,
} from './types';

/**
 * Transform raw promotion data from database to API format
 */
function transformPromotion(rawPromotion: any): TransformedPromotion {
  const now = new Date();
  const purchaseEndDate = new Date(rawPromotion.purchase_end_date);
  const isExpired = purchaseEndDate < now;
  
  // Calculate time remaining if not expired
  let timeRemaining = null;
  if (!isExpired) {
    const diff = purchaseEndDate.getTime() - now.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    timeRemaining = { days, hours, minutes, seconds };
  }

  // Format dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return {
    id: rawPromotion.id,
    title: rawPromotion.title,
    description: rawPromotion.description,
    maxCashbackAmount: rawPromotion.max_cashback_amount,
    purchaseStartDate: rawPromotion.purchase_start_date,
    purchaseEndDate: rawPromotion.purchase_end_date,
    claimStartOffsetDays: rawPromotion.claim_start_offset_days || 0,
    claimWindowDays: rawPromotion.claim_window_days || 30, // Default 30 days claim window
    termsUrl: rawPromotion.terms_url,
    termsDescription: rawPromotion.terms_description,
    status: rawPromotion.status || 'active',
    isFeatured: rawPromotion.is_featured || false,
    brand: rawPromotion.brand || null,
    category: rawPromotion.category || null,
    isActive: !isExpired && rawPromotion.status === 'active',
    isExpired,
    timeRemaining,
    formattedPurchaseDateRange: `${formatDate(rawPromotion.purchase_start_date)} - ${formatDate(rawPromotion.purchase_end_date)}`,
    formattedExpiryDate: formatDate(rawPromotion.purchase_end_date),
  }
}

/**
 * Get a single promotion by ID
 */
async function _getPromotion(supabase: SupabaseClient, id: string): Promise<TransformedPromotion | null> {
  try {
    const { data, error } = await supabase
      .from('promotions')
      .select(`
        *,
        brand:brand_id (id, name, slug, logo_url),
        category:category_id (id, name, slug)
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching promotion:', error)
      return null
    }

    return transformPromotion(data)
  } catch (error) {
    console.error('Exception in getPromotion:', error)
    return null
  }
}

/**
 * Cached version of getPromotion
 */
export const getPromotion = createCachedFunction(
  _getPromotion,
  {
    key: 'getPromotion',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.PROMOTION, CACHE_TAGS.PROMOTIONS],
  }
)

/**
 * Get featured promotions for homepage
 */
async function _getFeaturedPromotions(supabase: SupabaseClient, limit = 3): Promise<TransformedPromotion[]> {
  try {
    const { data, error } = await supabase
      .from('promotions')
      .select(`
        *,
        brand:brand_id (id, name, slug, logo_url),
        category:category_id (id, name, slug)
      `)
      .eq('is_featured', true)
      .eq('status', 'active')
      .gte('purchase_end_date', new Date().toISOString().split('T')[0])
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching featured promotions:', error)
      return []
    }

    return (data || []).map(transformPromotion)
  } catch (error) {
    console.error('Exception in getFeaturedPromotions:', error)
    return []
  }
}

/**
 * Cached version of getFeaturedPromotions
 */
export const getFeaturedPromotions = createCachedFunction(
  _getFeaturedPromotions,
  {
    key: 'getFeaturedPromotions',
    revalidate: CACHE_DURATIONS.SHORT,
    tags: [CACHE_TAGS.FEATURED, CACHE_TAGS.PROMOTIONS],
  }
)

/**
 * Get active promotions with pagination
 */
async function _getActivePromotions(supabase: SupabaseClient, page = 1, limit = 20): Promise<PaginatedResponse<TransformedPromotion>> {
  try {
    const offset = (page - 1) * limit
    const today = new Date().toISOString().split('T')[0]

    const { data, error, count } = await supabase
      .from('promotions')
      .select(`
        *,
        brand:brand_id (id, name, slug, logo_url),
        category:category_id (id, name, slug)
      `, { count: 'exact' })
      .eq('status', 'active')
      .gte('purchase_end_date', today)
      .order('is_featured', { ascending: false })
      .order('purchase_end_date', { ascending: true })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching active promotions:', error)
      throw new Error(`Failed to fetch active promotions: ${error.message}`)
    }

    const transformedPromotions = (data || []).map(transformPromotion)
    const total = count || 0
    const totalPages = Math.ceil(total / limit)

    return {
      data: transformedPromotions,
      pagination: {
        page,
        pageSize: limit,
        total,
        totalPages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    }
  } catch (error) {
    console.error('Exception in getActivePromotions:', error)
    throw error
  }
}

/**
 * Cached version of getActivePromotions
 */
export const getActivePromotions = createCachedFunction(
  _getActivePromotions,
  {
    key: 'getActivePromotions',
    revalidate: CACHE_DURATIONS.SHORT,
    tags: [CACHE_TAGS.PROMOTIONS],
  }
)

/**
 * Get promotions by brand ID
 */
async function _getPromotionsByBrand(supabase: SupabaseClient, brandId: string, limit = 10): Promise<TransformedPromotion[]> {
  try {
    const today = new Date().toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('promotions')
      .select(`
        *,
        brand:brand_id (id, name, slug, logo_url),
        category:category_id (id, name, slug)
      `)
      .eq('brand_id', brandId)
      .eq('status', 'active')
      .gte('purchase_end_date', today)
      .order('is_featured', { ascending: false })
      .order('purchase_end_date', { ascending: true })
      .limit(limit)

    if (error) {
      console.error('Error fetching promotions by brand:', error)
      return []
    }

    return (data || []).map(transformPromotion)
  } catch (error) {
    console.error('Exception in getPromotionsByBrand:', error)
    return []
  }
}

/**
 * Cached version of getPromotionsByBrand
 */
export const getPromotionsByBrand = createCachedFunction(
  _getPromotionsByBrand,
  {
    key: 'getPromotionsByBrand',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.PROMOTIONS, CACHE_TAGS.BRAND],
  }
)

/**
 * Get promotions by category ID
 */
async function _getPromotionsByCategory(supabase: SupabaseClient, categoryId: string, limit = 10): Promise<TransformedPromotion[]> {
  try {
    const today = new Date().toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('promotions')
      .select(`
        *,
        brand:brand_id (id, name, slug, logo_url),
        category:category_id (id, name, slug)
      `)
      .eq('category_id', categoryId)
      .eq('status', 'active')
      .gte('purchase_end_date', today)
      .order('is_featured', { ascending: false })
      .order('purchase_end_date', { ascending: true })
      .limit(limit)

    if (error) {
      console.error('Error fetching promotions by category:', error)
      return []
    }

    return (data || []).map(transformPromotion)
  } catch (error) {
    console.error('Exception in getPromotionsByCategory:', error)
    return []
  }
}

/**
 * Cached version of getPromotionsByCategory
 */
export const getPromotionsByCategory = createCachedFunction(
  _getPromotionsByCategory,
  {
    key: 'getPromotionsByCategory',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.PROMOTIONS, CACHE_TAGS.CATEGORY],
  }
)

/**
 * Get all promotions with filtering and pagination
 */
async function _getPromotions(
  supabase: SupabaseClient,
  filters: { brand_id?: string; category_id?: string; status?: string } = {},
  page = 1,
  limit = 20
): Promise<PaginatedResponse<TransformedPromotion>> {
  try {
    const offset = (page - 1) * limit

    let query = supabase
      .from('promotions')
      .select(`
        *,
        brand:brand_id (id, name, slug, logo_url),
        category:category_id (id, name, slug)
      `, { count: 'exact' })

    // Apply filters
    if (filters.brand_id) {
      query = query.eq('brand_id', filters.brand_id)
    }
    if (filters.category_id) {
      query = query.eq('category_id', filters.category_id)
    }
    if (filters.status) {
      query = query.eq('status', filters.status)
    } else {
      query = query.eq('status', 'active')
    }

    // Only show current and future promotions
    const today = new Date().toISOString().split('T')[0]
    query = query.gte('purchase_end_date', today)

    // Apply pagination and sorting
    query = query
      .order('is_featured', { ascending: false })
      .order('purchase_end_date', { ascending: true })
      .range(offset, offset + limit - 1)

    const { data, error, count } = await query

    if (error) {
      console.error('Error fetching promotions:', error)
      throw new Error(`Failed to fetch promotions: ${error.message}`)
    }

    const transformedPromotions = (data || []).map(transformPromotion)
    const total = count || 0
    const totalPages = Math.ceil(total / limit)

    return {
      data: transformedPromotions,
      pagination: {
        page,
        pageSize: limit,
        total,
        totalPages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    }
  } catch (error) {
    console.error('Exception in getPromotions:', error)
    throw error
  }
}

/**
 * Cached version of getPromotions
 */
export const getPromotions = createCachedFunction(
  _getPromotions,
  {
    key: 'getPromotions',
    revalidate: CACHE_DURATIONS.SHORT,
    tags: [CACHE_TAGS.PROMOTIONS],
  }
)

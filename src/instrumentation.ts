import * as Sentry from '@sentry/nextjs';

// Environment-based Sentry initialization
const ENABLE_SENTRY = process.env.ENABLE_SENTRY === 'true';
const ENABLE_SENTRY_LOCAL = process.env.ENABLE_SENTRY_LOCAL === 'true';

// Determine if Sentry should be loaded
const shouldLoadSentry = () => {
  // In production, require ENABLE_SENTRY=true
  if (process.env.NODE_ENV === 'production') {
    return ENABLE_SENTRY;
  }

  // In development, require explicit ENABLE_SENTRY_LOCAL=true
  if (process.env.NODE_ENV === 'development') {
    return ENABLE_SENTRY_LOCAL;
  }

  // For other environments (test, staging), follow ENABLE_SENTRY
  return ENABLE_SENTRY;
};

export async function register() {
  // Only load Sentry configuration if enabled
  if (shouldLoadSentry()) {
    if (process.env.NEXT_RUNTIME === 'nodejs') {
      await import('../sentry.server.config');
    }

    if (process.env.NEXT_RUNTIME === 'edge') {
      await import('../sentry.edge.config');
    }
  } else {
    console.log('[Sentry] Instrumentation disabled via environment configuration');
  }
}

// Only export Sentry error handler if Sentry is enabled
export const onRequestError = shouldLoadSentry()
  ? Sentry.captureRequestError
  : (error: unknown, _request: Request) => {
      // Fallback error logging when Sentry is disabled
      console.error('[Error] Request error (Sentry disabled):', error);
    };

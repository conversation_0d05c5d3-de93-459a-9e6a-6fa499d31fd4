// src/__tests__/security/hmac-security.test.ts
// Security tests for HMAC authentication - attack scenarios and edge cases

import { NextRequest } from 'next/server'
import { generateHMACSignature, verifyRequestHMAC } from '@/lib/security/hmac'
import { authenticateSearchRequest } from '@/lib/security/auth-middleware'

// Test configuration
const TEST_SECRET = 'test-secret-minimum-32-characters-long'
const TEST_PARTNER_ID = 'test-partner'

// Setup test environment
beforeAll(() => {
  process.env.PARTNER_SECRET_TEST_PARTNER = TEST_SECRET
  process.env.PARTNER_SECRET_DEFAULT = TEST_SECRET
  process.env.HMAC_TIMESTAMP_WINDOW = '300'
  process.env.ENABLE_SEARCH_AUTH = 'true'
  process.env.ENABLE_HMAC_AUTH = 'true'
})

afterAll(() => {
  delete process.env.PARTNER_SECRET_TEST_PARTNER
  delete process.env.PARTNER_SECRET_DEFAULT
  delete process.env.HMAC_TIMESTAMP_WINDOW
  delete process.env.ENABLE_SEARCH_AUTH
  delete process.env.ENABLE_HMAC_AUTH
})

describe('HMAC Security Tests', () => {
  describe('Replay Attack Protection', () => {
    it('rejects replayed request with old timestamp', async () => {
      const oldTimestamp = Math.floor(Date.now() / 1000) - 400 // 400 seconds ago
      const signature = generateHMACSignature('GET', '/api/search', oldTimestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': oldTimestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
    
    it('rejects future timestamp beyond window', async () => {
      const futureTimestamp = Math.floor(Date.now() / 1000) + 400 // 400 seconds in future
      const signature = generateHMACSignature('GET', '/api/search', futureTimestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': futureTimestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })

    it('detects and blocks duplicate requests (replay protection)', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      // First request should succeed
      const result1 = await verifyRequestHMAC(request)
      expect(result1).not.toBeNull()
      
      // Second identical request should be rejected as replay
      const result2 = await verifyRequestHMAC(request)
      expect(result2).toBeNull()
    })

    it('allows requests with same timestamp but different signatures', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      
      // First request to /api/search
      const signature1 = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
      const request1 = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${signature1}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      // Second request to /api/search/suggestions (different path, different signature)
      const signature2 = generateHMACSignature('GET', '/api/search/suggestions', timestamp, '', TEST_SECRET)
      const request2 = new NextRequest('http://localhost/api/search/suggestions?q=lap', {
        headers: {
          'X-Signature': `sha256=${signature2}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      // Both should succeed since they have different signatures
      const result1 = await verifyRequestHMAC(request1)
      const result2 = await verifyRequestHMAC(request2)
      
      expect(result1).not.toBeNull()
      expect(result2).not.toBeNull()
    })
  })
  
  describe('Signature Tampering Protection', () => {
    it('rejects modified signature', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const validSignature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
      const tamperedSignature = validSignature.slice(0, -1) + '0' // Change last character
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${tamperedSignature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
    
    it('rejects signature for different path', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search/suggestions', timestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      // Use signature for /suggestions on /search endpoint
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
    
    it('rejects signature with mismatched body hash', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      
      // Generate signature with empty body
      const signature = generateHMACSignature('POST', '/api/search', timestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search', {
        method: 'POST',
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query: 'laptop', filters: { brand: 'samsung' } })
      })
      
      // Should fail because signature was for empty body but request has JSON body
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
    
    it('rejects signature with different method', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      
      // Generate signature for GET request
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search', {
        method: 'POST',
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        },
        body: JSON.stringify({ query: 'laptop' })
      })
      
      // Use GET signature on POST request
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })

    it('rejects signature with different query parameters', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      
      // Generate signature for specific query
      const signature = generateHMACSignature('GET', '/api/search?q=laptop', timestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search?q=phone', {
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      // Use signature for different query parameters
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
  })
  
  describe('Partner Validation', () => {
    it('rejects unknown partner ID', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'unknown-secret')
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': 'unknown-partner'
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
    
    it('rejects partner with short secret', async () => {
      // Set up partner with short secret
      process.env.PARTNER_SECRET_INVALID_PARTNER = 'short'
      
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'short')
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': 'invalid-partner'
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
      
      // Clean up
      delete process.env.PARTNER_SECRET_INVALID_PARTNER
    })

    it('handles partner ID with special characters', async () => {
      // Set up partner with special characters in ID
      process.env.PARTNER_SECRET_SPECIAL_PARTNER_123 = TEST_SECRET
      
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': 'special-partner-123'
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      
      // Clean up
      delete process.env.PARTNER_SECRET_SPECIAL_PARTNER_123
    })
  })

  describe('Header Injection Attacks', () => {
    it('rejects requests with malformed signature header', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': 'malformed-signature-without-sha256-prefix',
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })

    it('rejects requests with non-numeric timestamp', async () => {
      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': 'not-a-number',
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })

    it('rejects requests with extremely large timestamp', async () => {
      const largeTimestamp = '9999999999999999999' // Way beyond reasonable timestamp
      const signature = generateHMACSignature('GET', '/api/search', parseInt(largeTimestamp), '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${signature}`,
          'X-Timestamp': largeTimestamp,
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })

    it('handles empty header values gracefully', async () => {
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': '',
          'X-Timestamp': '',
          'X-Partner-ID': ''
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
  })

  describe('Timing Attack Protection', () => {
    it('signature verification takes consistent time for valid and invalid signatures', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const validSignature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
      const invalidSignature = 'a'.repeat(64) // Invalid but same length
      
      // Measure time for valid signature
      const validStart = performance.now()
      const validRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${validSignature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      await verifyRequestHMAC(validRequest)
      const validTime = performance.now() - validStart
      
      // Measure time for invalid signature
      const invalidStart = performance.now()
      const invalidRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': `sha256=${invalidSignature}`,
          'X-Timestamp': timestamp.toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      await verifyRequestHMAC(invalidRequest)
      const invalidTime = performance.now() - invalidStart
      
      // Time difference should be minimal (within 50% of each other)
      const timeDifference = Math.abs(validTime - invalidTime)
      const averageTime = (validTime + invalidTime) / 2
      const percentageDifference = (timeDifference / averageTime) * 100
      
      expect(percentageDifference).toBeLessThan(50)
    })
  })

  describe('Resource Exhaustion Protection', () => {
    it('handles multiple concurrent invalid requests efficiently', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      
      // Create 50 concurrent invalid requests
      const requests = Array.from({ length: 50 }, (_, i) => {
        return new NextRequest(`http://localhost/api/search?q=test${i}`, {
          headers: {
            'X-Signature': `sha256=${'invalid'.repeat(16)}`, // Invalid signature
            'X-Timestamp': timestamp.toString(),
            'X-Partner-ID': TEST_PARTNER_ID
          }
        })
      })
      
      const startTime = performance.now()
      const results = await Promise.all(requests.map(verifyRequestHMAC))
      const endTime = performance.now()
      
      // All should fail
      expect(results.every(result => result === null)).toBe(true)
      
      // Should complete within reasonable time (less than 1 second for 50 requests)
      expect(endTime - startTime).toBeLessThan(1000)
    })
  })
})

/**
 * @jest-environment node
 */

/**
 * Enhanced Rate Limiting Tests
 *
 * Tests for updated rate limiting functionality including:
 * - Contact form rate limiting (5 requests per 10 minutes)
 * - Search API rate limiting (100 requests per minute)
 * - General API rate limiting
 * - Integration with Turnstile CAPTCHA
 */



import { rateLimits } from '@/lib/rateLimiter';

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.warn = jest.fn();
  console.log = jest.fn();
});

afterAll(() => {
  console.warn = originalConsole.warn;
  console.log = originalConsole.log;
});

describe('Updated Rate Limiting Configuration', () => {
  beforeEach(() => {
    // Clear rate limit store before each test
    jest.clearAllMocks();
  });

  test('should have correct contact form rate limit (5 requests per 10 minutes)', () => {
    expect(rateLimits.contact.maxRequests).toBe(5);
    expect(rateLimits.contact.windowSizeInSeconds).toBe(600); // 10 minutes
    expect(rateLimits.contact.identifier).toBe('contact');
  });

  test('should have correct search rate limit (100 requests per minute)', () => {
    expect(rateLimits.search.maxRequests).toBe(100);
    expect(rateLimits.search.windowSizeInSeconds).toBe(60); // 1 minute
    expect(rateLimits.search.identifier).toBe('search');
  });

  test('should maintain other API rate limits', () => {
    expect(rateLimits.product.maxRequests).toBe(30);
    expect(rateLimits.brands.maxRequests).toBe(40);
    expect(rateLimits.retailers.maxRequests).toBe(35);
  });
});

describe('Contact Form Rate Limiting Configuration', () => {
  test('should have correct contact form rate limit settings', () => {
    expect(rateLimits.contact.maxRequests).toBe(5);
    expect(rateLimits.contact.windowSizeInSeconds).toBe(600); // 10 minutes
    expect(rateLimits.contact.identifier).toBe('contact');
  });

  test('should have correct search rate limit settings', () => {
    expect(rateLimits.search.maxRequests).toBe(100);
    expect(rateLimits.search.windowSizeInSeconds).toBe(60); // 1 minute
    expect(rateLimits.search.identifier).toBe('search');
  });

  test('should maintain other API rate limits', () => {
    expect(rateLimits.product.maxRequests).toBe(30);
    expect(rateLimits.brands.maxRequests).toBe(40);
    expect(rateLimits.retailers.maxRequests).toBe(35);
  });

});

describe('Rate Limiting Utility Functions', () => {
  test('should export all required rate limit configurations', () => {
    expect(rateLimits).toHaveProperty('contact');
    expect(rateLimits).toHaveProperty('search');
    expect(rateLimits).toHaveProperty('product');
    expect(rateLimits).toHaveProperty('brands');
    expect(rateLimits).toHaveProperty('retailers');
    expect(rateLimits).toHaveProperty('default');
  });

  test('should have valid rate limit configuration structure', () => {
    Object.values(rateLimits).forEach(config => {
      expect(config).toHaveProperty('maxRequests');
      expect(config).toHaveProperty('windowSizeInSeconds');
      expect(config).toHaveProperty('identifier');
      expect(typeof config.maxRequests).toBe('number');
      expect(typeof config.windowSizeInSeconds).toBe('number');
      expect(typeof config.identifier).toBe('string');
      expect(config.maxRequests).toBeGreaterThan(0);
      expect(config.windowSizeInSeconds).toBeGreaterThan(0);
    });
  });
});

describe('Security Integration', () => {
  test('should have appropriate rate limits for security', () => {
    // Contact form should have the strictest limits (potential for spam)
    expect(rateLimits.contact.maxRequests).toBeLessThanOrEqual(5);
    expect(rateLimits.contact.windowSizeInSeconds).toBeGreaterThanOrEqual(600);

    // Search should allow reasonable user activity
    expect(rateLimits.search.maxRequests).toBeGreaterThanOrEqual(50);
    expect(rateLimits.search.windowSizeInSeconds).toBeLessThanOrEqual(60);
  });

  test('should have unique identifiers for each endpoint', () => {
    const identifiers = Object.values(rateLimits).map(config => config.identifier);
    const uniqueIdentifiers = new Set(identifiers);
    expect(uniqueIdentifiers.size).toBe(identifiers.length);
  });
});

// src/__tests__/integration/jwt-hmac-compatibility.test.ts
// Compatibility tests for JWT + HMAC coexistence

import { NextRequest } from 'next/server'
import { createJWT, verifyRequestJWT } from '@/lib/security/jwt'
import { createHMACHeaders, clearReplayCache } from '@/lib/security/hmac'
import { authenticateSearchRequest } from '@/lib/security/auth-middleware'

// Test configuration
const TEST_SECRET = 'test-secret-minimum-32-characters-long'
const TEST_PARTNER_ID = 'test-partner'

// Setup test environment
beforeAll(() => {
  process.env.JWT_SECRET = 'test-jwt-secret-minimum-32-characters-long'
  process.env.PARTNER_SECRET_TEST_PARTNER = TEST_SECRET
  process.env.PARTNER_SECRET_DEFAULT = TEST_SECRET
  process.env.HMAC_TIMESTAMP_WINDOW = '300'
  process.env.ENABLE_SEARCH_AUTH = 'true'
  process.env.ENABLE_HMAC_AUTH = 'true'
})

beforeEach(() => {
  // Clear replay cache to avoid conflicts between tests
  clearReplayCache()
})

afterAll(() => {
  delete process.env.JWT_SECRET
  delete process.env.PARTNER_SECRET_TEST_PARTNER
  delete process.env.PARTNER_SECRET_DEFAULT
  delete process.env.HMAC_TIMESTAMP_WINDOW
  delete process.env.ENABLE_SEARCH_AUTH
  delete process.env.ENABLE_HMAC_AUTH
})

describe('JWT + HMAC Compatibility', () => {
  describe('Authentication Priority', () => {
    it('JWT takes precedence when both are present and valid', async () => {
      const jwt = await createJWT()
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': `Bearer ${jwt}`,
          ...hmacHeaders
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('JWT') // JWT should take precedence
      expect(authResult.payload).toHaveProperty('sub')
    })
    
    it('falls back to HMAC when JWT is invalid but HMAC is valid', async () => {
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': 'Bearer invalid-jwt',
          ...hmacHeaders
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('HMAC') // Should fall back to HMAC
      expect(authResult.payload).toHaveProperty('partnerId')
    })

    it('falls back to HMAC when JWT is missing but HMAC is valid', async () => {
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: hmacHeaders
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('HMAC')
    })

    it('rejects when both JWT and HMAC are invalid', async () => {
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': 'Bearer invalid-jwt',
          'X-Signature': 'sha256=invalid-signature',
          'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
      expect(authResult.method).toBeNull()
    })
  })

  describe('Backward Compatibility', () => {
    it('existing JWT functionality remains unchanged', async () => {
      // Test that PR 1 JWT functionality still works exactly as before
      const jwt = await createJWT()
      const request = new NextRequest('http://localhost/api/contact', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${jwt}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: 'Test User',
          email: '<EMAIL>',
          message: 'Test message'
        })
      })
      
      // Verify JWT directly (as it would work in contact endpoint)
      const jwtPayload = await verifyRequestJWT(request)
      expect(jwtPayload).not.toBeNull()
      expect(jwtPayload).toHaveProperty('sub')
    })

    it('JWT authentication works independently of HMAC', async () => {
      // Disable HMAC temporarily
      process.env.ENABLE_HMAC_AUTH = 'false'
      
      const jwt = await createJWT()
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Authorization': `Bearer ${jwt}` }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('JWT')
      
      // Re-enable HMAC
      process.env.ENABLE_HMAC_AUTH = 'true'
    })

    it('HMAC authentication works independently of JWT', async () => {
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers: hmacHeaders })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('HMAC')
    })
  })

  describe('Feature Flag Interactions', () => {
    it('respects ENABLE_SEARCH_AUTH flag for both methods', async () => {
      process.env.ENABLE_SEARCH_AUTH = 'false'
      
      // Both JWT and HMAC should be bypassed
      const jwt = await createJWT()
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      
      const jwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Authorization': `Bearer ${jwt}` }
      })
      
      const hmacRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: hmacHeaders
      })
      
      const noAuthRequest = new NextRequest('http://localhost/api/search?q=laptop')
      
      const jwtResult = await authenticateSearchRequest(jwtRequest)
      const hmacResult = await authenticateSearchRequest(hmacRequest)
      const noAuthResult = await authenticateSearchRequest(noAuthRequest)
      
      // All should succeed when authentication is disabled
      expect(jwtResult.success).toBe(true)
      expect(hmacResult.success).toBe(true)
      expect(noAuthResult.success).toBe(true)
      
      // All should have null method when bypassed
      expect(jwtResult.method).toBeNull()
      expect(hmacResult.method).toBeNull()
      expect(noAuthResult.method).toBeNull()
      
      process.env.ENABLE_SEARCH_AUTH = 'true'
    })

    it('ENABLE_HMAC_AUTH only affects HMAC, not JWT', async () => {
      process.env.ENABLE_HMAC_AUTH = 'false'
      
      const jwt = await createJWT()
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      
      // JWT should still work
      const jwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Authorization': `Bearer ${jwt}` }
      })
      const jwtResult = await authenticateSearchRequest(jwtRequest)
      expect(jwtResult.success).toBe(true)
      expect(jwtResult.method).toBe('JWT')
      
      // HMAC should fail
      const hmacRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: hmacHeaders
      })
      const hmacResult = await authenticateSearchRequest(hmacRequest)
      expect(hmacResult.success).toBe(false)
      
      process.env.ENABLE_HMAC_AUTH = 'true'
    })
  })

  describe('Error Handling Consistency', () => {
    it('provides consistent error responses regardless of authentication method', async () => {
      // Test invalid JWT
      const invalidJwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Authorization': 'Bearer invalid-jwt' }
      })
      const jwtResult = await authenticateSearchRequest(invalidJwtRequest)
      
      // Test invalid HMAC
      const invalidHmacRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': 'sha256=invalid-signature',
          'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      const hmacResult = await authenticateSearchRequest(invalidHmacRequest)
      
      // Test no authentication
      const noAuthRequest = new NextRequest('http://localhost/api/search?q=laptop')
      const noAuthResult = await authenticateSearchRequest(noAuthRequest)
      
      // All should fail with consistent structure
      expect(jwtResult.success).toBe(false)
      expect(hmacResult.success).toBe(false)
      expect(noAuthResult.success).toBe(false)
      
      expect(jwtResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
      expect(hmacResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
      expect(noAuthResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
    })
  })

  describe('Performance Impact', () => {
    it('dual authentication does not significantly impact performance', async () => {
      // Test JWT-only performance
      const jwt = await createJWT()
      const jwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Authorization': `Bearer ${jwt}` }
      })
      
      const jwtStart = performance.now()
      await authenticateSearchRequest(jwtRequest)
      const jwtTime = performance.now() - jwtStart
      
      // Test HMAC-only performance
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      const hmacRequest = new NextRequest('http://localhost/api/search?q=laptop', { headers: hmacHeaders })
      
      const hmacStart = performance.now()
      await authenticateSearchRequest(hmacRequest)
      const hmacTime = performance.now() - hmacStart
      
      // Test dual authentication (JWT + HMAC headers, JWT should win)
      const dualRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': `Bearer ${jwt}`,
          ...hmacHeaders
        }
      })
      
      const dualStart = performance.now()
      await authenticateSearchRequest(dualRequest)
      const dualTime = performance.now() - dualStart
      
      // Dual authentication should not be significantly slower than JWT alone
      expect(dualTime).toBeLessThan(jwtTime * 1.5) // At most 50% slower
      expect(jwtTime).toBeLessThan(10) // JWT should be fast
      expect(hmacTime).toBeLessThan(10) // HMAC should be fast
    })
  })

  describe('Logging and Tracing', () => {
    it('generates unique trace IDs for each authentication attempt', async () => {
      const requests = Array.from({ length: 10 }, () => {
        return new NextRequest('http://localhost/api/search?q=laptop')
      })
      
      const results = await Promise.all(requests.map(authenticateSearchRequest))
      const traceIds = results.map(r => r.traceId)
      
      // All trace IDs should be unique
      const uniqueTraceIds = new Set(traceIds)
      expect(uniqueTraceIds.size).toBe(traceIds.length)
      
      // All should match the expected format
      traceIds.forEach(traceId => {
        expect(traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
      })
    })

    it('maintains trace ID consistency across authentication methods', async () => {
      const jwt = await createJWT()
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      
      // Test with valid JWT
      const jwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Authorization': `Bearer ${jwt}` }
      })
      const jwtResult = await authenticateSearchRequest(jwtRequest)
      
      // Test with valid HMAC
      const hmacRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: hmacHeaders
      })
      const hmacResult = await authenticateSearchRequest(hmacRequest)
      
      // Both should have valid trace IDs
      expect(jwtResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
      expect(hmacResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
      expect(jwtResult.traceId).not.toBe(hmacResult.traceId)
    })
  })

  describe('Edge Cases', () => {
    it('handles malformed Authorization header with valid HMAC', async () => {
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': 'Malformed header format',
          ...hmacHeaders
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('HMAC') // Should fall back to HMAC
    })

    it('handles empty Authorization header with valid HMAC', async () => {
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': '',
          ...hmacHeaders
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('HMAC')
    })

    it('handles valid JWT with malformed HMAC headers', async () => {
      const jwt = await createJWT()
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': `Bearer ${jwt}`,
          'X-Signature': 'malformed',
          'X-Timestamp': 'not-a-number',
          'X-Partner-ID': ''
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('JWT') // JWT should work despite malformed HMAC
    })
  })
})

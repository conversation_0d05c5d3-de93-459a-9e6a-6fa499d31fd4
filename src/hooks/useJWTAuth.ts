// src/hooks/useJWTAuth.ts
// Custom hook for managing JWT authentication in client components

import { useState, useCallback } from 'react'

interface AuthState {
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface UseJWTAuthReturn extends AuthState {
  authenticate: () => Promise<boolean>
  clearError: () => void
}

/**
 * Custom hook for JWT authentication
 * 
 * For search functionality, we use a simplified authentication flow:
 * 1. Check if JWT cookie exists via a verify endpoint
 * 2. If not authenticated, redirect to contact page to complete CAPTCHA
 * 3. Once authenticated, JWT cookies are automatically included in subsequent requests
 */
export function useJWTAuth(): UseJWTAuthReturn {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: false,
    error: null
  })

  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }))
  }, [])

  const authenticate = useCallback(async (): Promise<boolean> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      // Check if we already have a valid JWT cookie
      const verifyResponse = await fetch('/api/auth/verify', {
        method: 'GET',
        credentials: 'include' // Include JWT cookies
      })

      if (verifyResponse.ok) {
        // Already authenticated
        setAuthState({
          isAuthenticated: true,
          isLoading: false,
          error: null
        })
        return true
      }

      // If not authenticated (404 or 401), we need to get a JWT token
      // For now, we'll redirect to contact page where user can complete CAPTCHA
      // In the future, we could implement a dedicated auth endpoint
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        error: 'Authentication required. Please complete CAPTCHA verification.'
      })
      return false

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Authentication failed'
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        error: errorMessage
      })
      return false
    }
  }, [])

  return {
    ...authState,
    authenticate,
    clearError
  }
}

/**
 * Helper function to make authenticated API requests
 * Automatically includes JWT cookies and handles authentication errors
 */
export async function makeAuthenticatedRequest(
  url: string, 
  options: RequestInit = {}
): Promise<Response> {
  const response = await fetch(url, {
    ...options,
    credentials: 'include', // Always include JWT cookies
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    }
  })

  if (response.status === 401) {
    // Authentication failed - JWT token is missing or invalid
    throw new Error('Authentication required. Please complete CAPTCHA verification.')
  }

  return response
}
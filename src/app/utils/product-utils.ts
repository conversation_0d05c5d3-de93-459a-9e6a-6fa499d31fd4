import type { TransformedProduct } from '@/lib/data/types';
import { SupabaseClient } from '@supabase/supabase-js';

interface ProductOffer {
  price: number | null;
  [key: string]: any;
}

/**
 * Calculate the minimum price for a product from its retailer offers or specifications
 */
export async function calculateProductMinPrice(supabase: SupabaseClient, productId: string, specifications: any): Promise<number | null> {
  if (!supabase) return null;

  try {
    // Get all offers for this product
    const { data: offers } = await supabase
      .from('product_retailer_offers')
      .select('price')
      .eq('product_id', productId);

    // Extract valid prices from offers
    const retailerPrices = (offers || [])
      .map((offer: ProductOffer) => offer.price)
      .filter((price: number | null): price is number => price != null);

    // Check for price in specifications
    const specificationPrice = specifications?.price 
      ? parseFloat(specifications.price.replace(/[^0-9.]/g, '')) 
      : null;

    // Return the minimum price from available sources
    if (retailerPrices.length > 0) {
      return Math.min(...retailerPrices);
    }
    return specificationPrice;
  } catch (error) {
    console.error('Error calculating product min price:', error);
    return null;
  }
}

/**
 * Transform a raw product by adding calculated fields like minPrice
 */
export async function transformProductWithCalculatedFields(supabase: SupabaseClient, product: any): Promise<TransformedProduct> {
  const minPrice = await calculateProductMinPrice(supabase, product.id, product.specifications);
  
  return {
    ...product,
    minPrice,
    // Add other transformations here if needed
  };
}

/**
 * Filter products by price range in memory
 */
export function filterProductsByPrice(
  products: TransformedProduct[], 
  minPrice?: number, 
  maxPrice?: number
): TransformedProduct[] {
  return products.filter(product => {
    if (product.minPrice === null) return false;
    if (minPrice !== undefined && product.minPrice < minPrice) return false;
    if (maxPrice !== undefined && product.minPrice > maxPrice) return false;
    return true;
  });
}

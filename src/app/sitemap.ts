// This file generates a dynamic sitemap for the website
// It pulls data from the data layer to include all products, brands, and retailers
// Implements caching and proper SEO optimization for search engine discovery

import { MetadataRoute } from 'next';
import { getProducts } from '@/lib/data/products';
import { getBrands } from '@/lib/data/brands';
import { getRetailers } from '@/lib/data/retailers';
import { siteConfig } from '@/lib/metadata-utils';
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    const supabase = createServerSupabaseReadOnlyClient();
    // Fetch all data in parallel for better performance
    const [products, brandsResponse, retailersResponse] = await Promise.all([
      getProducts(supabase), // Adjusted to no arguments as per function signature
      getBrands(supabase), // Adjusted to no arguments as per function signature
      getRetailers(supabase), // Adjusted to no arguments as per function signature
    ]);

    const brands = brandsResponse || [];
    const retailers = retailersResponse || [];

    // Static routes with their last modified date and proper SEO priorities
    const staticRoutes = [
      {
        url: `${siteConfig.url}`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 1.0,
      },
      {
        url: `${siteConfig.url}/products`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 0.9,
      },
      {
        url: `${siteConfig.url}/brands`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.9,
      },
      {
        url: `${siteConfig.url}/retailers`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.9,
      },
      {
        url: `${siteConfig.url}/search`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 0.8,
      },
      {
        url: `${siteConfig.url}/contact`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.5,
      },
    ] as MetadataRoute.Sitemap;

    // Dynamic product pages with SEO-friendly URLs and proper priorities
    const productRoutes: MetadataRoute.Sitemap = products.product.map((product: any) => ({
      url: `${siteConfig.url}/products/${product.slug || product.id}`,
      lastModified: product.updatedAt ? new Date(product.updatedAt) : new Date(),
      changeFrequency: 'weekly' as const,
      priority: product.isFeatured ? 0.9 : 0.7, // Higher priority for featured products
    }));

    // Dynamic brand pages with proper SEO optimization
    const brandRoutes: MetadataRoute.Sitemap = brands.data.map((brand: any) => ({
      url: `${siteConfig.url}/brands/${brand.slug || brand.id}`,
      lastModified: brand.updatedAt ? new Date(brand.updatedAt) : new Date(),
      changeFrequency: 'weekly' as const,
      priority: brand.featured ? 0.8 : 0.6, // Higher priority for featured brands
    }));

    // Dynamic retailer pages (1,658+ SEO pages for search indexing)
    const retailerRoutes: MetadataRoute.Sitemap = retailers.data.map((retailer: any) => ({
      url: `${siteConfig.url}/retailers/${retailer.slug || retailer.id}`,
      lastModified: retailer.updatedAt ? new Date(retailer.updatedAt) : new Date(),
      changeFrequency: 'weekly' as const,
      priority: retailer.featured ? 0.8 : 0.6, // Higher priority for featured retailers
    }));

    // Combine all routes and return
    const allRoutes = [
      ...staticRoutes,
      ...productRoutes,
      ...brandRoutes,
      ...retailerRoutes,
    ];

    // Log sitemap generation stats for monitoring
    console.log(`Sitemap generated with ${allRoutes.length} URLs:`, {
      static: staticRoutes.length,
      products: productRoutes.length,
      brands: brandRoutes.length,
      retailers: retailerRoutes.length,
    });

    return allRoutes;

  } catch (error) {
    console.error('Error generating sitemap:', error);

    // Return minimal sitemap on error to prevent complete failure
    return [
      {
        url: `${siteConfig.url}`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 1.0,
      },
    ];
  }
}

import { Suspense } from 'react';
import { Metadata } from 'next';
import ProductsContent from './components/ProductsContent';
import { getProducts, getFilterOptions } from '@/lib/data/products';
import type { ProductFilters, PaginatedResponse, TransformedProduct, Brand, Promotion } from '@/lib/data/types';
import { siteConfig } from '@/lib/metadata-utils';
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

// Define the shape of the promotion data we expect in the filter options
interface FilterPromotion {
    id: string;
    title: string;
    brand_id: string | null;
    brand_name: string;
}

// Type for the initial data passed to the client component
interface InitialProductsData {
    data: TransformedProduct[] | null;
    error: string | null;
    pagination?: {
        page: number;
        pageSize: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

// Type for filter options that matches the expected type in ProductsContent
interface FilterOptions {
    brands: Array<{ id: string; name: string }>;
    promotions: FilterPromotion[];
}

// Force dynamic rendering to ensure fresh data on each request
export const dynamic = 'force-dynamic';

interface ProductsPageProps {
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ searchParams }: ProductsPageProps): Promise<Metadata> {
    const resolvedSearchParams = await searchParams;
    const page = parseInt(resolvedSearchParams.page as string || '1', 10);
    const title = page > 1 ? `All Products - Page ${page} | Cashback Deals` : 'All Products | Cashback Deals';
    const description = `Browse all products with cashback offers. Page ${page} of our collection.`;

    const canonicalUrl = new URL('/products', siteConfig.url);
    if (page > 1) {
        canonicalUrl.searchParams.set('page', page.toString());
    }

    return {
        title,
        description,
        alternates: {
            canonical: canonicalUrl.toString(),
        },
    };
}

export default async function ProductsPage({ searchParams }: ProductsPageProps) {
    const resolvedSearchParams = await searchParams;
    const page = parseInt(resolvedSearchParams.page as string || '1', 10);
    const promotionId = resolvedSearchParams.promotion_id as string;
    const brandId = resolvedSearchParams.brand_id as string;
    const categoryId = resolvedSearchParams.category_id as string;
    const search = resolvedSearchParams.search as string;

    const filters: ProductFilters = {};
    if (promotionId) filters.promotionId = promotionId;
    if (brandId) filters.brandId = brandId;
    if (categoryId) filters.categoryId = categoryId;
    if (search) filters.search = search;

    const supabase = createServerSupabaseReadOnlyClient();
    const productsResponse = await getProducts(supabase, {
        ...filters,
        page,
        pageSize: 20,
    });

    const filterOptions: FilterOptions = await getFilterOptions(supabase);

    const initialData: InitialProductsData = {
        data: productsResponse?.product || null,
        error: null,
        pagination: productsResponse?.pagination
    };

    const { totalPages, hasNext, hasPrev } = productsResponse.pagination || {};
    const baseUrl = new URL('/products', siteConfig.url);

    const prevPageUrl = hasPrev ? `${baseUrl}?page=${page - 1}` : null;
    const nextPageUrl = hasNext ? `${baseUrl}?page=${page + 1}` : null;

    return (
        <>
            {prevPageUrl && <link rel="prev" href={prevPageUrl} />}
            {nextPageUrl && <link rel="next" href={nextPageUrl} />}
            <div className="container py-12">
                <Suspense
                    fallback={
                        <div className="flex justify-center items-center h-64">
                            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                        </div>
                    }
                >
                    <ProductsContent
                        initialData={initialData}
                        filterOptions={filterOptions}
                        initialPage={page}
                        initialFilters={{
                            promotionId,
                            brandId,
                            categoryId,
                            search
                        }}
                    />
                </Suspense>
            </div>
        </>
    );
}

import { Suspense } from 'react'
import { notFound } from 'next/navigation'

import { env } from '@/env.mjs'
import {
  getProducts,
  getFeaturedProducts,
  getBrands,
  getFeaturedBrands,
  getFeaturedPromotions
} from '@/lib/data'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

/**
 * Loading components for Suspense boundaries
 */
function ProductsLoading() {
  return (
    <div className="space-y-4">
      <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
      <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
    </div>
  )
}

function BrandsLoading() {
  return (
    <div className="space-y-4">
      <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3"></div>
    </div>
  )
}

function PromotionsLoading() {
  return (
    <div className="space-y-4">
      <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded animate-pulse w-4/5"></div>
    </div>
  )
}

/**
 * Server component to test product data fetching
 */
async function ProductsTest() {
  try {
    const supabase = createServerSupabaseReadOnlyClient();
    const [allProducts, featuredProducts] = await Promise.all([
      getProducts(supabase), // Fixed: removed extra arguments to match function signature
      getFeaturedProducts(supabase, 3)  // Get 3 featured products
    ])

    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold mb-4 text-green-600">✅ Products Data Layer</h2>

        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-gray-900">All Products (First 5)</h3>
          <p className="text-sm text-gray-600 mb-2">
            Total: {allProducts.pagination?.total ?? 0} | Page: {allProducts.pagination?.page ?? 0}
          </p>
          <ul className="space-y-2">
            {Array.isArray(allProducts.product) ? allProducts.product.map((product: any) => (
              <li key={product.id} className="text-sm">
                <span className="font-medium">{product.name}</span>
                {product.brand && <span className="text-gray-500"> - {product.brand.name}</span>}
                {product.cashbackAmount && (
                  <span className="text-green-600"> (£{product.cashbackAmount} cashback)</span>
                )}
              </li>
            )) : null}
          </ul>
          </div>

          <div>
            <h3 className="font-medium text-gray-900">Featured Products</h3>
        <ul className="space-y-2">
          {featuredProducts.map((product: any) => (
            <li key={product.id} className="text-sm">
              <span className="font-medium">{product.name}</span>
              {product.brand && <span className="text-gray-500"> - {product.brand.name}</span>}
              <span className="text-blue-600"> ⭐ Featured</span>
            </li>
          ))}
        </ul>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg border border-red-200">
        <h2 className="text-xl font-semibold mb-2 text-red-600">❌ Products Data Layer Error</h2>
        <p className="text-red-700">Error: {error instanceof Error ? error.message : 'Unknown error'}</p>
      </div>
    )
  }
}

/**
 * Server component to test brand data fetching
 */
  async function BrandsTest() {
    try {
      const supabase = createServerSupabaseReadOnlyClient();
      const [allBrands, featuredBrands] = await Promise.all([
        getBrands(supabase), // Fixed: removed extra arguments to match function signature
        getFeaturedBrands(supabase, 3) // Get 3 featured brands
      ])

    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold mb-4 text-green-600">✅ Brands Data Layer</h2>

        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-gray-900">All Brands (First 5)</h3>
            <p className="text-sm text-gray-600 mb-2">
              Total: {allBrands.pagination.total} | Page: {allBrands.pagination.page}
            </p>
            <ul className="space-y-2">
              {allBrands.data.map((brand) => (
                <li key={brand.id} className="text-sm">
                  <span className="font-medium">{brand.name}</span>
                  <span className="text-gray-500"> ({brand.slug})</span>
                  {brand.featured && <span className="text-blue-600"> ⭐ Featured</span>}
                  {brand.sponsored && <span className="text-purple-600"> 💎 Sponsored</span>}
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-gray-900">Featured Brands</h3>
            <ul className="space-y-2">
              {featuredBrands.map((brand) => (
                <li key={brand.id} className="text-sm">
                  <span className="font-medium">{brand.name}</span>
                  <span className="text-blue-600"> ⭐ Featured</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg border border-red-200">
        <h2 className="text-xl font-semibold mb-2 text-red-600">❌ Brands Data Layer Error</h2>
        <p className="text-red-700">Error: {error instanceof Error ? error.message : 'Unknown error'}</p>
      </div>
    )
  }
}

/**
 * Server component to test promotion data fetching
 */
async function PromotionsTest() {
  try {
    const supabase = createServerSupabaseReadOnlyClient();
    const featuredPromotions = await getFeaturedPromotions(supabase, 3)

    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold mb-4 text-green-600">✅ Promotions Data Layer</h2>

        <div>
          <h3 className="font-medium text-gray-900">Featured Promotions</h3>
          {featuredPromotions.length === 0 ? (
            <p className="text-gray-500 text-sm">No featured promotions found</p>
          ) : (
            <ul className="space-y-2">
              {featuredPromotions.map((promotion) => (
                <li key={promotion.id} className="text-sm">
                  <span className="font-medium">{promotion.title}</span>
                  {promotion.brand && <span className="text-gray-500"> - {promotion.brand.name}</span>}
                  <span className="text-green-600"> (Up to £{promotion.maxCashbackAmount})</span>
                  <div className="text-xs text-gray-500">
                    Valid until: {promotion.purchaseEndDate}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    )
  } catch (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg border border-red-200">
        <h2 className="text-xl font-semibold mb-2 text-red-600">❌ Promotions Data Layer Error</h2>
        <p className="text-red-700">Error: {error instanceof Error ? error.message : 'Unknown error'}</p>
      </div>
    )
  }
}

/**
 * Main test page component
 */
export default function TestDataLayerPage() {
  if (env.NEXT_PUBLIC_ENV === 'production') {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Phase 1 Data Layer Test
          </h1>
          <p className="text-gray-600">
            Testing server-side data functions in Next.js development environment
          </p>
          <div className="mt-4 text-sm text-gray-500">
            This page demonstrates server-side rendering with our new data layer.
            All data is fetched on the server and rendered before being sent to the client.
          </div>
        </div>

        <div className="space-y-6">
          <Suspense fallback={<ProductsLoading />}>
            <ProductsTest />
          </Suspense>

          <Suspense fallback={<BrandsLoading />}>
            <BrandsTest />
          </Suspense>

          <Suspense fallback={<PromotionsLoading />}>
            <PromotionsTest />
          </Suspense>
        </div>

        <div className="mt-8 bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">
            🧪 Test Information
          </h2>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• This page uses server-side rendering (SSR)</li>
            <li>• Data is fetched using our new server-side data layer</li>
            <li>• All functions use cached queries for optimal performance</li>
            <li>• Service role key is used securely on the server</li>
            <li>• Check the browser's "View Source" to see server-rendered content</li>
          </ul>
        </div>

        <div className="mt-6 text-center">
          <a
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            ← Back to Homepage
          </a>
        </div>
      </div>
    </div>
  )
}

/**
 * Metadata for the test page
 */
export const metadata = {
  title: 'Phase 1 Data Layer Test | CashbackDeals',
  description: 'Testing server-side data layer implementation for SEO optimization',
  robots: 'noindex,nofollow', // Don't index this test page
}

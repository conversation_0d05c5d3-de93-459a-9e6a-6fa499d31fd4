'use client'

import { useState, useEffect } from 'react'
import { Search } from 'lucide-react'
import { validateSearchQuery } from '@/lib/security/utils'
import { useRouter, useSearchParams } from 'next/navigation'

/**
 * Client-side search input component for filtering brands
 * Uses URL search params to maintain state across navigation
 */
export function SearchInput() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '')
  const [validationError, setValidationError] = useState<string | null>(null)

  // Enhanced input change handler with validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;

    // Prevent DoS attacks with length limit
    if (rawValue.length > 100) {
      setValidationError('Search query too long (max 100 characters)');
      return;
    }

    // Validate and sanitize the input
    const validation = validateSearchQuery(rawValue);

    if (!validation.isValid && rawValue.length > 0) {
      setValidationError('Search query contains invalid characters');
      return;
    }

    // Clear any previous validation errors
    setValidationError(null);

    // Use sanitized value
    setSearchQuery(validation.sanitized);
  };

  // Debounce search to prevent too many re-renders
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      const params = new URLSearchParams(searchParams)

      if (searchQuery) {
        params.set('q', searchQuery)
      } else {
        params.delete('q')
      }

      // Update URL without page reload
      router.push(`?${params.toString()}`, { scroll: false })
    }, 300)

    return () => clearTimeout(delayDebounceFn)
  }, [searchQuery, router, searchParams])

  return (
    <div className="relative">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-5 w-5 text-foreground/70" />
      </div>
      <input
        type="text"
        value={searchQuery}
        onChange={handleInputChange}
        className="block w-full pl-10 pr-3 py-3 border border-foreground/20 rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent"
        placeholder="Search brands..."
        aria-label="Search brands"
        maxLength={100}
      />
      {/* Validation error display */}
      {validationError && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm z-50">
          {validationError}
        </div>
      )}
    </div>
  )
}

export default SearchInput

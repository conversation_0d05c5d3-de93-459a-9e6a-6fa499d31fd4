'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Tag, Clock } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import type { 
  TransformedBrand, 
  TransformedPromotion, 
  TransformedProduct 
} from '@/lib/data/types';

interface BrandClientProps {
  brand?: TransformedBrand | null;
  promotions?: TransformedPromotion[];
  activePromotions?: TransformedPromotion[];
  expiredPromotions?: TransformedPromotion[];
  featuredProducts?: TransformedProduct[];
  promotionCount?: number;
  activePromotionCount?: number;
  expiredPromotionCount?: number;
}

export default function BrandClient({ 
  brand = null, 
  activePromotions = [],
  expiredPromotions = [],
  featuredProducts = [],
  promotionCount = 0,
  activePromotionCount = 0,
  expiredPromotionCount = 0
}: BrandClientProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // If brand is null, show a not found state
  if (!brand) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-foreground mb-4">Brand Not Found</h1>
          <p className="text-muted-foreground mb-6">The brand you're looking for doesn't exist or has been removed.</p>
          <Link href="/brands" className="inline-flex items-center text-primary hover:text-primary/80">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Brands
          </Link>
        </div>
      </div>
    );
  }

  // Use pre-processed promotions from the server with null checks
  const safeActivePromotions = activePromotions || [];
  const safeExpiredPromotions = expiredPromotions || [];
  const hasActivePromotions = safeActivePromotions.length > 0;
  const hasExpiredPromotions = safeExpiredPromotions.length > 0;

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Determine promotion status badge
  const getStatusBadge = (status: string, endDate: string) => {
    const now = new Date();
    const purchaseEnd = new Date(endDate);
    
    if (status === 'expired' || purchaseEnd < now) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Expired
        </span>
      );
    }
    
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        Active
      </span>
    );
  };

  return (
    <div className="relative flex flex-col min-h-screen">
      {/* Brand Header */}
      <div className="bg-muted border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center">
            {/* Brand Logo */}
            <div className="w-24 h-24 md:w-32 md:h-32 rounded-lg bg-card shadow-sm border flex items-center justify-center mb-4 md:mb-0 md:mr-8">
              {brand.logoUrl ? (
                <Image
                  src={brand.logoUrl}
                  alt={brand.name}
                  width={128}
                  height={128}
                  className="w-auto h-auto max-h-full max-w-full p-2 object-contain"
                  priority
                />
              ) : (
                <span className="text-2xl font-bold text-muted-foreground">
                  {brand.name.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
            <div className="flex-1">
              <h1 className="text-3xl font-bold tracking-tight text-foreground mb-4">
                {brand.name} Promotions & Cashback
              </h1>
              {brand.description && (
                <p className="text-muted-foreground mb-4">{brand.description}</p>
              )}
              <div className="flex flex-wrap gap-2">
                <Link href="/brands?category=all" className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3">
                  <Tag className="w-4 h-4 mr-1" />
                  View All Categories
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto">

        {/* Active Promotions */}
        <section className="container mx-auto px-4 py-8">
          <h2 className="text-xl font-semibold mb-6 flex items-center">
            <Tag className="w-5 h-5 mr-2 text-primary" />
            Active Promotions
            <span className="ml-2 text-sm font-normal text-muted-foreground">
              ({safeActivePromotions.length} available)
            </span>
          </h2>
          
          {hasActivePromotions ? (
            <div className="space-y-4">
              {safeActivePromotions.map((promo) => (
                <motion.div
                  key={promo.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="rounded-lg border p-6 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-lg font-medium text-card-foreground">{promo.title}</h3>
                        {getStatusBadge(promo.status, promo.purchaseEndDate)}
                      </div>
                      <p className="text-muted-foreground mb-2">{promo.description}</p>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="w-4 h-4 mr-1" />
                        <span>Valid until {formatDate(promo.purchaseEndDate)}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary">
                        £{promo.maxCashbackAmount} Cashback
                      </div>
                      <Link 
                        href={`/products?promotion_id=${promo.id}`}
                        className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-2 mt-2"
                      >
                        View Products
                      </Link>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="bg-muted border rounded-lg p-6 text-center">
              <p className="text-foreground">No active promotions found for {brand.name}. Check back soon for new offers!</p>
            </div>
          )}
        </section>

        {/* Expired Promotions */}
        {hasExpiredPromotions && (
          <section className="container mx-auto px-4 pb-12">
            <Collapsible onOpenChange={setIsExpanded}>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold flex items-center">
                  <Clock className="w-5 h-5 mr-2 text-muted-foreground" />
                  Expired Promotions
                  <span className="ml-2 text-sm font-normal text-muted-foreground">
                    ({safeExpiredPromotions.length} past offers)
                  </span>
                </h2>
                <CollapsibleTrigger asChild>
                  <button 
                    type="button" 
                    className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 px-3 text-muted-foreground"
                  >
                    {isExpanded ? 'Hide' : 'Show'}
                  </button>
                </CollapsibleTrigger>
              </div>
              <CollapsibleContent>
                <div className="space-y-4">
                  {safeExpiredPromotions.map((promo) => (
                    <div 
                      key={promo.id}
                      className="bg-muted rounded-lg border p-6 opacity-75"
                    >
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="text-lg font-medium text-card-foreground">{promo.title}</h3>
                            {getStatusBadge(promo.status, promo.purchaseEndDate)}
                          </div>
                          <p className="text-muted-foreground mb-2">{promo.description}</p>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Clock className="w-4 h-4 mr-1" />
                            <span>Valid until {formatDate(promo.purchaseEndDate)}</span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-primary">
                            ${promo.maxCashbackAmount} Cashback
                          </div>
                          <Link 
                            href={`/promotions/${promo.id}`}
                            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-2 mt-2"
                          >
                            View Details
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CollapsibleContent>
            </Collapsible>
          </section>
        )}
      </div>
    </div>
  );
}

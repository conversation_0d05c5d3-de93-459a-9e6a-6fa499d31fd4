import { notFound } from 'next/navigation';
import { getBrandPageData } from '@/lib/data/brands';
import BrandClient from './BrandClient';
import { Metadata } from 'next';
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

// Define the props type for both the page and metadata function
interface BrandPageProps {
  params: Promise<{
    id: string;
  }>;
}

// Revalidate every hour
export const revalidate = 3600;

// METADATA GENERATION - Correctly placed in the page file
export async function generateMetadata({ params }: BrandPageProps): Promise<Metadata> {
  const { id } = await params;
  const supabase = createServerSupabaseReadOnlyClient();
  const data = await getBrandPageData(supabase, id);

  if (!data?.brand) {
    return {
      title: 'Brand Not Found',
      description: 'The requested brand could not be found.',
    };
  }

  const { brand } = data;
  const title = `${brand.name} Promotions & Cashback Deals`;
  const description = brand.description || `Find the latest ${brand.name} cashback offers and promotions.`;
  const url = `/brands/${brand.slug || brand.id}`;

  // Structured Data for the <head>
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Brand',
    name: brand.name,
    description: description,
    ...(brand.logoUrl && { logo: brand.logoUrl }),
    url: new URL(url, process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000').toString(),
    identifier: brand.id
  };

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      images: brand.logoUrl ? [brand.logoUrl] : [],
      url,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: brand.logoUrl ? [brand.logoUrl] : [],
    },
    other: {
      'script:ld+json': JSON.stringify(structuredData)
    }
  };
}

// Helper to check if a string is a valid UUID
const isUuid = (id: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
};

// PAGE COMPONENT - Correctly typed
export default async function BrandPage({ params }: BrandPageProps) {
  const { id } = await params;
  const supabase = createServerSupabaseReadOnlyClient();
  const data = await getBrandPageData(supabase, id);
  
  if (!data?.brand) {
    notFound();
  }

  return (
    <BrandClient 
      brand={data.brand}
      promotions={data.promotions}
      activePromotions={data.activePromotions}
      expiredPromotions={data.expiredPromotions}
      promotionCount={data.promotionCount}
      activePromotionCount={data.activePromotionCount}
      expiredPromotionCount={data.expiredPromotionCount}
    />
  );
}

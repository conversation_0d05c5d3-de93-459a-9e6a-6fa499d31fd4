/**
 * Sentry Health Check API
 * 
 * Provides health monitoring for Sentry functionality
 * as per PR3 technical specifications.
 * 
 * <AUTHOR> Agent
 * @date 2025-07-13
 */

import { NextResponse } from 'next/server';
import * as Sentry from '@sentry/nextjs';

export const dynamic = 'force-dynamic';

/**
 * Health check for Sentry functionality
 */
async function healthCheckSentry(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: {
    sentryInitialized: boolean;
    dsnConfigured: boolean;
    errorCaptureWorking: boolean;
    lastTestError?: string;
  };
}> {
  try {
    const ENABLE_SENTRY = process.env.ENABLE_SENTRY === 'true';
    const ENABLE_SENTRY_LOCAL = process.env.ENABLE_SENTRY_LOCAL === 'true';
    const SENTRY_DSN = process.env.SENTRY_DSN;
    
    // Check if Sentry should be enabled
    const shouldBeEnabled = process.env.NODE_ENV === 'production' 
      ? ENABLE_SENTRY 
      : ENABLE_SENTRY_LOCAL;
    
    if (!shouldBeEnabled) {
      return {
        status: 'healthy',
        details: {
          sentryInitialized: false,
          dsnConfigured: !!SENTRY_DSN,
          errorCaptureWorking: false,
        },
      };
    }
    
    // Test Sentry error capture (only in non-production)
    let errorCaptureWorking = false;
    if (process.env.NODE_ENV !== 'production') {
      try {
        // Create a test error for Sentry
        const testError = new Error('_SENTRY_SMOKE_TEST_');
        Sentry.captureException(testError);
        errorCaptureWorking = true;
      } catch (error) {
        console.warn('[Sentry Health] Error capture test failed:', error);
      }
    } else {
      // In production, assume it's working if configured
      errorCaptureWorking = !!SENTRY_DSN && ENABLE_SENTRY;
    }
    
    return {
      status: SENTRY_DSN && shouldBeEnabled ? 'healthy' : 'degraded',
      details: {
        sentryInitialized: shouldBeEnabled,
        dsnConfigured: !!SENTRY_DSN,
        errorCaptureWorking,
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        sentryInitialized: false,
        dsnConfigured: false,
        errorCaptureWorking: false,
        lastTestError: error instanceof Error ? error.message : 'Unknown error',
      },
    };
  }
}

/**
 * GET /api/health/sentry
 * 
 * Returns health status of Sentry functionality
 */
export async function GET() {
  try {
    const healthResult = await healthCheckSentry();
    
    const response = {
      service: 'sentry',
      timestamp: new Date().toISOString(),
      ...healthResult,
    };
    
    // Return appropriate HTTP status based on health
    const statusCode = healthResult.status === 'healthy' ? 200 : 
                      healthResult.status === 'degraded' ? 200 : 503;
    
    return NextResponse.json(response, { status: statusCode });
  } catch (error) {
    // Return unhealthy status on any error
    return NextResponse.json({
      service: 'sentry',
      timestamp: new Date().toISOString(),
      status: 'unhealthy',
      details: {
        sentryInitialized: false,
        dsnConfigured: false,
        errorCaptureWorking: false,
        lastTestError: error instanceof Error ? error.message : 'Unknown error',
      },
    }, { status: 503 });
  }
}

/**
 * IP Allowlist Health Check API
 * 
 * Provides health monitoring for IP allowlist functionality
 * as per PR3 technical specifications.
 * 
 * <AUTHOR> Agent
 * @date 2025-07-13
 */

import { NextResponse } from 'next/server';
import { healthCheckIPAllowlist } from '@/lib/security/ip-allowlist';

export const dynamic = 'force-dynamic';

/**
 * GET /api/health/ip-allowlist
 * 
 * Returns health status of IP allowlist functionality
 */
export async function GET() {
  try {
    const healthResult = await healthCheckIPAllowlist();
    
    const response = {
      service: 'ip-allowlist',
      timestamp: new Date().toISOString(),
      ...healthResult,
    };
    
    // Return appropriate HTTP status based on health
    const statusCode = healthResult.status === 'healthy' ? 200 : 
                      healthResult.status === 'degraded' ? 200 : 503;
    
    return NextResponse.json(response, { status: statusCode });
  } catch (error) {
    // Return unhealthy status on any error
    return NextResponse.json({
      service: 'ip-allowlist',
      timestamp: new Date().toISOString(),
      status: 'unhealthy',
      details: {
        configLoaded: false,
        cidrRulesValid: false,
        performanceOk: false,
        lastError: error instanceof Error ? error.message : 'Unknown error',
      },
    }, { status: 503 });
  }
}

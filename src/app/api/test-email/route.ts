// Test email endpoint for debugging email configuration
import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

function createEmailTransporter() {
  return nodemailer.createTransport({
    host: process.env.EMAIL_SERVER || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD,
    },
  })
}

export async function GET(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Test endpoint not available in production' },
      { status: 403 }
    )
  }

  console.log('Testing email configuration...')
  console.log('Environment variables:', {
    EMAIL_SERVER: process.env.EMAIL_SERVER,
    EMAIL_PORT: process.env.EMAIL_PORT,
    EMAIL_SECURE: process.env.EMAIL_SECURE,
    EMAIL_USER: process.env.EMAIL_USER,
    EMAIL_FROM: process.env.EMAIL_FROM,
    EMAIL_PASSWORD: process.env.EMAIL_PASSWORD ? '[SET]' : '[NOT SET]'
  })

  const transporter = createEmailTransporter()

  try {
    // Test connection
    console.log('Testing SMTP connection...')
    await transporter.verify()
    console.log('SMTP connection successful!')

    // Send test email
    console.log('Sending test email...')
    const result = await transporter.sendMail({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: '<EMAIL>', // Send to self for testing
      subject: 'Test Email from Cashback Deals - ' + new Date().toISOString(),
      html: `
        <h2>Test Email</h2>
        <p>This is a test email from the Cashback Deals contact form system.</p>
        <p>Sent at: ${new Date().toISOString()}</p>
        <p>If you receive this, the email configuration is working correctly.</p>
      `,
      headers: {
        'X-Test-Email': 'true',
      },
    })

    console.log('Test email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    })

    return NextResponse.json({
      success: true,
      message: 'Test email sent successfully',
      details: {
        messageId: result.messageId,
        accepted: result.accepted,
        rejected: result.rejected,
        response: result.response
      }
    })

  } catch (error: any) {
    console.error('Email test failed:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Email test failed',
      details: {
        message: error?.message,
        code: error?.code,
        response: error?.response,
        responseCode: error?.responseCode
      }
    }, { status: 500 })
  }
}
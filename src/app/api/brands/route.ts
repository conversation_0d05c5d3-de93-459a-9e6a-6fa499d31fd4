/**
 * Refactored Brands API Route - Phase 2
 * 
 * This route has been refactored to use the shared server-side data layer
 * for improved security, performance, and maintainability.
 * 
 * Key improvements:
 * - Uses shared data layer functions instead of direct Supabase queries
 * - Eliminates public key usage in favor of secure server-side access
 * - Consistent error handling and response formats
 * - Better caching strategy
 * - Reduced code duplication
 */

import { NextRequest, NextResponse } from 'next/server'
import { getBrands } from '@/lib/data'
import type { ApiResponse, TransformedBrand } from '@/lib/data/types'
import { validatePaginationParams, validateFilterParams } from '@/lib/utils'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

/**
 * GET /api/brands
 * 
 * Fetches brands with pagination
 * 
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 20, max: 100)
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<TransformedBrand[]>>> {
  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request, rateLimits.brands)
  if (rateLimitResponse) {
    return rateLimitResponse as NextResponse<ApiResponse<TransformedBrand[]>>
  }

  try {
    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)

    // Validate pagination parameters
    const { page, limit } = validatePaginationParams(
      searchParams.get('page'),
      searchParams.get('limit')
    )

    // Validate and sanitize filter parameters
    const rawFilters = {
      search: searchParams.get('search'),
      featured: searchParams.get('featured'),
      sponsored: searchParams.get('sponsored')
    }

    const sanitizedFilters = validateFilterParams(rawFilters)

    // Validate boolean parameters
    let featuredFilter: boolean | undefined
    let sponsoredFilter: boolean | undefined

    if (rawFilters.featured !== null) {
      if (rawFilters.featured === 'true' || rawFilters.featured === 'false') {
        featuredFilter = rawFilters.featured === 'true'
      } else {
        return NextResponse.json({
          data: null,
          error: 'Invalid featured parameter. Must be true or false.',
        }, { status: 400 })
      }
    }

    if (rawFilters.sponsored !== null) {
      if (rawFilters.sponsored === 'true' || rawFilters.sponsored === 'false') {
        sponsoredFilter = rawFilters.sponsored === 'true'
      } else {
        return NextResponse.json({
          data: null,
          error: 'Invalid sponsored parameter. Must be true or false.',
        }, { status: 400 })
      }
    }
    
    const supabase = createServerSupabaseReadOnlyClient();
    // Fetch brands using shared data layer
    const result = await getBrands(supabase, page, limit)
    
    // Transform response to match legacy API format for backward compatibility
    const response: ApiResponse<TransformedBrand[]> = {
      data: result.data,
      error: null,
      pagination: {
        page: result.pagination.page,
        pageSize: result.pagination.pageSize,
        total: result.pagination.total,
        totalPages: result.pagination.totalPages,
        hasNext: result.pagination.hasNext,
        hasPrev: result.pagination.hasPrev,
      },
    }
    
    // Create Next.js response with proper caching headers
    const nextResponse = NextResponse.json(response)
    
    // Set cache headers for optimal performance
    // Brands change less frequently, so we can cache longer
    nextResponse.headers.set(
      'Cache-Control', 
      'public, s-maxage=3600, stale-while-revalidate=300'
    )
    
    // Add CORS headers for API access
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')
    
    return nextResponse
    
  } catch (error) {
    console.error('Error in brands API route:', error)
    
    // Return standardized error response
    const errorResponse: ApiResponse<TransformedBrand[]> = {
      data: null,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    }
    
    return NextResponse.json(errorResponse, { status: 500 })
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 */
export const revalidate = 3600 // Revalidate every hour

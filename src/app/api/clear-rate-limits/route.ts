import { NextResponse } from 'next/server'
import { clearAllRateLimits } from '@/lib/rateLimiter'

/**
 * Development-only endpoint to clear rate limits
 * This should only be used during development and testing
 */
export async function POST() {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Not available in production' },
      { status: 403 }
    )
  }

  try {
    clearAllRateLimits()
    return NextResponse.json({ 
      success: true, 
      message: 'Rate limits cleared successfully' 
    })
  } catch (error) {
    console.error('Error clearing rate limits:', error)
    return NextResponse.json(
      { error: 'Failed to clear rate limits' },
      { status: 500 }
    )
  }
}

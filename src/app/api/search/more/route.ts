
// src/app/api/search/more/route.ts
// This file defines the API endpoint for fetching additional pages of search results
// for the "Load More" functionality on the search page.

import { NextRequest, NextResponse } from 'next/server';
import { searchProducts } from '@/lib/data';
import type { SearchFilters } from '@/lib/data/types';
import { logger } from '@/lib/utils/logger';
import { withTimeout, TIMEOUT_CONFIG } from '@/lib/timeoutConfig';
import { getCacheMetrics } from '@/lib/cache/searchCache';
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter';
import { authenticateSearchRequest, createSearchUnauthorizedResponse, getAllowedOrigin, hasAuthenticationHeaders, getAuthenticationMethod } from '@/lib/security/auth-middleware';
import { enforceCorsPolicy, applyCorsHeaders, handleCorsPreflightRequest } from '@/lib/security/cors';

// Default number of items per page
const DEFAULT_PAGE_SIZE = 20;

/**
 * Classify user type based on User-Agent and Referer for analytics
 * Helps distinguish between real users, SEO bots, and LLM bots
 */
function classifyUserType(userAgent: string, referer: string): 'real_user' | 'seo_bot' | 'llm_bot' | 'unknown_bot' | 'development' {
  const ua = userAgent.toLowerCase();
  const ref = referer.toLowerCase();
  
  // Development/localhost traffic
  if (ref.includes('localhost') || ref.includes('127.0.0.1') || ref.includes('192.168.')) {
    return 'development';
  }
  
  // SEO bots (major search engines)
  const seoBots = [
    'googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider', 'yandexbot',
    'facebookexternalhit', 'twitterbot', 'linkedinbot', 'whatsapp', 'telegrambot',
    'applebot', 'discordbot', 'skypebot', 'redditbot', 'pinterestbot',
    'crawler', 'spider', 'scraper', 'indexer', 'sitemap'
  ];
  
  for (const bot of seoBots) {
    if (ua.includes(bot)) {
      return 'seo_bot';
    }
  }
  
  // LLM/AI bots (Claude, ChatGPT, etc.)
  const llmBots = [
    'claude-web', 'chatgpt', 'gpt-', 'openai', 'anthropic', 'claude',
    'perplexity', 'bard', 'gemini-pro', 'copilot', 'bing-ai',
    'llm', 'artificial', 'ai-agent', 'assistant'
  ];
  
  for (const bot of llmBots) {
    if (ua.includes(bot)) {
      return 'llm_bot';
    }
  }
  
  // Other bots
  const otherBots = [
    'bot', 'crawl', 'spider', 'scrape', 'fetch', 'scan', 'monitor',
    'headless', 'phantom', 'selenium', 'webdriver', 'automation',
    'curl', 'wget', 'postman', 'insomnia', 'http', 'python-requests'
  ];
  
  for (const bot of otherBots) {
    if (ua.includes(bot)) {
      return 'unknown_bot';
    }
  }
  
  // Real users (browsers)
  const browsers = ['chrome', 'firefox', 'safari', 'edge', 'opera', 'mozilla'];
  for (const browser of browsers) {
    if (ua.includes(browser)) {
      return 'real_user';
    }
  }
  
  return 'unknown_bot'; // Default to bot if uncertain
}

/**
 * Handles GET requests to fetch a specific page of search results.
 * This endpoint is designed to be called from the client-side to dynamically
 * load more products without a full page refresh.
 */
export async function GET(request: NextRequest) {
  const requestStartTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 10);

  // 1. CORS enforcement (first line of defense)
  const corsResponse = enforceCorsPolicy(request)
  if (corsResponse) {
    return corsResponse
  }

  // 2. Apply rate limiting
  const rateLimitResponse = applyRateLimit(request, rateLimits.search)
  if (rateLimitResponse) {
    // Add CORS headers to rate limit response
    return applyCorsHeaders(request, rateLimitResponse)
  }

  // Apply authentication with comprehensive logging for user analytics
  const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
  const userAgent = request.headers.get('user-agent') || 'unknown'
  const referer = request.headers.get('referer') || 'unknown'
  
  // Classify user type for analytics
  const userType = classifyUserType(userAgent, referer)
  
  // Log Load More analytics - track all attempts
  console.log(JSON.stringify({
    event: 'LOAD_MORE_ATTEMPT',
    timestamp: new Date().toISOString(),
    endpoint: '/api/search/more',
    ip: clientIP.split(',')[0].trim(), // First IP if multiple
    userAgent: userAgent.substring(0, 200), // Truncate for privacy
    referer: referer.substring(0, 200),
    userType: userType,
    hasAuthHeaders: hasAuthenticationHeaders(request),
    authMethod: getAuthenticationMethod(request),
    searchParams: {
      hasQuery: !!request.nextUrl.searchParams.get('q'),
      page: request.nextUrl.searchParams.get('page') || '1',
      category: request.nextUrl.searchParams.get('category') || 'none'
    }
  }))

  const authResult = await authenticateSearchRequest(request)
  if (!authResult.success) {
    // Log detailed authentication failure analytics
    console.warn(JSON.stringify({
      event: 'LOAD_MORE_AUTH_FAILURE',
      timestamp: new Date().toISOString(),
      endpoint: '/api/search/more',
      ip: clientIP.split(',')[0].trim(),
      userAgent: userAgent.substring(0, 200),
      userType: userType,
      authMethod: getAuthenticationMethod(request),
      hasAuthHeaders: hasAuthenticationHeaders(request),
      traceId: authResult.traceId,
      error: authResult.error,
      willShowCaptcha: userType === 'real_user', // Real users get CAPTCHA
      impact: userType === 'real_user' ? 'HIGH' : 'LOW'
    }))
    
    const unauthorizedResponse = createSearchUnauthorizedResponse(authResult.traceId)
    return applyCorsHeaders(request, unauthorizedResponse)
  }

  // Log successful authentication with detailed analytics
  console.log(JSON.stringify({
    event: 'LOAD_MORE_AUTH_SUCCESS',
    timestamp: new Date().toISOString(),
    endpoint: '/api/search/more',
    ip: clientIP.split(',')[0].trim(),
    userAgent: userAgent.substring(0, 200),
    userType: userType,
    authMethod: authResult.method,
    traceId: authResult.traceId,
    impact: userType === 'real_user' ? 'HIGH' : 'LOW'
  }))

  try {
    // Extract search parameters from the request URL
    const { searchParams, pathname } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
    const category = searchParams.get('category') || '';
    const subcategory = searchParams.get('subcategory') || '';
    const pageSize = Math.min(50, parseInt(searchParams.get('pageSize') || DEFAULT_PAGE_SIZE.toString(), 10));
    
    logger.info('API Request Received', {
      requestId,
      endpoint: pathname,
      method: 'GET',
      query: query ? '[REDACTED]' : '', // Redact full query in logs
      page,
      pageSize,
      category,
      subcategory,
      hasQuery: !!query,
      hasCategory: !!category,
      hasSubcategory: !!subcategory,
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
    });

    // Validate input parameters
    if (!query && !category && !subcategory) {
      logger.warn('Empty search parameters', { requestId });
      return NextResponse.json({ 
        products: [],
        totalCount: 0,
        hasMore: false,
        currentPage: 1,
        pageSize: DEFAULT_PAGE_SIZE
      });
    }

    const searchStartTime = Date.now();

    // Build search filters object (matching main search route pattern)
    const filters: SearchFilters = {
      query,
      category: category || undefined,
      // Note: subcategory is not supported in SearchFilters interface
      // This is a limitation that should be addressed in the future
    };

    const supabase = createServerSupabaseReadOnlyClient();
    // Wrap search operation with timeout
    const searchTimeout = TIMEOUT_CONFIG.SEARCH.COMPLEX;
    const searchResult = await withTimeout(
      searchProducts(supabase, filters, page, pageSize),
      searchTimeout,
      `Search operation timed out after ${searchTimeout}ms for query: ${query}`
    );

    // Extract products and total from SearchResult
    const { products, total: totalCount } = searchResult;

    const searchDuration = Date.now() - searchStartTime;
    
    // Calculate pagination metadata
    const hasMore = (page * pageSize) < totalCount;
    const totalPages = Math.ceil(totalCount / pageSize);

    // Create response
    const response = {
      products,
      currentPage: page,
      pageSize,
      totalCount,
      totalPages,
      hasMore
    };

    // Get cache metrics for monitoring
    const cacheMetrics = getCacheMetrics();

    // Log successful response summary with cache performance
    logger.info('Search completed', {
      requestId,
      productsCount: products.length,
      currentPage: page,
      pageSize,
      totalCount,
      totalPages,
      hasMore,
      searchDurationMs: searchDuration,
      executionTime: Date.now() - requestStartTime,
      cacheHitRate: cacheMetrics.stats.hitRate,
      cacheSize: cacheMetrics.stats.cacheSize
    });

    // Add performance headers
    const responseHeaders = {
      'X-Request-ID': requestId,
      'X-Response-Time': `${Date.now() - requestStartTime}ms`,
      'X-Search-Duration': `${searchDuration}ms`,
      'X-Cache-Hit-Rate': `${cacheMetrics.stats.hitRate}%`,
      'X-Cache-Size': `${cacheMetrics.stats.cacheSize}`,
      'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600' // 5 min cache, 10 min stale
    };

    // Create response with restricted CORS headers
    const nextResponse = NextResponse.json(response, { headers: responseHeaders });

    // Apply CORS headers using centralized middleware
    applyCorsHeaders(request, nextResponse, {
      methods: ['GET', 'OPTIONS'],
      credentials: true
    });

    return nextResponse;

  } catch (error) {
    const errorForLogging = error instanceof Error ? error : new Error(String(error));
    
    logger.error('Error in search API', errorForLogging, {
      requestId,
      executionTime: Date.now() - requestStartTime
    });
    
    const responseTime = Date.now() - requestStartTime;
    const errorResponse = NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: 'An error occurred while processing your request',
        requestId
      },
      { 
        status: 500,
        headers: { 
          'Content-Type': 'application/json',
          'X-Request-ID': requestId,
          'X-Response-Time': `${responseTime}ms`
        }
      }
    );

    // Apply CORS headers even to error responses
    applyCorsHeaders(request, errorResponse);
    return errorResponse;
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(request: NextRequest): Promise<NextResponse> {
  return handleCorsPreflightRequest(request)
}

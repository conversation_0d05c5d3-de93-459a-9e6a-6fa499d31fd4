/**
 * Refactored Search API Route - Phase 2A Step 4
 * 
 * This route has been refactored to use the shared server-side data layer
 * for improved security, performance, and maintainability.
 * 
 * Key improvements:
 * - Uses shared data layer functions instead of direct Supabase queries
 * - Eliminates public key usage in favor of secure server-side access
 * - Consistent error handling and response formats
 * - Better caching strategy
 * - Reduced code complexity from 249 lines to ~150 lines
 * - Maintains backward compatibility with existing response format
 */

import { NextRequest, NextResponse } from 'next/server'
import { searchProducts } from '@/lib/data'
import type { SearchFilters, ApiResponse } from '@/lib/data/types'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { validatePaginationParams, validateSearchQuery, validateFilterParams } from '@/lib/utils'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'
import { searchApiSchema, validateInput, createValidationErrorResponse } from '@/lib/validation/schemas'
import { authenticateSearchRequest, createSearchUnauthorizedResponse, getAllowedOrigin } from '@/lib/security/auth-middleware'

/**
 * Legacy response format for backward compatibility
 */
interface LegacySearchProduct {
  id: string
  name: string
  description: string
  images: string[]
  status: string
  brand: {
    id: string
    name: string
    logo_url: string
  } | null
  category: {
    id: string
    name: string
  } | null
  minPrice: number | null
  cashbackAmount: number
  retailerOffers: Array<{
    retailer: {
      name: string
      logo_url: string
    }
    price: number
    cashback: number
    url: string
    stock_status: string
    created_at: string
    valid_until?: string
  }>
  promotion: {
    id: string
    title: string
    purchase_end_date: string
  } | null
  slug: string | null
}

interface LegacySearchResponse {
  data: LegacySearchProduct[] | null
  error: string | null
  debug?: any
}

/**
 * Debug configuration
 */
interface DebugData {
  timing: {
    start: number
    end: number
    duration: number
  }
  params: Record<string, any>
  error?: any
  environment: Record<string, any>
  request: Record<string, any>
}

/**
 * Helper function to check if debug is enabled
 */
function isDebugEnabled(): boolean {
  return process.env.NODE_ENV === 'development' || process.env.DEBUG === 'true'
}

/**
 * GET /api/search
 * 
 * Search products with filtering and sorting
 * 
 * Query Parameters:
 * - q: Search query string
 * - category: Category ID filter
 * - brand: Brand name filter
 * - sort: Sort order (relevance, price_asc, price_desc, newest, featured)
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 20, max: 50)
 */
export async function GET(request: NextRequest): Promise<NextResponse<LegacySearchResponse>> {
  // Apply rate limiting first
  const rateLimitResponse = applyRateLimit(request, rateLimits.search)
  if (rateLimitResponse) {
    return rateLimitResponse as NextResponse<LegacySearchResponse>
  }

  // Apply authentication
  const authResult = await authenticateSearchRequest(request)
  if (!authResult.success) {
    console.warn(`Search API unauthorized access attempt from ${request.headers.get('x-forwarded-for') || 'unknown'}`)
    return createSearchUnauthorizedResponse(authResult.traceId) as NextResponse<LegacySearchResponse>
  }

  // Log successful authentication
  console.log(`Search API access: ${authResult.method} authentication successful`, {
    endpoint: '/api/search',
    method: 'GET',
    traceId: authResult.traceId
  })

  const debugData: DebugData = {
    timing: {
      start: Date.now(),
      end: Date.now(),
      duration: 0,
    },
    params: {},
    environment: {
      nodeEnv: process.env.NODE_ENV || 'development',
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    },
    request: {
      url: '/api/search',
      method: 'GET',
      headers: {
        'content-type': 'application/json',
      },
    },
  }

  try {
    // Parse and validate query parameters using Zod schema
    const { searchParams } = new URL(request.url)
    const params = Object.fromEntries(searchParams)

    // Validate all parameters with Zod schema
    const validation = validateInput(searchApiSchema, params)

    if (!validation.success) {
      return NextResponse.json(
        createValidationErrorResponse(validation.error, validation.details),
        { status: 400 }
      )
    }

    // Extract validated and type-safe parameters
    const { q: sanitizedQuery, category, brand, sort, page, limit } = validation.data

    debugData.params = {
      query: sanitizedQuery,
      category,
      brand,
      sort,
      page,
      limit
    }

    // Return empty results if no search criteria provided
    if (!sanitizedQuery && !category && !brand) {
      return NextResponse.json({ data: [], error: null })
    }

    // Build search filters with validated data
    const filters: SearchFilters = {
      query: sanitizedQuery,
      category,
      brand,
      sortBy: sort as any,
    }

    // Search products using shared data layer
    const supabase = createServerSupabaseReadOnlyClient();
    const searchResult = await searchProducts(supabase, filters, page, limit)

    // Transform to legacy format for backward compatibility
    const legacyProducts: LegacySearchProduct[] = searchResult.products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      images: product.images,
      status: product.status,
      brand: product.brand ? {
        id: product.brand.id,
        name: product.brand.name,
        logo_url: product.brand.logoUrl || '',
      } : null,
      category: product.category ? {
        id: product.category.id,
        name: product.category.name,
      } : null,
      minPrice: product.retailerOffers?.length > 0
        ? Math.min(...product.retailerOffers.map(offer => offer.price))
        : null,
      cashbackAmount: product.cashbackAmount || 0,
      retailerOffers: (product.retailerOffers || []).map(offer => ({
        retailer: {
          name: offer.retailer.name,
          logo_url: offer.retailer.logoUrl || '',
        },
        price: offer.price,
        cashback: product.cashbackAmount || 0,
        url: offer.url || '',
        stock_status: offer.stockStatus || 'unknown',
        created_at: offer.createdAt,
      })),
      promotion: product.promotion ? {
        id: product.promotion.id,
        title: product.promotion.title,
        purchase_end_date: product.promotion.purchaseEndDate,
      } : null,
      slug: product.slug,
    }))

    debugData.timing.end = Date.now()
    debugData.timing.duration = debugData.timing.end - debugData.timing.start

    // Create response with proper caching headers
    const response: LegacySearchResponse = {
      data: legacyProducts,
      error: null,
      ...(isDebugEnabled() && { debug: debugData }),
    }

    const nextResponse = NextResponse.json(response)

    // Set cache headers for search results
    nextResponse.headers.set(
      'Cache-Control',
      'public, s-maxage=300, stale-while-revalidate=60'
    )

    // Add restricted CORS headers for protected API access
    const allowedOrigin = getAllowedOrigin(request)
    nextResponse.headers.set('Access-Control-Allow-Origin', allowedOrigin)
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Signature, X-Timestamp, X-Partner-ID, X-Version')
    nextResponse.headers.set('Access-Control-Allow-Credentials', 'true')

    // Add performance timing header for monitoring
    nextResponse.headers.set('X-Response-Time', `${debugData.timing.duration}ms`)

    return nextResponse

  } catch (error) {
    console.error('Error in search API route:', error)

    debugData.error = error instanceof Error ? error.message : 'An unexpected error occurred'
    debugData.timing.end = Date.now()
    debugData.timing.duration = debugData.timing.end - debugData.timing.start

    // Return standardized error response in legacy format
    const errorResponse: LegacySearchResponse = {
      data: null,
      error: 'Search failed',
      ...(isDebugEnabled() && { debug: debugData }),
    }

    return NextResponse.json(errorResponse, { status: 500 })
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(request: NextRequest): Promise<NextResponse> {
  const allowedOrigin = getAllowedOrigin(request)

  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': allowedOrigin,
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Signature, X-Timestamp, X-Partner-ID, X-Version',
      'Access-Control-Allow-Credentials': 'true',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 */
export const revalidate = 300 // Revalidate every 5 minutes

// src/app/api/catalog/route.ts
// New endpoint for PR5 - Catalog API with CORS protection and rate limiting

import { NextRequest, NextResponse } from 'next/server'
import { searchProducts, getProducts } from '@/lib/data'
import type { ApiResponse, Product, SearchFilters } from '@/lib/data/types'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { enforceCorsPolicy, applyCorsHeaders, handleCorsPreflightRequest } from '@/lib/security/cors'
import { authenticateSearchRequest, createSearchUnauthorizedResponse } from '@/lib/security/auth-middleware'

// Enhanced rate limiting for catalog endpoint (stricter than regular APIs)
const CATALOG_RATE_LIMIT = {
  maxRequests: 10, // Maximum 10 requests per second
  windowSizeInSeconds: 1, // 1 second window
  identifier: 'catalog'
}

/**
 * GET /api/catalog
 * 
 * Provides catalog access to products with enhanced security
 * - CORS protection (only allowed origins)
 * - Rate limiting (10 requests/second)
 * - Authentication required (JWT or HMAC)
 * 
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 20, max: 50)
 * - category: Filter by category
 * - featured: Filter by featured status
 * - q: Search query
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now()
  const requestId = Math.random().toString(36).substring(2, 10)

  // 1. CORS enforcement (first line of defense)
  const corsResponse = enforceCorsPolicy(request)
  if (corsResponse) {
    return corsResponse
  }

  // 2. Enhanced rate limiting for catalog
  const rateLimitResponse = applyRateLimit(request, CATALOG_RATE_LIMIT)
  if (rateLimitResponse) {
    // Add CORS headers to rate limit response
    return applyCorsHeaders(request, rateLimitResponse)
  }

  // 3. Authentication required for catalog access
  const authResult = await authenticateSearchRequest(request)
  if (!authResult.success) {
    const unauthorizedResponse = createSearchUnauthorizedResponse(authResult.traceId)
    return applyCorsHeaders(request, unauthorizedResponse)
  }

  try {
    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10))
    const limit = Math.min(50, Math.max(1, parseInt(searchParams.get('limit') || '20', 10)))
    const category = searchParams.get('category') || undefined
    const featured = searchParams.get('featured')
    const query = searchParams.get('q') || undefined

    const supabase = createServerSupabaseReadOnlyClient()

    let products: any[] = []
    let total: number = 0

    // Determine if this is a search or browse request
    if (query) {
      // Search request
      const filters: SearchFilters = {
        query,
        category,
        ...(featured === 'true' && { featured: true })
      }
      
      const searchResult = await searchProducts(supabase, filters, page, limit)
      products = searchResult.products
      total = searchResult.total
    } else {
      // Browse request - use getProducts with proper filters
      const filters = {
        category,
        isFeatured: featured === 'true' ? true : undefined,
        status: 'active' as const,
        page,
        pageSize: limit
      }
      
      const browseResult = await getProducts(supabase, filters)
      products = browseResult.product
      total = browseResult.pagination?.total || 0
    }

    // Prepare response
    const response: ApiResponse<any[]> = {
      data: products,
      error: null,
      pagination: {
        page,
        pageSize: limit,
        total: total,
        totalPages: Math.ceil(total / limit),
        hasNext: (page * limit) < total,
        hasPrev: page > 1
      }
    }

    // Log catalog access for monitoring
    console.log(JSON.stringify({
      event: 'CATALOG_ACCESS',
      timestamp: new Date().toISOString(),
      requestId,
      authMethod: authResult.method,
      productsReturned: products.length,
      page,
      limit,
      hasQuery: !!query,
      hasCategory: !!category,
      responseTime: Date.now() - startTime
    }))

    // Create response with security headers
    const nextResponse = NextResponse.json(response)
    
    // Apply CORS headers
    applyCorsHeaders(request, nextResponse, {
      methods: ['GET', 'OPTIONS'],
      credentials: true
    })
    
    // Cache headers (shorter cache for catalog due to authentication)
    nextResponse.headers.set('Cache-Control', 'private, max-age=300') // 5 minutes private cache
    
    // Performance headers
    nextResponse.headers.set('X-Request-ID', requestId)
    nextResponse.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)
    nextResponse.headers.set('X-Auth-Method', authResult.method || 'none')

    return nextResponse

  } catch (error) {
    console.error('Error in catalog API:', error, { requestId })
    
    const errorResponse: ApiResponse<null> = {
      data: null,
      error: 'Internal server error'
    }
    
    const nextResponse = NextResponse.json(errorResponse, { status: 500 })
    
    // Apply CORS headers even to error responses
    applyCorsHeaders(request, nextResponse)
    
    nextResponse.headers.set('X-Request-ID', requestId)
    nextResponse.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)
    
    return nextResponse
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(request: NextRequest): Promise<NextResponse> {
  return handleCorsPreflightRequest(request)
}

/**
 * Runtime configuration
 */
export const runtime = 'nodejs'

/**
 * Route segment config
 * No static revalidation due to authentication requirement
 */
export const dynamic = 'force-dynamic'
/**
 * Refactored Products API Route - Phase 2
 * 
 * This route has been refactored to use the shared server-side data layer
 * for improved security, performance, and maintainability.
 * 
 * Key improvements:
 * - Uses shared data layer functions instead of direct Supabase queries
 * - Eliminates public key usage in favor of secure server-side access
 * - Consistent error handling and response formats
 * - Better caching strategy
 * - Reduced code duplication
 */

import { NextRequest, NextResponse } from 'next/server'
import { getProducts } from '@/lib/data'
import type { ProductFilters, ApiResponse, TransformedProduct } from '@/lib/data/types'
import { validatePaginationParams, validateFilterParams, isValidUUID } from '@/lib/utils'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

/**
 * GET /api/products
 * 
 * Fetches products with filtering and pagination
 * 
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 10, max: 50)
 * - brandId: Filter by brand ID
 * - category_id: Filter by category ID
 * - promotion_id: Filter by promotion ID
 * - is_featured: Filter featured products (true/false)
 * - search: Search query for product names and descriptions
 * - status: Filter by status (default: active)
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<TransformedProduct[]>>> {
  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request, rateLimits.product)
  if (rateLimitResponse) {
    return rateLimitResponse as NextResponse<ApiResponse<TransformedProduct[]>>
  }

  try {
    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)

    // Validate pagination parameters
    const { page, limit } = validatePaginationParams(
      searchParams.get('page'),
      searchParams.get('limit')
    )

    // Validate and sanitize filter parameters
    const rawFilters = {
      brand_id: searchParams.get('brandId') || searchParams.get('brand_id'),
      category_id: searchParams.get('category_id'),
      promotion_id: searchParams.get('promotion_id'),
      status: searchParams.get('status'),
      search: searchParams.get('search')
    }

    const sanitizedFilters = validateFilterParams(rawFilters)

    // Build filters object with validation
    const filters: ProductFilters = {}

    // Validate UUID parameters
    if (sanitizedFilters.brand_id) {
      if (isValidUUID(sanitizedFilters.brand_id)) {
        filters.brandId = sanitizedFilters.brand_id
      } else {
        return NextResponse.json({
          data: null,
          error: 'Invalid brand_id format. Must be a valid UUID.',
        }, { status: 400 })
      }
    }

    if (sanitizedFilters.category_id) {
      if (isValidUUID(sanitizedFilters.category_id)) {
        filters.categoryId = sanitizedFilters.category_id
      } else {
        return NextResponse.json({
          data: null,
          error: 'Invalid category_id format. Must be a valid UUID.',
        }, { status: 400 })
      }
    }

    if (sanitizedFilters.promotion_id) {
      if (isValidUUID(sanitizedFilters.promotion_id)) {
        filters.promotionId = sanitizedFilters.promotion_id
      } else {
        return NextResponse.json({
          data: null,
          error: 'Invalid promotion_id format. Must be a valid UUID.',
        }, { status: 400 })
      }
    }

    // Validate status parameter
    const validStatuses = ['active', 'inactive', 'discontinued']
    if (sanitizedFilters.status) {
      if (validStatuses.includes(sanitizedFilters.status)) {
        filters.status = sanitizedFilters.status
      } else {
        return NextResponse.json({
          data: null,
          error: 'Invalid status. Must be one of: active, inactive, discontinued.',
        }, { status: 400 })
      }
    } else {
      filters.status = 'active' // Default to active
    }

    // Validate search parameter
    if (sanitizedFilters.search) {
      filters.search = sanitizedFilters.search
    }

    // Validate boolean parameters
    const isFeatured = searchParams.get('is_featured')
    if (isFeatured !== null) {
      if (isFeatured === 'true' || isFeatured === 'false') {
        filters.isFeatured = isFeatured === 'true'
      } else {
        return NextResponse.json({
          data: null,
          error: 'Invalid is_featured parameter. Must be true or false.',
        }, { status: 400 })
      }
    }
    
    // Add pagination parameters to filters
    filters.page = page
    filters.pageSize = limit

    const supabase = createServerSupabaseReadOnlyClient();
    // Fetch products using shared data layer
    const result = await getProducts(supabase, filters)

    // Transform response to match legacy API format for backward compatibility
    const response: ApiResponse<TransformedProduct[]> = {
      data: result.product,
      error: null,
      pagination: {
        page: result.pagination.page,
        pageSize: result.pagination.pageSize,
        total: result.pagination.total,
        totalPages: result.pagination.totalPages,
        hasNext: result.pagination.hasNext,
        hasPrev: result.pagination.hasPrev,
      },
    }
    
    // Create Next.js response with proper caching headers
    const nextResponse = NextResponse.json(response)
    
    // Set cache headers for optimal performance
    // - Public cache for 5 minutes
    // - Stale-while-revalidate for 30 seconds
    nextResponse.headers.set(
      'Cache-Control', 
      'public, s-maxage=300, stale-while-revalidate=30'
    )
    
    // Add CORS headers for API access
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')
    
    return nextResponse
    
  } catch (error) {
    console.error('Error in products API route:', error)
    
    // Return standardized error response
    const errorResponse: ApiResponse<TransformedProduct[]> = {
      data: null,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    }
    
    return NextResponse.json(errorResponse, { status: 500 })
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 */
export const revalidate = 300 // Revalidate every 5 minutes

// src/app/api/auth/verify/route.ts
// Simple endpoint to verify if JW<PERSON> token is valid

import { NextRequest, NextResponse } from 'next/server'
import { verifyRequestJWT } from '@/lib/security/jwt'

/**
 * GET /api/auth/verify
 * 
 * Verifies if the current request has a valid JWT token
 * Returns 200 if authenticated, 401 if not
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Verify JWT token from cookie or Authorization header
    const jwtPayload = await verifyRequestJWT(request)
    
    if (jwtPayload) {
      // Valid JWT token found
      return NextResponse.json({
        authenticated: true,
        payload: {
          sub: jwtPayload.sub,
          exp: jwtPayload.exp
        }
      })
    } else {
      // No valid JWT token
      return NextResponse.json(
        {
          authenticated: false,
          error: 'No valid JWT token found'
        },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error verifying JWT:', error)
    return NextResponse.json(
      {
        authenticated: false,
        error: 'JWT verification failed'
      },
      { status: 401 }
    )
  }
}

/**
 * Runtime configuration
 */
export const runtime = 'nodejs'
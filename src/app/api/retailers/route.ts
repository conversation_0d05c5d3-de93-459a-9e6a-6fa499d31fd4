/**
 * Retailers API Route - Phase 2C Implementation
 * 
 * This route provides access to retailer data with pagination and filtering.
 * Follows the established patterns from other API routes for consistency.
 * 
 * Key features:
 * - Uses shared server-side data layer for security and performance
 * - Supports pagination and filtering
 * - Consistent error handling and response formats
 * - Proper caching strategy
 * - Rate limiting and security headers
 */

import { NextRequest, NextResponse } from 'next/server'
import { getRetailers } from '@/lib/data'
import type { ApiResponse, TransformedRetailer, RetailerFilters } from '@/lib/data/types'
import { validatePaginationParams, validateFilterParams } from '@/lib/utils'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'
import { enforceCorsPolicy, applyCorsHeaders, handleCorsPreflightRequest } from '@/lib/security/cors'

/**
 * GET /api/retailers
 * 
 * Retrieves a paginated list of retailers with optional filtering
 * 
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 20, max: 100)
 * - featured: Filter by featured status (true/false)
 * - sponsored: Filter by sponsored status (true/false)
 * - status: Filter by status (default: active)
 * - search: Search by retailer name
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now()

  // Apply CORS enforcement first
  const corsResponse = enforceCorsPolicy(request)
  if (corsResponse) {
    return corsResponse
  }

  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request, rateLimits.retailers)
  if (rateLimitResponse) {
    return applyCorsHeaders(request, rateLimitResponse)
  }

  try {
    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)

    // Validate pagination parameters
    const { page, limit } = validatePaginationParams(
      searchParams.get('page'),
      searchParams.get('limit')
    )

    // Validate and sanitize filter parameters
    const rawFilters = {
      search: searchParams.get('search'),
      status: searchParams.get('status'),
      featured: searchParams.get('featured'),
      sponsored: searchParams.get('sponsored')
    }

    const sanitizedFilters = validateFilterParams(rawFilters)

    // Build filters object with validation
    const filters: RetailerFilters = {}

    // Validate search parameter
    if (sanitizedFilters.search) {
      filters.search = sanitizedFilters.search
    }

    // Validate status parameter
    if (sanitizedFilters.status) {
      const validStatuses = ['active', 'inactive', 'pending']
      if (validStatuses.includes(sanitizedFilters.status)) {
        filters.status = sanitizedFilters.status
      } else {
        return NextResponse.json({
          data: null,
          error: 'Invalid status. Must be one of: active, inactive, pending.',
        }, { status: 400 })
      }
    }

    // Validate boolean parameters
    const featured = searchParams.get('featured')
    if (featured !== null) {
      if (featured === 'true' || featured === 'false') {
        filters.featured = featured === 'true'
      } else {
        return NextResponse.json({
          data: null,
          error: 'Invalid featured parameter. Must be true or false.',
        }, { status: 400 })
      }
    }

    const sponsored = searchParams.get('sponsored')
    if (sponsored !== null) {
      if (sponsored === 'true' || sponsored === 'false') {
        filters.sponsored = sponsored === 'true'
      } else {
        return NextResponse.json({
          data: null,
          error: 'Invalid sponsored parameter. Must be true or false.',
        }, { status: 400 })
      }
    }

    const supabase = createServerSupabaseReadOnlyClient();
    // Fetch retailers using data layer
    const result = await getRetailers(supabase, filters, page, limit)

    // Prepare API response
    const response: ApiResponse<TransformedRetailer[]> = {
      data: result.data,
      error: null,
      pagination: result.pagination,
    }

    // Create response with proper headers
    const nextResponse = NextResponse.json(response, { status: 200 })

    // Set caching headers
    nextResponse.headers.set('Cache-Control', 'public, s-maxage=300, stale-while-revalidate=60')
    
    // Apply strict CORS headers
    applyCorsHeaders(request, nextResponse, {
      methods: ['GET', 'OPTIONS'],
      credentials: false
    })

    // Add performance headers
    const duration = Date.now() - startTime
    nextResponse.headers.set('X-Response-Time', `${duration}ms`)
    nextResponse.headers.set('X-Cache-Status', 'MISS')

    return nextResponse

  } catch (error) {
    console.error('Error in retailers API route:', error)

    const errorResponse: ApiResponse<null> = {
      data: null,
      error: error instanceof Error ? error.message : 'Internal server error',
    }

    const nextResponse = NextResponse.json(errorResponse, { status: 500 })
    
    // Apply strict CORS headers even for errors
    applyCorsHeaders(request, nextResponse, {
      methods: ['GET', 'OPTIONS'],
      credentials: false
    })

    // Add performance headers
    const duration = Date.now() - startTime
    nextResponse.headers.set('X-Response-Time', `${duration}ms`)

    return nextResponse
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(request: NextRequest): Promise<NextResponse> {
  return handleCorsPreflightRequest(request)
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 * Retailers list can be cached for a moderate duration
 */
export const revalidate = 300 // Revalidate every 5 minutes

'use client'; // Mark as client component to handle interactivity

// Import necessary libraries and components
import { motion } from 'framer-motion'; // For animations
import { Send } from 'lucide-react'; // Icons for the form
import Link from 'next/link'; // Link component for navigation
import { useState, useEffect } from 'react'; // For form state management
import { Turnstile } from '@marsidev/react-turnstile'; // Cloudflare Turnstile CAPTCHA

// Define ContactPageContent component - this is a client component
// that handles all the interactive UI elements while maintaining the design
export default function ContactPageContent() {
  // State for form feedback - controlled through React
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  // State for field-specific errors
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({});
  // State for Turnstile CAPTCHA token
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);
  // State for HTML5 validation errors (for red borders and messages)
  const [html5ValidationErrors, setHtml5ValidationErrors] = useState<Record<string, string>>({});

  // Effect to handle scrolling to error fields when fieldErrors change
  useEffect(() => {
    console.log('fieldErrors changed:', fieldErrors);
    if (Object.keys(fieldErrors).length > 0) {
      console.log('Found field errors, attempting to scroll');
      const fieldOrder = ['name', 'email', 'enquiryType', 'message'];
      for (const field of fieldOrder) {
        if (fieldErrors[field]) {
          console.log('Found error for field:', field);
          scrollToErrorField(field);
          break;
        }
      }
    }
  }, [fieldErrors]);

  // Effect to handle HTML5 validation scrolling
  useEffect(() => {
    let hasScrolled = false; // Flag to prevent multiple scrolls

    const handleInvalidField = (e: Event) => {
      const target = e.target as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
      if (target.id) {
        console.log('HTML5 validation failed for field:', target.id);
        
        // Set the HTML5 validation error for styling
        setHtml5ValidationErrors(prev => ({
          ...prev,
          [target.id]: target.validationMessage
        }));
        
        // Only scroll to the first invalid field
        if (!hasScrolled) {
          hasScrolled = true; // Prevent further scrolling
          
          // Small delay to let browser show tooltip first
          setTimeout(() => {
            scrollToErrorField(target.id);
          }, 100);
          
          // Reset flag after a short delay to allow for next form submission
          setTimeout(() => {
            hasScrolled = false;
          }, 1000);
        }
      }
    };

    const handleValidField = (e: Event) => {
      const target = e.target as HTMLElement;
      if (target.id) {
        // Clear the HTML5 validation error when field becomes valid
        setHtml5ValidationErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[target.id];
          return newErrors;
        });
      }
    };

    // Add event listeners to all form fields
    const form = document.querySelector('form');
    if (form) {
      const fields = form.querySelectorAll('input, textarea, select');
      fields.forEach(field => {
        field.addEventListener('invalid', handleInvalidField);
        field.addEventListener('input', handleValidField); // Clear error when user types
      });

      // Cleanup function
      return () => {
        fields.forEach(field => {
          field.removeEventListener('invalid', handleInvalidField);
          field.removeEventListener('input', handleValidField);
        });
      };
    }
  }, []);

  // Helper function for email validation
  const validateEmail = (email: string): string | null => {
    // Basic regex for email validation
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:\.[a-zA-Z]{2,})?$/;
    if (!emailRegex.test(email)) {
      return 'Please enter a valid email address (e.g., <EMAIL>).';
    }
    return null;
  };

  // Helper function for message validation
  const validateMessage = (message: string): string | null => {
    if (message.length < 10) {
      return 'Your message must be at least 10 characters long.';
    }
    return null;
  };

  // Utility function to scroll to error field with proper offset for sticky header
  const scrollToErrorField = (fieldId: string) => {
    const element = document.getElementById(fieldId);
    if (element) {
      console.log('Scrolling to field:', fieldId);
      const headerHeight = 120; // Account for sticky header height (80px) + extra padding
      const elementRect = element.getBoundingClientRect();
      const currentScrollY = window.scrollY;
      const targetScrollY = currentScrollY + elementRect.top - headerHeight;
      
      console.log('Element rect:', elementRect);
      console.log('Current scroll Y:', currentScrollY);
      console.log('Target scroll Y:', targetScrollY);
      
      window.scrollTo({
        top: Math.max(0, targetScrollY), // Ensure we don't scroll to negative values
        behavior: 'smooth'
      });
      
      // Focus the element after scrolling
      setTimeout(() => {
        element.focus();
      }, 300);
    } else {
      console.log('Element not found:', fieldId);
    }
  };

  // Comprehensive form validation function
  const validateForm = (formData: FormData) => {
    const errors: Record<string, string[]> = {};

    const name = formData.get('name')?.toString() || '';
    if (!name) {
      errors.name = ['Your name is required.'];
    }

    const email = formData.get('email')?.toString() || '';
    const emailError = validateEmail(email);
    if (!email || emailError) {
      errors.email = [emailError || 'Email address is required.'];
    }

    const enquiryType = formData.get('enquiryType')?.toString() || '';
    if (!enquiryType) {
      errors.enquiryType = ['Please select an inquiry type.'];
    }

    const message = formData.get('message')?.toString() || '';
    const messageError = validateMessage(message);
    if (!message || messageError) {
      errors.message = [messageError || 'Your message is required.'];
    }

    console.log('Setting field errors:', errors);
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle input change to clear errors and perform client-side validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Clear specific field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors(prev => {
        const newState = { ...prev };
        delete newState[name];
        return newState;
      });
    }

    // Perform client-side validation for email field
    if (name === 'email') {
      const inputElement = e.target as HTMLInputElement; // Cast to appropriate type for email input
      const emailError = validateEmail(value);
      if (emailError) {
        inputElement.setCustomValidity(emailError); // Set custom validity for browser tooltip
      } else {
        inputElement.setCustomValidity(''); // Clear custom validity
      }
    }

    // Clear general submit error if user starts typing after a general form error
    // and there are no specific field errors left
    if (submitError && Object.keys(fieldErrors).length === 0) {
      setSubmitError(null);
    }
  };



  // Handle form submission with two-step JWT authentication
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    console.log('handleSubmit triggered.'); // New log
    e.preventDefault(); // Preventing default form submission behavior

    // Clear all previous errors
    setSubmitError(null);
    setFieldErrors({});
    setHtml5ValidationErrors({}); // Clear HTML5 validation errors
    setSubmitSuccess(false); // Clear success message on new submission

    const formData = new FormData(e.currentTarget); // Collecting form data
    console.log('Form data collected:', Object.fromEntries(formData));

    // Check if Turnstile token is present
    if (!turnstileToken) {
      setSubmitError('Please complete the CAPTCHA verification.');
      return;
    }

    setIsSubmitting(true);

    // Perform client-side validation
    const isValid = validateForm(formData); // Store result
    console.log('Form validation result:', isValid); // New log

    if (!isValid) {
      setIsSubmitting(false);
      console.log('Client-side validation failed.');
      // Scrolling will be handled by the useEffect when fieldErrors updates
      return;
    }

    // Convert FormData to JSON and include Turnstile token
    const formDataObj: Record<string, string> = {};
    formData.forEach((value, key) => {
      formDataObj[key] = value.toString();
    });

    try {
      // STEP 1: Verify Turnstile and get JWT
      console.log('Step 1: Verifying Turnstile token...');
      const turnstileResponse = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formDataObj,
          'cf-turnstile-response': turnstileToken
        }),
      });

      const turnstileData = await turnstileResponse.json();

      if (!turnstileResponse.ok) {
        console.error('Turnstile verification failed:', turnstileData);
        setSubmitError(turnstileData.message || turnstileData.error || 'Security verification failed. Please try again.');
        return;
      }

      console.log('Step 1 completed: Turnstile verified, JWT received');

      // STEP 2: Submit form with JWT (cookies are automatically included)
      console.log('Step 2: Submitting form with JWT...');

      // Create form data WITHOUT Turnstile token for Step 2
      const formDataWithoutTurnstile = { ...formDataObj };
      delete formDataWithoutTurnstile['cf-turnstile-response'];

      const formResponse = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies (JWT)
        body: JSON.stringify(formDataWithoutTurnstile), // No Turnstile token this time
      });

      if (!formResponse.ok) {
        // Handle error responses for Step 2
        const errorData = await formResponse.json();
        console.error('Step 2 failed:', formResponse.status, errorData);

        if (formResponse.status === 429) {
          setSubmitError('Too many requests. Please wait a moment and try again.');
        } else if (formResponse.status === 400 && errorData.details) {
          // Handle validation errors from Zod
          if (errorData.details.fieldErrors) {
            setFieldErrors(errorData.details.fieldErrors);
            // Scrolling will be handled by the useEffect when fieldErrors updates
          }
          if (errorData.details.formErrors && errorData.details.formErrors.length > 0) {
            setSubmitError(errorData.details.formErrors[0]); // Display first form-level error
          } else if (errorData.message) {
            setSubmitError(errorData.message); // Fallback to general message
          } else {
            setSubmitError('Validation failed. Please check your input.');
          }
        } else {
          // Handle other non-200 responses
          setSubmitError(errorData.message || errorData.error || 'There was a problem submitting your form. Please try again.');
        }
        return; // Stop execution if there's an error
      }

      // Parse successful response
      const formResponseData = await formResponse.json();

      // Handle successful submission
      setSubmitSuccess(true);
      console.log('Step 2 completed: Form submitted successfully, email sent!', formResponseData);

      // Optional: redirect to thank you page
      // Delayed redirect to show success message
      setTimeout(() => {
        const params = new URLSearchParams({
          name: formDataObj.name,
          type: formDataObj.enquiryType,
        });
        window.location.href = `/contact/thank-you?${params.toString()}`;
      }, 1000);

    } catch (error) {
      console.error('Error submitting form:', error);
      // Set error state to display to user
      setSubmitError('There was a problem submitting your form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  

  return (
    // Main container for the contact page
    <div className="relative flex flex-col min-h-screen">
      {/* Hero section with animation */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary/10 via-secondary/10 to-background py-20"
      > 
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="max-w-3xl"
          >
            {/* Page heading - important for SEO hierarchy */}
            <h1 className="text-4xl font-bold text-primary mb-6">Contact Us</h1>
            <p className="text-lg text-foreground/80"> 
              We'd love to hear from you! Our team is always happy to help with any questions or feedback. 
            </p> 
          </motion.div>
        </div>
      </motion.section>

      {/* Form container section */}
      <div className="container py-16">
        <div className="max-w-3xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-10"
          >
            <p className="text-foreground/80 mb-6">
              Whether you need help with finding the right rebates, have a business inquiry, or just want to say hello,
              we're here for you. We aim to respond to all inquiries within 24 hours during business days.
            </p>
          </motion.div>

          {/* Form with animation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-sm border border-primary/10 p-8"
          >
            {/* Show success message if form submitted successfully */}
            {submitSuccess && (
              <div className="bg-green-50 border border-green-200 text-green-700 p-4 rounded-lg mb-6">
                Thank you for your message! We'll get back to you shortly.
              </div>
            )}
            
            {/* Show error message if form submission failed */}
            {submitError && (
              <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-6">
                {submitError}
              </div>
            )}

            {/* Contact form with client-side handling */}
            <form 
              // Using both action (for non-JS fallback) and onSubmit for enhanced experience
              action="/api/contact" 
              method="POST"
              onSubmit={handleSubmit}
              className="space-y-6"
            >
              {/* Name input field */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-foreground mb-2" aria-label="Your Name">
                  Your Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  className={`w-full px-4 py-2 rounded-lg border ${fieldErrors.name || html5ValidationErrors.name ? 'border-red-500' : 'border-foreground/20'} focus:outline-none focus:ring-2 focus:ring-primary/20`}
                  onChange={handleInputChange}
                />
                {fieldErrors.name && (
                  <p className="text-red-500 text-sm mt-1">{fieldErrors.name[0]}</p>
                )}
                {html5ValidationErrors.name && !fieldErrors.name && (
                  <p className="text-red-500 text-sm mt-1">{html5ValidationErrors.name}</p>
                )}
              </div> 

              {/* Email input field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2" aria-label="Email Address">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  className={`w-full px-4 py-2 rounded-lg border ${fieldErrors.email || html5ValidationErrors.email ? 'border-red-500' : 'border-foreground/20'} focus:outline-none focus:ring-2 focus:ring-primary/20`}
                  onChange={handleInputChange}
                />
                {fieldErrors.email && (
                  <p className="text-red-500 text-sm mt-1">{fieldErrors.email[0]}</p>
                )}
                {html5ValidationErrors.email && !fieldErrors.email && (
                  <p className="text-red-500 text-sm mt-1">{html5ValidationErrors.email}</p>
                )}
              </div> 

              {/* Phone input field (optional) */}
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-foreground mb-2" aria-label="Phone Number">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  className={`w-full px-4 py-2 rounded-lg border ${fieldErrors.phone ? 'border-red-500' : 'border-foreground/20'} focus:outline-none focus:ring-2 focus:ring-primary/20`}
                  onChange={handleInputChange}
                />
                {fieldErrors.phone && (
                  <p className="text-red-500 text-sm mt-1">{fieldErrors.phone[0]}</p>
                )}
              </div> 

              {/* Inquiry type selection */}
              <div>
                <label htmlFor="enquiryType" className="block text-sm font-medium text-foreground mb-2" aria-label="Inquiry Type">
                  Inquiry Type *
                </label>
                <select
                  id="enquiryType"
                  name="enquiryType"
                  required
                  className={`w-full px-4 py-2 rounded-lg border ${fieldErrors.enquiryType || html5ValidationErrors.enquiryType ? 'border-red-500' : 'border-foreground/20'} focus:outline-none focus:ring-2 focus:ring-primary/20`}
                  onChange={handleInputChange}
                >
                  <option value="" disabled selected>Please select an inquiry type</option>
                  <option value="support" aria-label="Customer Support">Customer Support</option>
                  <option value="partnership" aria-label="Business Enquiry">Business Enquiry</option>
                  <option value="general" aria-label="General Enquiry">General Enquiry</option>
                  <option value="feedback" aria-label="Feedback">Feedback</option>
                  <option value="other" aria-label="Other">Other</option>
                </select>
                {fieldErrors.enquiryType && (
                  <p className="text-red-500 text-sm mt-1">{fieldErrors.enquiryType[0]}</p>
                )}
                {html5ValidationErrors.enquiryType && !fieldErrors.enquiryType && (
                  <p className="text-red-500 text-sm mt-1">{html5ValidationErrors.enquiryType}</p>
                )}
              </div> 

              {/* Message input field */}
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-foreground mb-2" aria-label="Your Message">
                  Your Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  required
                  rows={5}
                  minLength={10}
                  className={`w-full px-4 py-2 rounded-lg border ${fieldErrors.message || html5ValidationErrors.message ? 'border-red-500' : 'border-foreground/20'} focus:outline-none focus:ring-2 focus:ring-primary/20`}
                  onChange={handleInputChange}
                />
                {fieldErrors.message && (
                  <p className="text-red-500 text-sm mt-1">{fieldErrors.message[0]}</p>
                )}
                {html5ValidationErrors.message && !fieldErrors.message && (
                  <p className="text-red-500 text-sm mt-1">{html5ValidationErrors.message}</p>
                )}
              </div> 

              {/* Cloudflare Turnstile CAPTCHA */}
              <div className="pt-2">
                <label className="block text-sm font-medium text-foreground mb-2">
                  Security Verification *
                </label>
                <div className="mb-4">
                  {/* Turnstile CAPTCHA - invisible in test mode */}
                  <Turnstile
                    siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || '1x00000000000000000000AA'}
                    onSuccess={(token) => {
                      setTurnstileToken(token);
                      setSubmitError(null);
                    }}
                    onError={() => {
                      setTurnstileToken(null);
                      setSubmitError('Security verification failed. Please try again.');
                    }}
                    onExpire={() => {
                      setTurnstileToken(null);
                      setSubmitError('Security verification expired. Please verify again.');
                    }}
                  />
                  
                  {/* Status indicator for user feedback */}
                  <div className="mt-2">
                    {turnstileToken ? (
                      <p className="text-sm text-green-600 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Security verification completed
                      </p>
                    ) : (
                      <p className="text-sm text-gray-500">
                        Security verification in progress...
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Terms and submit button section */}
              <div className="pt-2">
                <p className="text-sm text-foreground/60 mb-6" aria-label="Terms and Conditions">
                  By submitting this form, you agree to our{' '}
                  <Link href="/terms" className="text-primary hover:underline" aria-label="Terms & Conditions">
                    Terms & Conditions
                  </Link>
                  .
                </p>
                {/* Submit button with icon - with loading state */}
                <button
                  type="submit"
                  disabled={isSubmitting || !turnstileToken}
                  className="relative inline-flex items-center justify-center gap-2 bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-all w-full sm:w-auto disabled:opacity-70"
                >
                  <span className="flex items-center gap-2" aria-label="Send Message">
                    {isSubmitting ? 'Sending...' : 'Send Message'} <Send className="h-4 w-4" />
                  </span>
                </button>
              </div>
            </form> 
          </motion.div> 
        </div> 
      </div> 
    </div>
  );
}



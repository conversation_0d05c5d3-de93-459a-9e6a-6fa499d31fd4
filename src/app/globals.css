@import "tailwindcss";

@theme {
  --color-background: hsl(195 98% 93%);
  --color-foreground: hsl(0 0% 0%);
  --color-primary: hsl(212 35% 37%);
  --color-primary-foreground: hsl(0 0% 100%);
  --color-secondary: hsl(201 45% 72%);
  --color-secondary-foreground: hsl(0 0% 0%);
  --color-accent: hsl(12 84% 62%);
  --color-accent-foreground: hsl(0 0% 100%);
  --color-muted: hsl(0 0% 96.1%);
  --color-muted-foreground: hsl(0 0% 45.1%);
  --color-popover: hsl(0 0% 100%);
  --color-popover-foreground: hsl(0 0% 3.9%);
  --color-card: hsl(0 0% 100%);
  --color-card-foreground: hsl(0 0% 3.9%);
  --color-destructive: hsl(0 84.2% 60.2%);
  --color-destructive-foreground: hsl(0 0% 98%);
  --color-border: hsl(0 0% 98%);
  --color-input: hsl(0 0% 89.8%);
  --color-ring: hsl(0 0% 100% / 0.1);
  
  /* Override Tailwind's default border color */
  --default-border-color: hsl(0 0% 98%);
}

/* Fix for Tailwind 4.x container configuration not working */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 2rem;
  padding-right: 2rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
  }
}

body {
  /* Removed @apply bg-background text-foreground; to remove explicit background */
}

/* Override Tailwind's default border color globally */
.border,
.border-t,
.border-b,
.border-l,
.border-r {
  border-color: var(--color-border);
}

.animate-in {
  animation: var(--animate-fade-in);
}

.card {
  @apply rounded-lg bg-card shadow-sm transition-all hover:shadow-md;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

